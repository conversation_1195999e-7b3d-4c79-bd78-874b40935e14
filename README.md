# 脚手架应用使用说明

<p style="color:red">linux下生成的项目的父pom文件很丑，需要手动优化一下</p>

<p style="color:red">所有的依赖统一管理在 parent 项目中，方便维护</p>

<p style="color: red">重要</p>
使用前先前往 [阿里云nexus仓库](https://packages.aliyun.com/repos/2130733-snapshot-d2CMZk/guide) 按照步骤配置阿里云 maven 的帐号和密码，否则会找不到依赖，直接复制即可，不用修改 server 的 id

maven和脚手架工程使用方法详见:https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8ajrLl1wSd9wX9wEWgN7R35y?doc_type=wiki_doc#
「Maven使用文档」

## 使用步骤

1. 将 ${artifactId}-starter 模块中的 ScaffoldApplication 改名, 使其与项目用途对应
2. 注意${artifactId}-starter 模块中启动类上注解的包扫描, 将包扫描修改成与对应 artifactId 一致(一般不需要修改)
3. 修改 ${artifactId}-starter 模块中的 bootstrap.yaml 内的 **server.servlet.context-path** 和 **spring.application.name** 使其与项目用途对应
4. 修改 ${artifactId}-starter 模块中的 bootstrap.yaml 内的 **spring.cloud.nacos**配置和注册相关配置， 使其与项目用途对应
5. 修改 ${artifactId}-service common.error.ScaffoldErrorCode 的文件名和错误码第3、4位， 使其与项目用途对应
6. 修改 ${artifactId}-service common.mq.ScaffoldMessageType  的文件名和发送 mq 消息的 topic 和tag， 使其与项目用途对应
7. 修改 Dockerfile 的第12行，使 cp 目录与artifactId对应
8. 全局搜索 scaffold 关键词，将出现项目名的地方替换成项目相关的内容 
9. 删除脚手架中没用的示例代码(可选)
10. 脚手架内置了各种依赖和配置文件, 生成以后目可以直接启动
   - 访问 http://localhost:8080/${server.servlet.context-path}/swagger-ui/index.html 可以获取 swagger 文档
   - 访问 http://localhost:8080/${server.servlet.context-path}v3/api-docs 可以获取 openapi 文档
   - 访问 http://localhost:8080/${server.servlet.context-path}/user/{userId} 可以查询用户。 e.g. 在没有配置server.servlet.context-path时, 访问 http://localhost:8080/user/15978
   - 连续访问 http://localhost:8080/${server.servlet.context-path}/user/demo/3 可以体验熔断。 e.g. 在没有配置server.servlet.context-path时, 访问 http://localhost:8080/user/demo/3

脚手架大部分功能默认关闭，通过修改 application-local.yaml 可以开启关闭的功能

## 常见问题

## 如何生成接口文档

脚手架内置了 springdoc 依赖, 详见 https://springdoc.org/v1/#Introduction

项目继承了 javadoc 扫描, 不需要使用 swagger 注解, 只需要在接口、参数和返回体上使用标准的 javadoc 文档, 就能自动生成对应的 swagger 和 openapi 
文档  

注意: 类上仍需要使用 @Tag 注解, 指定此类的标签, 否则会根据类名生成标签  

adapter 包内提供了一些示例代码, 可以参照 swagger 文档和示例代码熟悉写法

### java: 警告: 源发行版 17 需要目标发行版 17

问题描述: java: 警告: 源发行版 17 需要目标发行版 17
出现原因: 由于项目指定了 java.version = 17, 使用17版本以下的jdk无法编译项目
解决方法: file -> project structure -> project -> sdk 选择17或17版本以上的jdk,如果没有需自行下载

## 项目结构说明

```text
├─adapter                            -- adapter模块, 负责把server的数据转换成各端需要的数据的格式                    
│  ├─web                               -- 处理和转换页面请求的controller              
│  ├─app                               -- 处理和转换app页面请求的controller
│  ├─miniapp                          -- 处理和转换小程序页面请求的controller 
├─client                             -- client模块, 负责提供服务间调用的约束, 供其他服务引用
│  ├─api                               -- 为其他服务调用提供的api    
│  ├─dto                               -- 为其他服务调用提供的dto
├─schedule                           -- 定时任务模块, 定时任务相关写这里
├─mq                                 -- mq模块, mq的发送和监听写这里
├─service                            -- service模块，处理业务的核心模块
│  ├─biz                               -- 业务相关的包
│  │  ├─业务A                            -- 一级业务，子业务不需要继续划分
│  │  │  ├─config                             -- 业务相关配置
│  │  │  ├─constant                           -- 业务相关常量
│  │  │  ├─es                                 -- 业务相关es
│  │  │  ├─mybatis                            -- 业务相关mybatis文件
│  │  │  │  ├─mapper                             -- mapper
│  │  │  │  ├─entity                             -- mapper对应的实体
│  │  │  ├─pojo                               -- 业务相关pojo类
│  │  │  ├─service                            -- 业务相关service
│  │  ├─业务B                            -- 一级业务，子业务不需要继续划分
│  │  │  ├─config                             -- 业务相关配置
│  │  │  ├─constant                           -- 业务相关常量
│  │  │  ├─es                                 -- 业务相关es
│  │  │  ├─mybatis                            -- 业务相关mybatis文件
│  │  │  │  ├─mapper                             -- mapper
│  │  │  │  ├─entity                             -- mapper对应的实体
│  │  │  ├─pojo                               -- 业务相关pojo类
│  │  │  ├─service                            -- 业务相关service
│  ├─common                              -- 通用工具
│  │  ├─aop                                   -- 切面包
│  │  ├─config                                -- 通用配置(web、redis、bean配置等)
│  │  ├─error                                 -- 业务异常
│  │  ├─filter                                -- 过滤器
│  │  ├─interceptor                           -- 拦截器
│  ├──├─mq                                    -- mq 消息发送的类型, 继承自 MessageTypeBase
│  │  ├─util                                  -- 通用工具
│  ├─remote                              -- 调用其他服务的包, 可以包含一些熔断、限流逻辑
│  ├─mq                                  -- mq相关, 可以用来存放 LocalTransactionHandler
│  ├─rpc                                 -- 远程调用的实现包
├─starter                                -- 启动模块，专门用来存放配置文件和启动类
```