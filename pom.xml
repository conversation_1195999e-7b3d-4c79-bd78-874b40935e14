<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-dependencies-spring-cloud</artifactId>
        <version>2021.0.9.0-********-1.1.0-SNAPSHOT</version>
    </parent>
    <artifactId>tyt-outer-export-service</artifactId>
    <groupId>com.teyuntong</groupId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tyt-outer-export-service</name>
    <description>tyt 脚手架项目</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <tyt-infra-common-web.version>2.1.3-SNAPSHOT</tyt-infra-common-web.version>
        <tyt-infra-common-cache.version>2.1.3-SNAPSHOT</tyt-infra-common-cache.version>
        <tyt-infra-common-redis.version>2.1.3-SNAPSHOT</tyt-infra-common-redis.version>
        <tyt-infra-common-circuit-breaker.version>2.1.3-SNAPSHOT</tyt-infra-common-circuit-breaker.version>
        <tyt-infra-common-rocket-mq.version>2.1.3-SNAPSHOT</tyt-infra-common-rocket-mq.version>
        <tyt-infra-common-retrofit.version>2.1.3-SNAPSHOT</tyt-infra-common-retrofit.version>
        <tyt-infra-common-xxl-job.version>2.1.3-SNAPSHOT</tyt-infra-common-xxl-job.version>
        <tyt-infra-common-core.version>2.1.3-SNAPSHOT</tyt-infra-common-core.version>
        <tyt-infra-common-logging.version>2.1.3-SNAPSHOT</tyt-infra-common-logging.version>
        <tyt-infra-common-springdoc.version>2.1.3-SNAPSHOT</tyt-infra-common-springdoc.version>
        <tyt-infra-common-definition.version>2.1.3-SNAPSHOT</tyt-infra-common-definition.version>
        <!-- ==================== sonar ==================== -->
        <tyt-p3c-pmd.version>1.0.0</tyt-p3c-pmd.version>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.projectName>${project.artifactId}</sonar.projectName>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.projectVersion>1.0</sonar.projectVersion>
        <sonar.inclusions>**/com/teyuntong/**/*.java,**/com/teyuntong/**/*.kt</sonar.inclusions>
        <sonar.java.binaries>target</sonar.java.binaries>
        <sonar.host.url>http://************:9000</sonar.host.url>
        <sonar.login>****************************************</sonar.login>

        <!-- JaCoCo Configuration for Sonar -->
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml,${project.basedir}/target/site/jacoco-aggregate/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <sonar.language>java</sonar.language>

        <!-- ==================== gateway-client ==================== -->
        <gateway-client.version>2.1.18-SNAPSHOT</gateway-client.version>
        <tyt-common.version>0.0.4-SNAPSHOT</tyt-common.version>
        <dynamic-datasource.version>3.5.0</dynamic-datasource.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-logging-spring-boot-starter</artifactId>
            <version>${tyt-infra-common-logging.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- ECC 加密器  -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.64</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic-datasource.version}</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-web-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-web.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-cache-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-redis-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-circuit-breaker-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-circuit-breaker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-rocket-mq-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-rocket-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-retrofit-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-xxl-job-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-springdoc-spring-boot-starter</artifactId>
                <version>${tyt-infra-common-springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-core</artifactId>
                <version>${tyt-infra-common-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-definition</artifactId>
                <version>${tyt-infra-common-definition.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wlqq.wallet.gateway</groupId>
                <artifactId>gateway-client</artifactId>
                <version>${gateway-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-inner-export-service-client</artifactId>
                <version>1.2.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-user-service-client</artifactId>
                <version>1.3.0-SNAPSHOT</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.security/spring-security-rsa -->
<!--            <dependency>-->
<!--                <groupId>org.springframework.security</groupId>-->
<!--                <artifactId>spring-security-rsa</artifactId>-->
<!--                <version>1.1.1</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>

            <!-- maven 编译插件 集成了 lombok 和 mapstruct, 不要乱改 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 脚手架插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
            </plugin>
            <!-- 一键修改版本号插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>

            <!-- sonar插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>

            <!-- JaCoCo plugin for code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
    <modules>
        <module>tyt-outer-export-service-adapter</module>
        <module>tyt-outer-export-service-client</module>
        <module>tyt-outer-export-service-service</module>
        <module>tyt-outer-export-service-starter</module>
        <module>tyt-outer-export-service-mybatis-generator</module>
    </modules>
</project>
