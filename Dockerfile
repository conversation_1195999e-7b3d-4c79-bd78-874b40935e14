FROM registry.cn-beijing.aliyuncs.com/teyuntong/java:17.0.10

ARG WORK_HOME=/app

ENV VM_OPTIONS=""
ENV PROGRAM_ARGS=""

WORKDIR $WORK_HOME

RUN sed -i -e "s|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g" /etc/yum.repos.d/CentOS-* && sed -i -e "s|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g" /etc/yum.repos.d/CentOS-*
RUN yum -y install freetype && yum -y install fontconfig

RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

COPY ./tyt-outer-export-service-starter/target/*.jar $WORK_HOME/application.jar

ENTRYPOINT ["/bin/sh","-c","java -jar  ${VM_OPTIONS} ./application.jar ${PROGRAM_ARGS}"]
