package com.teyuntong.outer.export.service.mybatis.generator.mbp;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.builder.Entity;
import com.baomidou.mybatisplus.generator.config.builder.Mapper;
import com.baomidou.mybatisplus.generator.config.builder.Service;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.query.SQLQuery;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.function.UnaryOperator;

@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(MbpGeneratorProperties.class)
public class MyBatisPlusGenerator implements ApplicationRunner {

    private final MbpGeneratorProperties mbpGeneratorProperties;

    /**
     * 读取控制台输入内容
     */
    private final Scanner inputScanner = new Scanner(System.in);

    private final UnaryOperator<String> scannerNext = message -> {
        System.out.println(message); //NOSONAR
        String nextLine = inputScanner.nextLine();
        if (StringUtils.isBlank(nextLine)) {
            // 如果输入空行继续等待
            return inputScanner.next();
        }
        return nextLine;
    };

    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("重要: 使用前先创建和修改自己的 application-mbp-generate-local.yaml 文件"); //NOSONAR
        Map<String, MbpGeneratorProperties.DataSourceProperties> dataSources = mbpGeneratorProperties.getDataSources();

        Set<String> keySet = dataSources.keySet();

        String env;

        if (keySet.isEmpty()) {
            throw new MbpGeneratorException("未配置数据源, 请前往配置文件配置");
        } else if (keySet.size() == 1) {
            env = keySet.stream().findFirst().orElseThrow();
        } else {
            env = scannerNext.apply("请选择数据库连接信息, 当前配置的连接有: " + keySet);
        }

        System.out.printf("目前使用的数据源为: %s%n", env); //NOSONAR

        MbpGeneratorProperties.DataSourceProperties dataSourceProperties = dataSources.get(env);
        if (dataSourceProperties == null) {
            throw new MbpGeneratorException(String.format("未配置名为: %s 的数据库连接", env));
        }

        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig.Builder(dataSourceProperties.getUrl(),
                dataSourceProperties.getUsername(), dataSourceProperties.getPassword()).databaseQueryClass(SQLQuery.class); // 设置SQL查询方式，默认的是元数据查询方式

        FastAutoGenerator autoGenerator = FastAutoGenerator.create(dataSourceConfig);
        // 全局配置
        configGlobal(autoGenerator);
        // 包配置
        configPackage(autoGenerator);
        // 模板配置
        configTemplate(autoGenerator);
        // 策略配置
        configStrategy(autoGenerator);
        // 模板引擎配置，默认 Velocity 可选模板引擎 Beetl 或 Freemarker
        //.templateEngine(new BeetlTemplateEngine())
        // 使用 Freemarker引擎，因为Velocity自定义模板会报找不到模板的异常
        autoGenerator.templateEngine(new FreemarkerTemplateEngine());

        autoGenerator.execute();
    }

    private void configStrategy(FastAutoGenerator autoGenerator) {
        autoGenerator.strategyConfig(builder -> {
            MbpGeneratorProperties.StrategyConfig strategyConfig = mbpGeneratorProperties.getStrategyConfig();
            String tytPrefixRegex = "(.*)(?i)tyt(.*)";
            if (!strategyConfig.isEnableSqlFilter()) {
                builder.disableSqlFilter();
            }

            builder.addInclude(strategyConfig.getInclude().toArray(new String[]{}));
            Entity.Builder entityBuilder = builder.entityBuilder();
            if (strategyConfig.isEntityOverride()) {
                entityBuilder.enableFileOverride();
            }
            entityBuilder.enableLombok().disableSerialVersionUID().convertFileName(it -> RegExUtils.replaceFirst(it,
                    tytPrefixRegex, "$1$2") + "DO");

            Service.Builder serviceBuilder = builder.serviceBuilder();
            if (strategyConfig.isServiceOverride()) {
                serviceBuilder.enableFileOverride();
            }

            serviceBuilder.convertServiceFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2") +
                    "Service").convertServiceImplFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex,
                    "$1$2") + "ServiceImpl");

            Mapper.Builder mapperBuilder = builder.mapperBuilder();
            if (strategyConfig.isMapperOverride()) {
                mapperBuilder.enableFileOverride();
            }
            mapperBuilder.mapperAnnotation(org.apache.ibatis.annotations.Mapper.class)
                    .enableBaseResultMap()
                    .enableBaseColumnList()
                    .formatMapperFileName("%sDao")
                    .formatXmlFileName("%sXml")
                    .convertMapperFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2") + "Mapper")
                    .convertXmlFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2") + "Mapper")
                    .build();
        });
    }

    private void configTemplate(FastAutoGenerator autoGenerator) {
        autoGenerator.templateConfig(builder -> {
            MbpGeneratorProperties.TemplateConfig templateConfig = mbpGeneratorProperties.getTemplateConfig();

            builder.disable(templateConfig.getDisableTypes().toArray(new TemplateType[]{}));
            if (StringUtils.isNotBlank(templateConfig.getEntity())) {
                builder.entity(templateConfig.getEntity());
            }
            if (StringUtils.isNotBlank(templateConfig.getEntity())) {
                builder.entityKt(templateConfig.getEntityKt());
            }
            if (StringUtils.isNotBlank(templateConfig.getController())) {
                builder.controller(templateConfig.getController());
            }
            if (StringUtils.isNotBlank(templateConfig.getMapper())) {
                builder.mapper(templateConfig.getMapper());
            }
            if (StringUtils.isNotBlank(templateConfig.getXml())) {
                builder.xml(templateConfig.getXml());
            }
            if (StringUtils.isNotBlank(templateConfig.getService())) {
                builder.service(templateConfig.getService());
            }
            if (StringUtils.isNotBlank(templateConfig.getServiceImpl())) {
                builder.serviceImpl(templateConfig.getServiceImpl());
            }
        });
    }

    private void configPackage(FastAutoGenerator autoGenerator) {
        autoGenerator.packageConfig(builder -> {
            MbpGeneratorProperties.PackageConfig packageConfig = mbpGeneratorProperties.getPackageConfig();

            builder.parent(packageConfig.getParent()).moduleName(packageConfig.getModuleName()).entity(packageConfig.getEntity()).service(packageConfig.getService()).serviceImpl(packageConfig.getServiceImpl()).mapper(packageConfig.getMapper()).xml(packageConfig.getXml()).controller(packageConfig.getController());
        });
    }

    private void configGlobal(FastAutoGenerator autoGenerator) {
        autoGenerator.globalConfig(builder -> {
            MbpGeneratorProperties.GlobalConfig globalConfig = mbpGeneratorProperties.getGlobalConfig();

            if (!globalConfig.isOpen()) {
                builder.disableOpenDir();
            }
            if (globalConfig.isKotlin()) {
                builder.enableKotlin();
            }
            if (globalConfig.isSwagger()) {
                builder.enableSwagger();
            }
            if (globalConfig.isSpringdoc()) {
                builder.enableSpringdoc();
            }
            if (!globalConfig.isServiceInterface()) {
                builder.disableServiceInterface();
            }

            String prefix = StringUtils.equals("relative", globalConfig.getOutputDirType()) ? System.getProperty(
                    "user.dir") : "";

            String outputDir = globalConfig.getOutputDir();
            if (!System.getProperty("os.name").toLowerCase().contains("windows") && !StringUtils.startsWith(
                    "outputDir", "/")) {
                outputDir = "/" + outputDir;
            }
            builder.outputDir(prefix + outputDir).author(globalConfig.getAuthor()).dateType(globalConfig.getDateType());
        });
    }

    static class MbpGeneratorException extends RuntimeException {
        public MbpGeneratorException(String message) {
            super(message);
        }
    }
}