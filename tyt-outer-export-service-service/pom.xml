<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-outer-export-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>tyt-outer-export-service-service</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <volc-sdk-java.version>1.0.140</volc-sdk-java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-outer-export-service-client</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-web-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-cache-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-circuitbreaker-resilience4j</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-circuit-breaker-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-rocket-mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-retrofit-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wlqq.wallet.gateway</groupId>
            <artifactId>gateway-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ========== e签宝 ========== -->
        <!-- esign base -->

        <dependency>
            <groupId>com.3rd.esign</groupId>
            <artifactId>tech-sdk</artifactId>
            <version>2.1.60</version>
        </dependency>

        <dependency>
            <groupId>com.3rd.esign</groupId>
            <artifactId>tgtext</artifactId>
            <version>3.3.64.2160</version>
        </dependency>

        <dependency>
            <groupId>com.3rd.esign</groupId>
            <artifactId>utils</artifactId>
            <version>3.0.6.2160</version>
        </dependency>

        <dependency>
            <groupId>com.3rd.esign</groupId>
            <artifactId>smUtil</artifactId>
            <version>1.3.3.2160</version>
        </dependency>

        <!-- esign deps -->
        <!-- https://mvnrepository.com/artifact/commons-lang/commons-lang -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpmime -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>

        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-inner-export-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-user-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.78.1</version>
        </dependency>
        <!-- 花瓣家结巴分词器 -->
        <dependency>
            <groupId>com.huaban</groupId>
            <artifactId>jieba-analysis</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
            <version>${volc-sdk-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>
</project>
