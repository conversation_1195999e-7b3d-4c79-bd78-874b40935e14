package com.teyuntong.outer.export.service.service.biz.ocr.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.*;
import com.teyuntong.outer.export.service.service.biz.ocr.constant.BaiduOcrConstant;
import com.teyuntong.outer.export.service.service.biz.ocr.constant.CommonRedisConstant;
import com.teyuntong.outer.export.service.service.biz.ocr.enums.ImgSideEnum;
import com.teyuntong.outer.export.service.service.biz.ocr.service.BaiduOcrService;
import com.teyuntong.outer.export.service.service.biz.ocr.util.DateUtil;
import com.teyuntong.outer.export.service.service.biz.ocr.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 百度ocr 识别
 *
 * <AUTHOR>
 * @date 2023/4/24 16:09
 */
@Slf4j
@Service
public class BaiduOcrServiceImpl implements BaiduOcrService {


    private static final String LONG_TIME_STR = "长期";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getJsonString(JSONObject jsonObject, String... keyArray) {

        StringBuilder jsonPathBuilder = new StringBuilder();

        int i = 0;
        for (String oneKey : keyArray) {
            if (i > 0) {
                jsonPathBuilder.append(".");
            }

            jsonPathBuilder.append(oneKey);

            i++;
        }

        String jsonPath = jsonPathBuilder.toString();

        Object jsonValue = JSONPath.eval(jsonObject, jsonPath);

        String result = null;
        if (jsonValue != null) {
            result = jsonValue.toString();
        }
        return result;
    }

    /**
     * 转日期忽略异常
     *
     * @param dateText
     * @param formatType
     * @return
     */
    private Date parseDate(String dateText, String formatType) {
        if (StringUtils.isBlank(dateText)) {
            return null;
        }
        Date resultDate = null;
        try {
            resultDate = DateUtil.parseDate(dateText, formatType);
        } catch (Exception e) {
            log.error("parseDate_error : " + dateText, e);
        }
        return resultDate;
    }

    @Override
    public String getAccessToken() {

        String accessToken = stringRedisTemplate.opsForValue().get(CommonRedisConstant.ocr_access_token_key);
        if (StringUtils.isBlank(accessToken)) {

            String apiUrl = BaiduOcrConstant.access_token;

            Map<String, String> paramMap = new TreeMap<>();
            paramMap.put("grant_type", "client_credentials");
            paramMap.put("client_id", BaiduOcrConstant.API_KEY);
            paramMap.put("client_secret", BaiduOcrConstant.SECRET_KEY);

            URI uri = HttpClientUtil.createUri(apiUrl, paramMap);

            HttpPost httpPost = new HttpPost(uri);

            httpPost.addHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());

            CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpPost);

            JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

            accessToken = jsonObject.getString("access_token");

            //10小时
            stringRedisTemplate.opsForValue().set(CommonRedisConstant.ocr_access_token_key, accessToken, 10 * 60 * 60, TimeUnit.SECONDS);
        }

        return accessToken;
    }

    /**
     * 请求ocr接口
     *
     * @param apiUrl
     * @param paramMap
     */
    private JSONObject requestOcrApi(String apiUrl, Map<String, String> paramMap) {

        String accessToken = this.getAccessToken();

        Map<String, String> urlMap = new TreeMap<>();
        urlMap.put("access_token", accessToken);

        URI uri = HttpClientUtil.createUri(apiUrl, urlMap);

        HttpPost httpPost = new HttpPost(uri);
        httpPost.addHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());

        if (MapUtils.isNotEmpty(paramMap)) {

            List<NameValuePair> paramList = new ArrayList<>();
            for (Map.Entry<String, String> oneEntry : paramMap.entrySet()) {
                String key = oneEntry.getKey();
                String value = oneEntry.getValue();

                paramList.add(new BasicNameValuePair(key, value));
            }
            try {
                httpPost.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("", e);
                throw TytException.createException(e);
            }
        }

        CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpPost);

        JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

        return jsonObject;
    }

    @Override
    public OcrIdCardFrontVo idCardFrontOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("id_card_side", ImgSideEnum.front.name());
        optMap.put("detect_photo", "false");
        optMap.put("detect_card", "false");

        JSONObject idCardJson = this.requestOcrApi(BaiduOcrConstant.idcard, optMap);

        String ocrJsonText = idCardJson.toJSONString();

        Long logId = idCardJson.getLong("log_id");
        String imageStatus = idCardJson.getString("image_status");

        String name = this.getJsonString(idCardJson, "words_result", "姓名", "words");
        String gender = this.getJsonString(idCardJson, "words_result", "性别", "words");
        String nation = this.getJsonString(idCardJson, "words_result", "民族", "words");

        String birthdayText = this.getJsonString(idCardJson, "words_result", "出生", "words");
        Date birthday = this.parseDate(birthdayText, DateUtil.day_format_short);

        String liveAddress = this.getJsonString(idCardJson, "words_result", "住址", "words");
        String idNumber = this.getJsonString(idCardJson, "words_result", "公民身份号码", "words");

        OcrIdCardFrontVo idCardFrontVo = new OcrIdCardFrontVo();

        idCardFrontVo.setOcrJsonText(ocrJsonText);
        idCardFrontVo.setLogId(logId);

        idCardFrontVo.setImageStatus(imageStatus);

        idCardFrontVo.setName(name);

        idCardFrontVo.setGender(gender);

        idCardFrontVo.setNation(nation);

        idCardFrontVo.setBirthdayText(birthdayText);
        idCardFrontVo.setBirthday(birthday);
        idCardFrontVo.setLiveAddress(liveAddress);
        idCardFrontVo.setIdNumber(idNumber);

        return idCardFrontVo;
    }

    @Override
    public OcrIdCardBackVo idCardBackOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("id_card_side", ImgSideEnum.back.name());
        optMap.put("detect_photo", "false");
        optMap.put("detect_card", "false");

        JSONObject idCardJson = this.requestOcrApi(BaiduOcrConstant.idcard, optMap);

        String ocrJsonText = idCardJson.toJSONString();

        Long logId = idCardJson.getLong("log_id");
        String imageStatus = idCardJson.getString("image_status");

        String expireDateText = this.getJsonString(idCardJson, "words_result", "失效日期", "words");
        Date expireDate = this.parseDate(expireDateText, DateUtil.day_format_short);

        String signGov = this.getJsonString(idCardJson, "words_result", "签发机关", "words");

        String signDateText = this.getJsonString(idCardJson, "words_result", "签发日期", "words");
        Date signDate = this.parseDate(signDateText, DateUtil.day_format_short);

        OcrIdCardBackVo idCardBackVo = new OcrIdCardBackVo();

        idCardBackVo.setOcrJsonText(ocrJsonText);
        idCardBackVo.setLogId(logId);

        idCardBackVo.setImageStatus(imageStatus);

        idCardBackVo.setExpireDateText(expireDateText);
        idCardBackVo.setExpireDate(expireDate);
        idCardBackVo.setSignGov(signGov);
        idCardBackVo.setSignDateText(signDateText);
        idCardBackVo.setSignDate(signDate);

        return idCardBackVo;
    }

    @Override
    public DriverLicenseFrontVo driverLicenseFrontOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("driving_license_side", ImgSideEnum.front.name());

        JSONObject driverJson = this.requestOcrApi(BaiduOcrConstant.driving_license, optMap);

        String ocrJsonText = driverJson.toJSONString();
        Long logId = driverJson.getLong("log_id");

        String name = this.getJsonString(driverJson, "words_result", "姓名", "words");
        String birthdayText = this.getJsonString(driverJson, "words_result", "出生日期", "words");
        Date birthday = this.parseDate(birthdayText, DateUtil.day_format_short);

        String driverNumber = this.getJsonString(driverJson, "words_result", "证号", "words");
        String liveAddress = this.getJsonString(driverJson, "words_result", "住址", "words");

        String firstRegisterDateText = this.getJsonString(driverJson, "words_result", "初次领证日期", "words");
        Date firstRegisterDate = this.parseDate(firstRegisterDateText, DateUtil.day_format_short);

        String nationality = this.getJsonString(driverJson, "words_result", "国籍", "words");
        String licenseType = this.getJsonString(driverJson, "words_result", "准驾车型", "words");
        String gender = this.getJsonString(driverJson, "words_result", "性别", "words");
        String issueUnit = this.getJsonString(driverJson, "words_result", "发证单位", "words");

        String validStartDateText = this.getJsonString(driverJson, "words_result", "有效期限", "words");
        Date validStartDate = this.parseDate(validStartDateText, DateUtil.day_format_short);

        String validEndDateText = this.getJsonString(driverJson, "words_result", "至", "words");
        Date validEndDate = this.parseDate(validEndDateText, DateUtil.day_format_short);

        DriverLicenseFrontVo driverLicenseFrontVo = new DriverLicenseFrontVo();

        driverLicenseFrontVo.setOcrJsonText(ocrJsonText);
        driverLicenseFrontVo.setLogId(logId);

        driverLicenseFrontVo.setName(name);

        driverLicenseFrontVo.setBirthdayText(birthdayText);
        driverLicenseFrontVo.setBirthday(birthday);

        driverLicenseFrontVo.setDriverNumber(driverNumber);
        driverLicenseFrontVo.setLiveAddress(liveAddress);

        driverLicenseFrontVo.setFirstRegisterDateText(firstRegisterDateText);
        driverLicenseFrontVo.setFirstRegisterDate(firstRegisterDate);

        driverLicenseFrontVo.setNationality(nationality);
        driverLicenseFrontVo.setLicenseType(licenseType);
        driverLicenseFrontVo.setGender(gender);
        driverLicenseFrontVo.setIssueUnit(issueUnit);

        //如果识别为长期
        if (StringUtils.isNotBlank(validStartDateText)
                && LONG_TIME_STR.equals(validStartDateText)) {
            String validStartDateTText = this.getJsonString(driverJson, "words_result", "有效起始日期", "words");
            Date validStartDateTime = this.parseDate(validStartDateTText, DateUtil.day_format_short);
            driverLicenseFrontVo.setValidStartDateText(validStartDateTText);
            driverLicenseFrontVo.setValidStartDate(validStartDateTime);
            driverLicenseFrontVo.setValidEndDateText(LONG_TIME_STR);
        } else {
            driverLicenseFrontVo.setValidStartDateText(validStartDateText);
            driverLicenseFrontVo.setValidStartDate(validStartDate);
            driverLicenseFrontVo.setValidEndDateText(validEndDateText);
            driverLicenseFrontVo.setValidEndDate(validEndDate);
        }

        return driverLicenseFrontVo;
    }

    @Override
    public DriverLicenseBackVo driverLicenseBackOcr(String url) {
        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("driving_license_side", ImgSideEnum.back.name());

        JSONObject driverJson = this.requestOcrApi(BaiduOcrConstant.driving_license, optMap);

        String ocrJsonText = driverJson.toJSONString();
        Long logId = driverJson.getLong("log_id");

        String name = this.getJsonString(driverJson, "words_result", "姓名", "words");
        String record = this.getJsonString(driverJson, "words_result", "记录", "words");
        String driverNumber = this.getJsonString(driverJson, "words_result", "证号", "words");
        String fileNumber = this.getJsonString(driverJson, "words_result", "档案编号", "words");

        DriverLicenseBackVo driverLicenseBackVo = new DriverLicenseBackVo();

        driverLicenseBackVo.setOcrJsonText(ocrJsonText);
        driverLicenseBackVo.setLogId(logId);

        driverLicenseBackVo.setName(name);
        driverLicenseBackVo.setRecord(record);
        driverLicenseBackVo.setDriverNumber(driverNumber);
        driverLicenseBackVo.setFileNumber(fileNumber);

        return driverLicenseBackVo;
    }

    @Override
    public VehicleLicenseFrontVo vehicleLicenseFrontOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("vehicle_license_side", ImgSideEnum.front.name());

        JSONObject driverJson = this.requestOcrApi(BaiduOcrConstant.vehicle_license, optMap);

        String ocrJsonText = driverJson.toJSONString();
        Long logId = driverJson.getLong("log_id");

        String identifyCode = this.getJsonString(driverJson, "words_result", "车辆识别代号", "words");
        ;
        String liveAddress = this.getJsonString(driverJson, "words_result", "住址", "words");
        ;

        String issueDateText = this.getJsonString(driverJson, "words_result", "发证日期", "words");
        ;
        Date issueDate = this.parseDate(issueDateText, DateUtil.day_format_short);

        String issueUnit = this.getJsonString(driverJson, "words_result", "发证单位", "words");
        ;
        String brandModel = this.getJsonString(driverJson, "words_result", "品牌型号", "words");
        ;
        String carType = this.getJsonString(driverJson, "words_result", "车辆类型", "words");
        ;
        String ownerName = this.getJsonString(driverJson, "words_result", "所有人", "words");
        ;
        String useNature = this.getJsonString(driverJson, "words_result", "使用性质", "words");
        ;
        String engineNumber = this.getJsonString(driverJson, "words_result", "发动机号码", "words");
        ;
        String carNumber = this.getJsonString(driverJson, "words_result", "号牌号码", "words");
        ;

        String registerDateText = this.getJsonString(driverJson, "words_result", "注册日期", "words");
        ;
        Date registerDate = this.parseDate(registerDateText, DateUtil.day_format_short);

        VehicleLicenseFrontVo vehicleLicenseFrontVo = new VehicleLicenseFrontVo();

        vehicleLicenseFrontVo.setOcrJsonText(ocrJsonText);
        vehicleLicenseFrontVo.setLogId(logId);

        vehicleLicenseFrontVo.setIdentifyCode(identifyCode);
        vehicleLicenseFrontVo.setLiveAddress(liveAddress);

        vehicleLicenseFrontVo.setIssueDateText(issueDateText);
        vehicleLicenseFrontVo.setIssueDate(issueDate);

        vehicleLicenseFrontVo.setIssueUnit(issueUnit);
        vehicleLicenseFrontVo.setBrandModel(brandModel);
        vehicleLicenseFrontVo.setCarType(carType);
        vehicleLicenseFrontVo.setOwnerName(ownerName);
        vehicleLicenseFrontVo.setUseNature(useNature);
        vehicleLicenseFrontVo.setEngineNumber(engineNumber);
        vehicleLicenseFrontVo.setCarNumber(carNumber);

        vehicleLicenseFrontVo.setRegisterDateText(registerDateText);
        vehicleLicenseFrontVo.setRegisterDate(registerDate);

        return vehicleLicenseFrontVo;
    }

    @Override
    public VehicleLicenseBackVo vehicleLicenseBackOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);
        optMap.put("vehicle_license_side", ImgSideEnum.back.name());

        JSONObject driverJson = this.requestOcrApi(BaiduOcrConstant.vehicle_license, optMap);

        String ocrJsonText = driverJson.toJSONString();
        Long logId = driverJson.getLong("log_id");

        String inspectionRecord = this.getJsonString(driverJson, "words_result", "检验记录", "words");
        String approvedLoad = this.getJsonString(driverJson, "words_result", "核定载质量", "words");
        String curbWeight = this.getJsonString(driverJson, "words_result", "整备质量", "words");
        String overallSize = this.getJsonString(driverJson, "words_result", "外廓尺寸", "words");
        String passengerNumber = this.getJsonString(driverJson, "words_result", "核定载人数", "words");
        String totalMass = this.getJsonString(driverJson, "words_result", "总质量", "words");
        String fuelType = this.getJsonString(driverJson, "words_result", "燃油类型", "words");
        String tractionMass = this.getJsonString(driverJson, "words_result", "准牵引总质量", "words");
        String remark = this.getJsonString(driverJson, "words_result", "备注", "words");
        String fileNumber = this.getJsonString(driverJson, "words_result", "档案编号", "words");
        String carNumber = this.getJsonString(driverJson, "words_result", "号牌号码", "words");
        String chipNumber = this.getJsonString(driverJson, "words_result", "证芯编号", "words");

        VehicleLicenseBackVo vehicleLicenseBackVo = new VehicleLicenseBackVo();

        vehicleLicenseBackVo.setOcrJsonText(ocrJsonText);
        vehicleLicenseBackVo.setLogId(logId);

        vehicleLicenseBackVo.setInspectionRecord(inspectionRecord);
        vehicleLicenseBackVo.setApprovedLoad(approvedLoad);
        vehicleLicenseBackVo.setCurbWeight(curbWeight);
        vehicleLicenseBackVo.setOverallSize(overallSize);
        vehicleLicenseBackVo.setPassengerNumber(passengerNumber);
        vehicleLicenseBackVo.setTotalMass(totalMass);
        vehicleLicenseBackVo.setFuelType(fuelType);
        vehicleLicenseBackVo.setTractionMass(tractionMass);
        vehicleLicenseBackVo.setRemark(remark);
        vehicleLicenseBackVo.setFileNumber(fileNumber);
        vehicleLicenseBackVo.setCarNumber(carNumber);
        vehicleLicenseBackVo.setChipNumber(chipNumber);

        return vehicleLicenseBackVo;
    }

    @Override
    public OcrBusinessLicenseVo businessLicenseOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);

        JSONObject businessJson = this.requestOcrApi(BaiduOcrConstant.business_license, optMap);

        String ocrJsonText = businessJson.toString();

        Long logId = businessJson.getLong("log_id");

        String businessScope = this.getJsonString(businessJson, "words_result", "经营范围", "words");
        String componentForm = this.getJsonString(businessJson, "words_result", "组成形式", "words");
        String legalPersonName = this.getJsonString(businessJson, "words_result", "法人", "words");
        String licenseNumber = this.getJsonString(businessJson, "words_result", "证件编号", "words");
        String registerCapital = this.getJsonString(businessJson, "words_result", "注册资本", "words");
        String companyName = this.getJsonString(businessJson, "words_result", "单位名称", "words");
        String expiration = this.getJsonString(businessJson, "words_result", "有效期", "words");
        String creditCode = this.getJsonString(businessJson, "words_result", "社会信用代码", "words");
        String paidCapital = this.getJsonString(businessJson, "words_result", "实收资本", "words");

        String validStartDateText = this.getJsonString(businessJson, "words_result", "有效期起始日期", "words");
        Date validStartDate = null;
        if (!BaiduOcrConstant.DEFAULT_VALUE.equals(validStartDateText)) {
            validStartDate = this.parseDate(validStartDateText, DateUtil.zh_day_format);
        }

        String approvalDateText = this.getJsonString(businessJson, "words_result", "核准日期", "words");
        Date approvalDate = this.parseDate(approvalDateText, DateUtil.zh_day_format);

        String establishDateText = this.getJsonString(businessJson, "words_result", "成立日期", "words");
        Date establishDate = this.parseDate(establishDateText, DateUtil.zh_day_format);

        String taxNumber = this.getJsonString(businessJson, "words_result", "税务登记号", "words");
        String registerAddress = this.getJsonString(businessJson, "words_result", "地址", "words");
        String registerGov = this.getJsonString(businessJson, "words_result", "登记机关", "words");
        String companyType = this.getJsonString(businessJson, "words_result", "类型", "words");

        OcrBusinessLicenseVo businessLicenseVo = new OcrBusinessLicenseVo();

        businessLicenseVo.setOcrJsonText(ocrJsonText);
        businessLicenseVo.setLogId(logId);

        businessLicenseVo.setBusinessScope(businessScope);
        businessLicenseVo.setComponentForm(componentForm);
        businessLicenseVo.setLegalPersonName(legalPersonName);
        businessLicenseVo.setLicenseNumber(licenseNumber);
        businessLicenseVo.setRegisterCapital(registerCapital);
        businessLicenseVo.setCompanyName(companyName);
        businessLicenseVo.setExpiration(expiration);
        businessLicenseVo.setCreditCode(creditCode);
        businessLicenseVo.setPaidCapital(paidCapital);

        businessLicenseVo.setValidStartDateText(validStartDateText);
        businessLicenseVo.setValidStartDate(validStartDate);

        businessLicenseVo.setApprovalDateText(approvalDateText);
        businessLicenseVo.setApprovalDate(approvalDate);

        businessLicenseVo.setEstablishDateText(establishDateText);
        businessLicenseVo.setEstablishDate(establishDate);

        businessLicenseVo.setTaxNumber(taxNumber);
        businessLicenseVo.setRegisterAddress(registerAddress);
        businessLicenseVo.setRegisterGov(registerGov);
        businessLicenseVo.setCompanyType(companyType);

        return businessLicenseVo;
    }

    @Override
    public RoadTransportVo roadTransportOcr(String url) {

        Map<String, String> optMap = new HashMap<>();
        optMap.put("url", url);

        JSONObject businessJson = this.requestOcrApi(BaiduOcrConstant.road_transport, optMap);

        String ocrJsonText = businessJson.toString();

        Long logId = businessJson.getLong("log_id");

        String carNumber = this.getJsonString(businessJson, "words_result", "车辆号牌[0]", "word");
        String economicType = this.getJsonString(businessJson, "words_result", "经济类型[0]", "word");
        String businessScope = this.getJsonString(businessJson, "words_result", "经营范围[0]", "word");
        String vehicleType = this.getJsonString(businessJson, "words_result", "车辆类型[0]", "word");
        String tonSeat = this.getJsonString(businessJson, "words_result", "吨座位[0]", "word");
        String remark = this.getJsonString(businessJson, "words_result", "备注[0]", "word");
        String businessLicense = this.getJsonString(businessJson, "words_result", "经营许可证[0]", "word");
        String vehicleHeight = this.getJsonString(businessJson, "words_result", "车辆毫米_高[0]", "word");
        String vehicleWidth = this.getJsonString(businessJson, "words_result", "车辆毫米_宽[0]", "word");

        String issueDateText = this.getJsonString(businessJson, "words_result", "发证日期[0]", "word");
        Date issueDate = this.parseDate(issueDateText, DateUtil.zh_day_format);

        String address = this.getJsonString(businessJson, "words_result", "地址[0]", "word");
        String VehicleLength = this.getJsonString(businessJson, "words_result", "车辆毫米_长[0]", "word");
        String ownerName = this.getJsonString(businessJson, "words_result", "业户名称[0]", "word");

        String firstRegisterDateText = this.getJsonString(businessJson, "words_result", "初领日期[0]", "word");
        Date firstRegisterDate = this.parseDate(firstRegisterDateText, DateUtil.zh_day_format);

        RoadTransportVo roadTransportVo = new RoadTransportVo();

        roadTransportVo.setOcrJsonText(ocrJsonText);
        roadTransportVo.setLogId(logId);

        roadTransportVo.setCarNumber(carNumber);
        roadTransportVo.setEconomicType(economicType);
        roadTransportVo.setBusinessScope(businessScope);
        roadTransportVo.setVehicleType(vehicleType);
        roadTransportVo.setTonSeat(tonSeat);
        roadTransportVo.setRemark(remark);
        roadTransportVo.setBusinessLicense(businessLicense);
        roadTransportVo.setVehicleHeight(vehicleHeight);
        roadTransportVo.setVehicleWidth(vehicleWidth);

        roadTransportVo.setIssueDateText(issueDateText);
        roadTransportVo.setIssueDate(issueDate);

        roadTransportVo.setAddress(address);
        roadTransportVo.setVehicleLength(VehicleLength);
        roadTransportVo.setOwnerName(ownerName);

        roadTransportVo.setFirstRegisterDateText(firstRegisterDateText);
        roadTransportVo.setFirstRegisterDate(firstRegisterDate);

        return roadTransportVo;
    }

}
