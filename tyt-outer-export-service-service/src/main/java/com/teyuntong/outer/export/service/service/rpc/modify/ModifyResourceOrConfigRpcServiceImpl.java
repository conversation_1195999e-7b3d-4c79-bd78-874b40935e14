package com.teyuntong.outer.export.service.service.rpc.modify;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.teyuntong.outer.export.service.client.modify.service.ModifyResourceOrConfigRpcService;
import com.teyuntong.outer.export.service.client.modify.vo.ModifyResourceOrConfigVO;
import com.teyuntong.outer.export.service.service.common.config.DingTalkProperties;
import com.teyuntong.outer.export.service.service.common.utils.AppConfig;
import com.teyuntong.outer.export.service.service.remote.common.TytConfigRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@RestController
public class ModifyResourceOrConfigRpcServiceImpl implements ModifyResourceOrConfigRpcService {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private DingTalkProperties dingTalkProperties;

    @Autowired
    private TytConfigRemoteService configRpcService;

    /**
     * 用于保护DingTalkClient实例更新的锁
     */
    private final ReentrantLock lock = new ReentrantLock();

    /**
     * DingTalkClient实例，不再使用final修饰，以便于更新
     */
    private DingTalkClient creeperDingTalkClient;

    /**
     * 初始化DingTalkClient
     */
    @PostConstruct
    public void init() {
        refreshDingTalkClient();
    }

    /**
     * 刷新DingTalkClient实例
     * 当配置中心的webhook URL发生变化时会自动调用此方法
     * 因为DingTalkProperties使用了@RefreshScope注解
     */
    public void refreshDingTalkClient() {
        String webhookUrl = dingTalkProperties.getWebhookUrl();
        log.info("Refreshing DingTalkClient with webhook URL: {}", webhookUrl);

        lock.lock();
        try {
            creeperDingTalkClient = new DefaultDingTalkClient(webhookUrl);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void dingMessage(ModifyResourceOrConfigVO modifyResourceOrConfigVO) {
        if (modifyResourceOrConfigVO == null || modifyResourceOrConfigVO.getType() == null
                || StringUtils.isBlank(modifyResourceOrConfigVO.getMessage())) {
            return;
        }

        if (configRpcService.getIntValue("dingtalk_message_switch", 1) == 1) {
            if (!"prod".equals(appConfig.getActiveProfile())) {
                return;
            }
        }

        String type = "开关";
        if (modifyResourceOrConfigVO.getType() == 2) {
            type = "公共资源";
        } else if (modifyResourceOrConfigVO.getType() == 3) {
            type = "AB测试";
        } else if (modifyResourceOrConfigVO.getType() == 4) {
            type = "修改权益发放策略";
        }
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent("环境：" + appConfig.getActiveProfile()+ " 【" + type  + "】修改 " + modifyResourceOrConfigVO.getMessage());
        req.setText(text);
        try {
            // 获取最新的DingTalkClient实例
            DingTalkClient client;
            lock.lock();
            try {
                client = creeperDingTalkClient;
            } finally {
                lock.unlock();
            }

            // 如果client为null，尝试重新初始化
            if (client == null) {
                refreshDingTalkClient();
                lock.lock();
                try {
                    client = creeperDingTalkClient;
                } finally {
                    lock.unlock();
                }
            }

            OapiRobotSendResponse rsp = client.execute(req, "");
        } catch (ApiException e) {
            log.info("修改AB测试开关公共资源发送钉钉消息失败 原因：", e);
        }

    }

}
