package com.teyuntong.outer.export.service.service.biz.manbang.account.service;

import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBAccountDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBLicenseDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBAccountInfoVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBLicenseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/15 11:21
 */
public interface MBAccountService {



    List<MBAccountInfoVO> queryAccountsByMobile(MBAccountDTO mbAccountDTO);


    MBLicenseVO queryLicenseByTypes(MBLicenseDTO mbLicenseDTO);
}
