package com.teyuntong.outer.export.service.service.biz.ocr.util;

import com.teyuntong.outer.export.service.client.common.old.enums.ResponseEnum;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {
    public static final String date_time_format = "yyyy-MM-dd HH:mm:ss";
    public static final String date_time_format_short = "yyyyMMddHHmmss";

    public static final String day_format = "yyyy-MM-dd";
    public static final String day_format_short = "yyyyMMdd";

    public static final String zh_day_format = "yyyy年MM月dd日";

    public static final String hour_format = "HH:mm:ss";

    public static final String default_time_zone = "GMT+8";

    private static final long _1h = 3600000;

    /**
     * 毫秒时间戳 + 随机数
     *
     * @return string 时间戳
     */
    public static String getNanoRandom() {
        long time = System.nanoTime();
        Random random = new Random();
        time += random.nextInt(999999999);
        return String.valueOf(time);
    }

    /**
     * 获得当前日期
     *
     * @param formatType
     * @return
     */
    public static String getNowTime(String formatType) {

        Date date = new Date();

        SimpleDateFormat format = new SimpleDateFormat(formatType);

        String resultDay = format.format(date);

        return resultDay;
    }

    /**
     * 转换String 为 date
     *
     * @param dateStr
     * @param formatType
     * @return
     * @throws Exception
     */
    public static Date parseDate(String dateStr, String formatType) {

        SimpleDateFormat format = new SimpleDateFormat(formatType);

        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            throw TytException.createException(e);
        }

        return date;
    }

    /**
     * 将 2014-01-11 这样的日期转换成 20140211
     *
     * @param day
     * @return
     */
    public static long parseDayToLong(String day) {
        long longDay = new Long(day.replaceAll("-", ""));

        return longDay;
    }

    /**
     * 获得字符串日期时间戳
     *
     * @param day
     * @param formatType
     * @return
     * @throws Exception
     */
    public static long getDateTime(String day, String formatType) throws Exception {

        Date date = DateUtil.parseDate(day, formatType);

        long timeLong = date.getTime();

        return timeLong;
    }

    /**
     * 日期转字符串
     *
     * @param date
     * @param formatType
     * @return
     */
    public static String dateToString(Date date, String formatType) {
        SimpleDateFormat format = new SimpleDateFormat(formatType);

        String resultDay = format.format(date);
        return resultDay;
    }

    /**
     * excel字符串转DATE
     *
     * @param dateStr
     * @return
     * @throws Exception
     */
    public static Date strongDateParse(String dateStr) {
        String[] formatArray = {"y", "M", "d", "H", "m", "s"};
        //yyyy-MM-dd HH:mm:ss
        String[] splitArray = {"-", "-", " ", ":", ":", ""};
        int[] partSizeArray = {4, 2, 2, 2, 2, 2};

        char[] charArray = dateStr.toCharArray();
        StringBuilder dateStrBuilder = new StringBuilder();
        StringBuilder formatBuilder = new StringBuilder();
        int part = 0;

        int nowSize = 0;
        for (char oneChar : charArray) {
            int partSize = partSizeArray[part];

            if (part >= (formatArray.length - 1) && nowSize >= partSize) {
                throw TytException.createException(ResponseEnum.sys_error.info("日期格式化错误"));
            }
            if (Character.isDigit(oneChar)) {
                if (nowSize >= partSize) {
                    throw TytException.createException(ResponseEnum.sys_error.info("日期格式化错误"));
                }
                dateStrBuilder.append(oneChar);
                String formatPart = formatArray[part];
                formatBuilder.append(formatPart);

                nowSize++;

            } else {
                String spliter = splitArray[part];
                dateStrBuilder.append(spliter);
                formatBuilder.append(spliter);

                nowSize = 0;
                part++;
            }
        }

        String newDateStr = dateStrBuilder.toString();
        String formatType = formatBuilder.toString();
        Date date = parseDate(newDateStr, formatType);

        return date;
    }

    /**
     * 把时间戳转换成字符串格式
     *
     * @param dateFormat 格式
     * @param timeMillis 时间戳
     * @return
     */
    public static String transferLongToDate(String dateFormat, Long timeMillis) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date date = new Date(timeMillis);
        return sdf.format(date);
    }

    /**
     * 日期计算
     *
     * @param date   时间
     * @param type   Calendar.DAY_OF_MONTH
     * @param number 可以为负数
     * @return
     */
    public static Date addTime(Date date, int type, int number) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.add(Calendar.DAY_OF_MONTH, number);

        Date resultDate = calendar.getTime();

        return resultDate;
    }

    /**
     * 天开始时间
     *
     * @param date
     * @return
     */
    public static Date startOfDay(Date date) {

        Calendar todayBegin = Calendar.getInstance();
        todayBegin.setTime(date);
        todayBegin.set(Calendar.HOUR_OF_DAY, 0); // Calendar.HOUR 12小时制
        todayBegin.set(Calendar.MINUTE, 0);
        todayBegin.set(Calendar.SECOND, 0);
        todayBegin.set(Calendar.MILLISECOND, 0);

        Date time = todayBegin.getTime();

        return time;
    }

    /**
     * 天结束时间
     *
     * @param date
     * @return
     */
    public static Date endOfDay(Date date) {

        Calendar todayEnd = Calendar.getInstance();
        todayEnd.setTime(date);
        todayEnd.set(Calendar.HOUR_OF_DAY, 23); // Calendar.HOUR 12小时制
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);

        Date time = todayEnd.getTime();

        return time;
    }

    /**
     * 判断某个时间戳是否我当天
     *
     * @param time
     * @return
     */
    public static boolean isToday(long time) {
        //Calendar使用单例，多次调用不重复创建对象
        Calendar calendar = Calendar.getInstance();
        //使用System获取当前时间
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long today = calendar.getTimeInMillis();
        if (time - today < _1h * 24 && time - today > 0) {
            return true;
        }
        return false;
    }
}
