package com.teyuntong.outer.export.service.service.rpc.cticloud;

import com.teyuntong.outer.export.service.client.cticloud.axb.service.AxbRpcService;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.*;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.service.IAxbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * axb绑定相关
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AxbRpcServiceImpl implements AxbRpcService {

    @Autowired
    private IAxbService axbService;

    @Override
    public AxbBindVO axbBind(AxbBindReq req) {
        TytAxbBindInfo tytAxbBindInfo = axbService.axbBind(req);

        AxbBindVO vo = AxbBindVO.builder()
                .id(tytAxbBindInfo.getId())
                .telA(tytAxbBindInfo.getTelA())
                .telB(tytAxbBindInfo.getTelB())
                .telX(tytAxbBindInfo.getTelX())
                .expirationDate(tytAxbBindInfo.getExpirationDate())
                .isPermanent(tytAxbBindInfo.getIsPermanent())
                .extraField(tytAxbBindInfo.getExtraField()).build();
        return vo;
    }

    @Override
    public AxbBindVO bindGet(String telA, String telB, Integer bizType, Long bizId, Integer expiration, String extraField) {
        AxbBindReq req = new AxbBindReq(telA, telB, bizType, bizId, expiration, extraField, null, null);
        TytAxbBindInfo tytAxbBindInfo = axbService.axbBind(req);

        AxbBindVO vo = AxbBindVO.builder()
                .id(tytAxbBindInfo.getId())
                .telA(tytAxbBindInfo.getTelA())
                .telB(tytAxbBindInfo.getTelB())
                .telX(tytAxbBindInfo.getTelX())
                .expirationDate(tytAxbBindInfo.getExpirationDate())
                .isPermanent(tytAxbBindInfo.getIsPermanent())
                .extraField(tytAxbBindInfo.getExtraField()).build();
        return vo;
    }

    @Override
    public void axbUpdate(AxbUpdateReq req) {
        axbService.axbUpdate(req);
    }

    @Override
    public List<AxbInfoVO> getAxbInfo(Integer bizType, Long bizId, String telA, String telB, String extraField) {
        List<TytAxbBindInfo> infos = axbService.getAxbInfo(bizType, bizId, telA, telB, extraField);

        return infos.stream().map(it -> AxbInfoVO.builder()
                .id(it.getId())
                .telA(it.getTelA())
                .telB(it.getTelB())
                .telX(it.getTelX())
                .bizType(it.getBizType())
                .bizId(it.getBizId())
                .expirationDate(it.getExpirationDate())
                .extraField(it.getExtraField())
                .build()).collect(Collectors.toList());
    }

    @Override
    public void axbDelete(AxbDeleteReq req) {
        List<Long> ids = req.getIds();
        List<String> thirdPartyIds = req.getThirdPartyIds();
        if (ids != null && !ids.isEmpty()) {
            for (Long id : ids) {
                try {
                    axbService.axbDelete(id);
                } catch (Exception e) {
                    log.error("axb删除失败 id:{}", id, e);
                }
            }
        } else if (CollectionUtils.isNotEmpty(thirdPartyIds)) {
            for (String thirdPartyId : thirdPartyIds) {
                TytAxbBindInfo axbInfo = axbService.getAxbInfoByThirdPartyId(thirdPartyId);
                if(axbInfo != null && axbInfo.getId() != null) {
                    axbService.axbDelete(axbInfo.getId());
                }
            }
        } else {
            List<TytAxbBindInfo> axbInfos = axbService.getAxbInfo(req.getBizType(), req.getBizId(), null, null, null);
            for (TytAxbBindInfo axbInfo : axbInfos) {
                try {
                    axbService.axbDelete(axbInfo.getId());
                } catch (Exception e) {
                    log.error("axb删除失败 id:{}", axbInfo.getId(), e);
                }
            }
        }
    }

    @Override
    public CDRsResp getCDR(CDRReq req) {
        CDRsResp cdr = axbService.getCDR(req);
        return cdr;
    }

}
