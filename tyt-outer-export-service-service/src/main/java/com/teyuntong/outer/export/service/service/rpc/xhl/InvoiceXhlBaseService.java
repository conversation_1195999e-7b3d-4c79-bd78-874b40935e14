package com.teyuntong.outer.export.service.service.rpc.xhl;


import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;

import java.util.Objects;

/**
 * 开票-翔和翎开放接口基础类
 *
 * <AUTHOR>
 * @since 2025/01/13 13:13
 */
public class InvoiceXhlBaseService {

    public <T> WebResult<T> fromXhlDataResult(XhlDataResult<T> xhlDataResult) {
        if (Objects.isNull(xhlDataResult)) {
            return WebResult.error(CommonErrorCode.INTERNAL_ERROR, null);
        }
        WebResult<T> result = new WebResult<>();
        result.setCode(xhlDataResult.getCode());
        result.setMsg(xhlDataResult.getMsg());
        result.setData(xhlDataResult.getData());
        return result;
    }

}
