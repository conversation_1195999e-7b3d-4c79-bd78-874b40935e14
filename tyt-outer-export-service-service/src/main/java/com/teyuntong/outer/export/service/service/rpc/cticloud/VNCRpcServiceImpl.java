package com.teyuntong.outer.export.service.service.rpc.cticloud;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.cticloud.axb.service.VNCRpcService;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.biz.cticloud.CticloudClient;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.ASRResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.CticloudAxbApi;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper.TytAxbBindInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Response;

import java.io.IOException;
import java.util.HashMap;

/**
 * 天润 VNC新平台相关
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@Slf4j
@RestController
public class VNCRpcServiceImpl implements VNCRpcService {


    private final CticloudAxbApi cticloudAxbApi;
    private final CticloudAxbApi cticloudAxbApi2;

    public VNCRpcServiceImpl(CticloudClient cticloudClient) {
        this.cticloudAxbApi = cticloudClient.getCticloudAxBApi();
        this.cticloudAxbApi2 = cticloudClient.getCticloudAxBApi2();
    }

    @Autowired
    private TytAxbBindInfoMapper tytAxbBindInfoMapper;


    @Override
    public String getRecordUrl(String recordName, String subId) throws IOException {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("recordName", recordName);

        Response<CticloudResp<String>> response;

        TytAxbBindInfo tytAxbBindInfo =  new TytAxbBindInfo();
        if (subId  != null) {
            TytAxbBindInfo tytAxbBindInfoReq = new TytAxbBindInfo();
            tytAxbBindInfoReq.setThirdPartyId(subId);
            tytAxbBindInfoReq.setDelFlag(false);
            tytAxbBindInfo = tytAxbBindInfoMapper.selectByThirdPartyId(tytAxbBindInfoReq);
        } else {
            tytAxbBindInfo.setThirdPartyType(1);
        }
        if (tytAxbBindInfo.getThirdPartyType() == 1) {
            response = cticloudAxbApi.record(paramMap).execute();
        } else {
            response = cticloudAxbApi2.record(paramMap).execute();
        }

        log.info("VNCController getRecordUrl  [recordName:{}]  [response:{}]", recordName, JSONObject.toJSONString(response.body()));

        if (!response.isSuccessful()) {
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        String result = "";
        if (response.body() != null && response.body().getData() != null) {
            result = response.body().getData();
        }
        return result;
    }

    @Override
    public void asrCreate(String callId, String subId) throws IOException {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("callId", callId);

        Response<CticloudResp<String>> response;

        TytAxbBindInfo tytAxbBindInfo =  new TytAxbBindInfo();
        if (subId != null) {
            TytAxbBindInfo tytAxbBindInfoReq = new TytAxbBindInfo();
            tytAxbBindInfoReq.setThirdPartyId(subId);
            tytAxbBindInfoReq.setDelFlag(false);
            tytAxbBindInfo = tytAxbBindInfoMapper.selectByThirdPartyId(tytAxbBindInfoReq);
        } else {
            tytAxbBindInfo.setThirdPartyType(1);
        }
        if (tytAxbBindInfo != null && tytAxbBindInfo.getThirdPartyType() != null && tytAxbBindInfo.getThirdPartyType() == 1) {
            response = cticloudAxbApi.asrCreate(paramMap).execute();
        } else {
            response = cticloudAxbApi2.asrCreate(paramMap).execute();
        }

        log.info("VNCController asrCreate  [callId:{}, subId:{}]  [response:{}]", callId, subId, JSONObject.toJSONString(response.body()));

        if (!response.isSuccessful()) {
            log.info("创建音转文任务失败 请求错误码：{}", response.code());
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        if (response.body() == null || response.body().getResult() == null || response.body().getResult() != 0) {
            log.info("创建音转文任务失败 原因：{}", JSONObject.toJSONString(response.body()));
            throw new BusinessException(OuterExportErrorCode.COMMON_TASK_FAIL_ERROR);
        }
    }

    @Override
    public String asrQuery(String callId) throws IOException {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("callId", callId);
        Response<CticloudResp<ASRResp>> response = cticloudAxbApi.queryCdrAsr(paramMap).execute();
        log.info("VNCController asrQuery  [callId:{}]  [response:{}]", callId, JSONObject.toJSONString(response.body()));

        if (!response.isSuccessful()) {
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        if (response.body() != null && response.body().getData() != null
                && CollectionUtils.isNotEmpty(response.body().getData().getAsrs())
                && response.body().getData().getAsrs().get(0) != null && StringUtils.isNotBlank(response.body().getData().getAsrs().get(0).getText())) {
            return response.body().getData().getAsrs().get(0).getText();
        }
        return "";
    }
}
