package com.teyuntong.outer.export.service.service.biz.esign.sign.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.teyuntong.outer.export.service.client.esign.vo.sign.*;

/**
 * <AUTHOR>
 * @since 2024/02/05 10:53
 */
public interface EsignSignService {

    CreatePdfVO createPdf(CreatePdfReq req);

    AddAccountVO addAccount(AddAccountReq req);

    AddAccountVO addPersonAccount(AddPersonAccountReq req);

    TemplateSealVO addTemplateSeal(TemplateSealReq req);

    TemplateSealVO addPersonSeal(PersonTemplateSealReq req);

    void sendSignMobileCode3rd(SendSignMobileCode3rdReq req);

    LocalSafeSignPDF3rdVO localSafeSignPDF3rd(LocalSafeSignPDF3rdReq req);

    LocalSafeSignPDF3rdVO multiPositionSign3rd(MultiPositionSignPDF3rdReq req);

    LocalSignPdfVO sealIdSign(LocalSignPdfReq req);

    LocalVerifyPdfVO localVerifyPdf(LocalVerifyPdfReq req) throws JsonProcessingException;

    LocalSafeSignPDF3rdVO silentUserSealSign(SilentUserSealSignReq req);

}
