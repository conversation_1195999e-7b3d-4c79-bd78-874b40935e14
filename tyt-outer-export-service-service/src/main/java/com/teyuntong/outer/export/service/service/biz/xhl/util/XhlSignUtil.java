package com.teyuntong.outer.export.service.service.biz.xhl.util;

import com.alibaba.fastjson.JSON;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * app生成签名的工具
 *
 * <AUTHOR>
 */
public class XhlSignUtil {

    private static int signPeriod = 1 * 60 * 1000;

    /**
     * 验证签名的有效性
     *
     * @param request HTTP请求对象，用于获取请求参数
     * @param key     用于生成签名的密钥
     * @return 如果签名有效，返回true；否则抛出异常
     * @throws Exception 如果时间戳为空、时间戳已过期或签名无效，则抛出异常
     */
    public static boolean verifySign(HttpServletRequest request, String key) throws Exception {
        Object timestamp = request.getParameter("timestamp");
        Object sign = request.getParameter("sign");
        if (timestamp == null || timestamp.equals("")) {
            throw new Exception("访问校验失败：时间戳不能为空！[" + timestamp + "]");
        }
        long period = System.currentTimeMillis() - Long.parseLong(timestamp.toString());
        period = period < 0 ? -period : period;
        if (period > signPeriod) {
            throw new Exception("访问校验失败：时间戳已过期！[" + timestamp + "]");
        }
        if (sign == null || !sign.toString().trim().equals(getSign(request.getParameter("appId"), key, request.getRequestURI(), timestamp, request.getParameter("body")))) {
            throw new Exception("访问校验失败：无效的签名！");
        }
        return true;
    }

    /**
     * 生成签名规则
     *
     * @param paramsMap 包含所有请求参数的映射
     * @param apiKey    用于生成签名的API密钥
     * @return 根据参数生成的签名字符串
     */
    public static String getSign(Map<String, Object> paramsMap, String apiKey) {
        List<String> keys = new ArrayList<String>();
        keys.addAll(paramsMap.keySet());
        Collections.sort(keys);
        StringBuffer sb = new StringBuffer();
        for (String key : keys) {
            if (!key.equals("sign")) {
                sb.append(key).append("=").append(paramsMap.get(key)).append("&");
            }
        }
        sb.append("apiKey=").append(apiKey);
        return Encrypt.MD5(sb.toString().trim());
    }

    /**
     * 生成签名规则
     *
     * @param appId     应用ID
     * @param apiKey    API密钥
     * @param url       请求的URL
     * @param timestamp 时间戳
     * @param body      请求体内容
     * @return 根据参数生成的签名字符串
     */
    public static String getSign(String appId, String apiKey, String url, Object timestamp, String body) {
        StringBuffer sb = new StringBuffer(url);
        sb.append("&appId=").append(appId);
        sb.append("&timestamp=").append(timestamp);
        sb.append("&body=").append(body);
        sb.append("&apiKey=").append(apiKey);
        return Encrypt.MD5(sb.toString());
    }

    /**
     * 生成签名规则（特定于YHB）
     *
     * @param appId     应用ID
     * @param apiKey    API密钥
     * @param url       请求的URL
     * @param timestamp 时间戳
     * @param body      请求体内容
     * @return 根据参数生成的签名字符串
     */
    public static String getSignYHB(String appId, String apiKey, String url, Object timestamp, String body) {
        StringBuffer sb = new StringBuffer();
        sb.append("appId=").append(appId);
        sb.append("&appKey=").append(apiKey);
        sb.append("&content=").append(body);
        sb.append("&timestamp=").append(timestamp);
        // System.out.println(sb.toString());
        return Encrypt.MD5(sb.toString());
    }

    /**
     * 获得相对API URL
     *
     * @param url 输入的完整URL或相对URL
     * @return 返回相对API URL
     */
    public static String getRelUrl(String url) {
        url = url.trim();
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        if (url.toLowerCase().startsWith("http")) {
            url = url.substring(url.indexOf("//") + 2);
            return url.substring(url.indexOf("/"));
        } else if (url.startsWith("/")) {
            return url;
        } else {
            return "/" + url;
        }
    }

    public static String getBodyString(Object body) {
        if (body == null) {
            return "";
        } else if (body instanceof String) {
            return body.toString();
        } else {
            return JSON.toJSONString(body);
        }
    }

}
