package com.teyuntong.outer.export.service.service.biz.xhl.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Encrypt {

    // 固定盐值，用于增强加密安全性
    private static final String fixSalt = "79d721612210472f9fb2804972d8b42d";

    /**
     * 传入文件，返回 MD5 串
     *
     * @param file 文件
     * @return 文件的MD5值
     */
    public static String MD5(final File file) {
        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            return new BigInteger(1, md5.digest()).toString(16);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != in) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 传入文本内容，返回 MD5 串
     *
     * @param strText 待加密的文本
     * @return 文本的MD5值
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String MD5(final String strText) {
        try {
            return SHA(strText.getBytes("UTF-8"), "MD5");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 传入文本内容和随机盐，返回 MD5 串
     *
     * @param strText 待加密的文本
     * @param salt    随机盐
     * @return 加盐后文本的MD5值
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String MD5(final String strText, String salt) {
        return MD5(strText + "&" + fixSalt + "&" + salt);
    }

    /**
     * 传入文本内容，返回 MD5 串的大写形式
     *
     * @param strText 待加密的文本
     * @return 文本的MD5值（大写）
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String MD5toUpperCase(final String strText) {
        return MD5(strText).toUpperCase();
    }

    /**
     * 传入文本内容和随机盐，返回 MD5 串的大写形式
     *
     * @param strText 待加密的文本
     * @param salt    随机盐
     * @return 加盐后文本的MD5值（大写）
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String MD5toUpperCase(final String strText, String salt) {
        return MD5(strText + "&" + fixSalt + "&" + salt).toUpperCase();
    }

    /**
     * 传入文本内容，返回 SHA-256 串
     *
     * @param strText 待加密的文本
     * @return 文本的SHA-256值
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String SHA256(final String strText) {
        try {
            return SHA(strText.getBytes("UTF-8"), "SHA-256");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 传入文本内容，返回 SHA-512 串
     *
     * @param strText 待加密的文本
     * @return 文本的SHA-512值
     * @throws UnsupportedEncodingException 字符编码不支持异常
     */
    public static String SHA512(final String strText) {
        try {
            return SHA(strText.getBytes("UTF-8"), "SHA-512");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 字符串 SHA 加密
     *
     * @param strText 待加密的字节数组
     * @param strType 加密类型
     * @return 加密后的字符串
     */
    private static String SHA(final byte[] strText, final String strType) {
        // 返回值
        String strResult = null;

        // 是否是有效字符串
        if (strText != null && strText.length > 0) {
            try {
                // SHA 加密开始
                // 创建加密对象 并传入加密类型
                MessageDigest messageDigest = MessageDigest.getInstance(strType);
                // 传入要加密的字符串
                messageDigest.update(strText);
                // 得到 byte 类型结果
                byte byteBuffer[] = messageDigest.digest();

                // 将 byte 转换为 string
                StringBuffer strHexString = new StringBuffer();
                // 遍历 byte buffer
                for (int i = 0; i < byteBuffer.length; i++) {
                    String hex = Integer.toHexString(0xff & byteBuffer[i]);
                    if (hex.length() == 1) {
                        strHexString.append('0');
                    }
                    strHexString.append(hex);
                }
                // 得到返回结果
                strResult = strHexString.toString();
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            }
        }
        return strResult;
    }
}  
