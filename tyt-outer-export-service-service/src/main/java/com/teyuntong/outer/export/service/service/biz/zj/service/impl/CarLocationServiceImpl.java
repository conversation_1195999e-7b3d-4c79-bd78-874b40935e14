package com.teyuntong.outer.export.service.service.biz.zj.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.manbang.util.HttpClientUtils;
import com.teyuntong.outer.export.service.service.biz.zj.constant.ZJConst;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJHistoryTrackResult;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJVehicleLocationResult;
import com.teyuntong.outer.export.service.service.biz.zj.service.CarLocationService;
import com.teyuntong.outer.export.service.service.biz.zj.service.LoginService;
import com.teyuntong.outer.export.service.service.common.property.ZJProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/05 13:51
 */

@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties({ZJProperties.class})
public class CarLocationServiceImpl implements CarLocationService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final LoginService loginService;


    private final ZJProperties zjProperties;


    /**
     *
     * @param carHeadNo  车牌号
     * @param color 颜色 1 蓝色、2 黄色、3 黄绿  可选参数 默认 2
     *
     */
    @Override
    public ZJVehicleLocationResult getCarRealTimeLocation(String carHeadNo, String color) {
        log.info("调用中交获取车辆实时位置参数carHeadNo:{},color:{}",carHeadNo,color);

        try {
            String token = loginService.login();
            if(StringUtils.isBlank(token)){
                log.error("调用中交获取车辆实时位置时token为空");
                return null;
            }
            Map<String,String> params = new HashMap<>();
            if (StringUtils.isBlank(carHeadNo)){
                log.error("调用中交获取车辆实时位置时参数车牌号为空");
                return null;
            }
            String[] carHeadNoArray = carHeadNo.split(",");
            if (StringUtils.isBlank(color)){
                color = "2";
            }
            StringBuilder stringBuilder = new StringBuilder();
            for (String vno:carHeadNoArray) {
                stringBuilder.append(vno).append("_").append(color).append(",");
            }
            if(!stringBuilder.isEmpty()){
                params.put("vnos", stringBuilder.substring(0,stringBuilder.length()-1));
            }
            log.info("调用中交获取车辆实时位置参数：{}",JSON.toJSONString(params));
            String vehicleLocationResultStr = HttpClientUtils.doPostWithToken(zjProperties.getUrl() + ZJConst.CAR_REAL_TIME_LOCATION_URL, JSON.toJSONString(params), token);
            log.info("调用中交获取车辆实时位置返回结果：{}",vehicleLocationResultStr);
            JSONObject jsonObject = JSON.parseObject(vehicleLocationResultStr);
            if(Objects.nonNull(jsonObject)){
                if (jsonObject.get("code") != null && 0 == Integer.parseInt(jsonObject.get("code").toString())) {
                    ZJVehicleLocationResult vehicleLocationResult = JSON.parseObject(jsonObject.getString("data"), ZJVehicleLocationResult.class);
                    log.info("调用中交获取车辆实时位置返回结果vehicleLocationResult：{}",JSON.toJSONString(vehicleLocationResult));
                    return vehicleLocationResult;
                }else {
                    log.error("调用中交获取车辆实时位置返回结果异常code:{},msg:{}",jsonObject.get("code"),jsonObject.get("msg"));
                    return null;
                }
            }else {
                log.error("调用中交获取车辆实时位置返回结果为空");
                return null;
            }
        }catch (Exception e){
            log.error("调用中交获取车辆实时位置异常",e);
            return null;
        }
    }

    /**
     * 查询车辆历史轨迹
     * @param carHeadNo 车牌号
     * @param color 车牌颜色 1 蓝色、2 黄色、3 黄绿色  可选参数，默认 2
     * @param beginTime  查询开始时间 格式：yyyy-MM-dd HH:mm:ss   近 6 个月自然月
     * @param endTime  查询结束时间  格式：yyyy-MM-dd HH:mm:ss   与开始时间相差 72小时之内
     */
    @Override
    public ZJHistoryTrackResult getCarLocus(String carHeadNo, String color, String beginTime, String endTime) {
        log.info("调用中交获取车辆历史轨迹参数carHeadNo:{},color:{},beginTime:{},endTime:{}",carHeadNo,color,beginTime,endTime);

        // 1.开始时间和结束时间都不在近6月以内 不查中交；2.开始时间和结束时间都超过当前日期 不查中交
        if(!isWithinSixMonths(beginTime) && !isWithinSixMonths(endTime) || timeIfGreaterThanCurrentTime(beginTime)){
            log.info("【1】【2】调用中交获取车辆历史轨迹查询时间参数违法carHeadNo:{},beginTime:{},endTime:{}",carHeadNo,beginTime,endTime);
            return null;
        }

        // 3.开始时间不在近6个月内，结束时间在近6个月内，更新开始时间为最早的近6月日期，查询中交
        if(!isWithinSixMonths(beginTime) && isWithinSixMonths(endTime)){
            beginTime = getEarliestDateInLastMonths();
            log.info("【3】调用中交获取车辆历史轨迹查询时间carHeadNo:{},beginTime:{},endTime:{}",carHeadNo,beginTime,endTime);
            if(!isDate1BeforeOrEqualDate2(beginTime,endTime)){
                log.info("【3】调用中交获取车辆历史轨迹查询开始时间晚于结束时间carHeadNo:{},beginTime:{},endTime:{}",carHeadNo,beginTime,endTime);
                return null;
            }

        }

        // 5.开始时间在近6个月内，结束时间晚于当前时间，更新结束时间为当前日期，查询中交
        if(isWithinSixMonths(beginTime) && timeIfGreaterThanCurrentTime(endTime)){
            endTime = updateEndTimeIfGreaterThanCurrentTime(endTime);
            log.info("【5】调用中交获取车辆历史轨迹查询时间carHeadNo:{},beginTime:{},endTime:{}",carHeadNo,beginTime,endTime);
            if(!isDate1BeforeOrEqualDate2(beginTime,endTime)){
                log.info("【5】调用中交获取车辆历史轨迹查询开始时间晚于结束时间carHeadNo:{},beginTime:{},endTime:{}",carHeadNo,beginTime,endTime);
                return null;
            }
        }
        // 4.开始时间，结束时间都在近6个月内，查询中交
        log.info("【4】调用中交获取车辆历史轨迹参数carHeadNo:{},color:{},beginTime:{},endTime:{}",carHeadNo,color,beginTime,endTime);

        try {
            String token = loginService.login();
            if (StringUtils.isBlank(token)) {
                log.error("调用中交获取车辆历史轨迹时token为空");
                return null;
            }
            Map<String, String> params = new HashMap<>();
            params.put("vclN", carHeadNo);
            if (StringUtils.isNotBlank(color)) {
                params.put("vco", color);
            } else {
                params.put("vco", "2");
            }
            params.put("qryBtm",beginTime);
            params.put("qryEtm",endTime);

            log.info("调用中交获取车辆历史轨迹参数：{}",JSON.toJSONString(params));
            String vehicleLocationResultStr = HttpClientUtils.doPostWithToken(zjProperties.getUrl() + ZJConst.CAR_LOCUS_URL, JSON.toJSONString(params), token);
            log.info("调用中交获取车辆历史轨迹返回结果：{}",vehicleLocationResultStr);
            JSONObject jsonObject = JSON.parseObject(vehicleLocationResultStr);
            if(Objects.nonNull(jsonObject)){
                log.info("调用中交获取车辆历史轨迹返回结果code:{},msg:{}",jsonObject.get("code"),jsonObject.get("msg"));
                if (jsonObject.get("code") != null && 0 == Integer.parseInt(jsonObject.get("code").toString())) {
                    return JSON.parseObject(jsonObject.getString("data"), ZJHistoryTrackResult.class);
                }else {
                    log.info("调用中交获取车辆历史轨迹结果异常code:{},msg:{}",jsonObject.get("code"),jsonObject.get("msg"));
                    return null;
                }
            }else {
                log.error("调用中交获取车辆历史轨迹返回结果为空");
                return null;
            }
        }catch (Exception e){
            log.error("调用中交获取车辆历史轨迹异常",e);
            return null;
        }
    }



    /**
     * 判断给定的时间字符串是否在近6个月的自然月内。
     *
     * @param timeStr 时间字符串
     * @return 是否在近6个月的自然月内  true 在，false 不在
     */
    private boolean isWithinSixMonths(String timeStr) {
        // 将字符串转换为LocalDate对象
        LocalDate time = LocalDate.parse(timeStr, DATE_TIME_FORMATTER);

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算两个日期之间的月份数
        Period period = Period.between(time, currentDate);

        // 判断是否在近6个月的自然月内
        return period.getMonths() < 6 && period.getYears() == 0;
    }

    /**
     * 判断给定的时间字符串是否大于当前时间，如果大于，更新为当前时间。
     *
     * @param timeStr 时间字符串
     * @return 更新后的时间字符串
     */
    private String updateEndTimeIfGreaterThanCurrentTime(String timeStr) {
        // 将字符串转换为LocalDateTime对象
        LocalDateTime time = LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);

        // 获取当前日期时间
        LocalDateTime currentTime = LocalDateTime.now();

        // 判断结束时间是否大于当前时间
        if (time.isAfter(currentTime)) {
            // 更新结束时间为当前时间
            time = currentTime.plusSeconds(-1);
        }

        // 将更新后的时间转换回字符串
        return time.format(DATE_TIME_FORMATTER);
    }


    /**
     * 判断给定的时间字符串是否大于当前时间，true：大于  false：小于
     *
     * @param timeStr 时间字符串
     * @return 更新后的时间字符串
     */
    private boolean timeIfGreaterThanCurrentTime(String timeStr) {
        // 将字符串转换为LocalDateTime对象
        LocalDateTime time = LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);

        // 获取当前日期时间
        LocalDateTime currentTime = LocalDateTime.now();

        // 判断结束时间是否大于当前时间
        return time.isAfter(currentTime);
    }


    /**
     * 获取近n个自然月的日期，处理无效日期比如 2月31日，则返回2月28日
     *
     */
    private String getEarliestDateInLastMonths() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 减去months个月
        LocalDate sixMonthsAgo = currentDate.minusMonths(6);

        // 确保日期的有效性
        int dayOfMonth = Math.min(currentDate.getDayOfMonth(), sixMonthsAgo.lengthOfMonth());
        LocalDate validSixMonthsAgo = sixMonthsAgo.withDayOfMonth(dayOfMonth);

        // 将日期转换为 LocalDateTime，并设置时间晚于当前时间1s,保证在近6个自然月以内
        LocalDateTime sixMonthsAgoDateTime = validSixMonthsAgo.atTime(LocalTime.now().plusSeconds(1));

        // 格式化日期时间字符串
        return sixMonthsAgoDateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 判断第一个日期时间是否早于或等于第二个日期时间。
     *
     * @param date1Str 第一个日期时间字符串
     * @param date2Str 第二个日期时间字符串
     * @return 第一个日期时间是否早于或等于第二个日期时间  true：早于或等于，false：晚于
     */
    private boolean isDate1BeforeOrEqualDate2(String date1Str, String date2Str) {
        try {
            // 解析日期时间字符串
            LocalDateTime date1 = LocalDateTime.parse(date1Str, DATE_TIME_FORMATTER);
            LocalDateTime date2 = LocalDateTime.parse(date2Str, DATE_TIME_FORMATTER);

            // 比较日期时间
            return !date1.isAfter(date2);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date time format. Expected format: yyyy-MM-dd HH:mm:ss", e);
        }
    }

}
