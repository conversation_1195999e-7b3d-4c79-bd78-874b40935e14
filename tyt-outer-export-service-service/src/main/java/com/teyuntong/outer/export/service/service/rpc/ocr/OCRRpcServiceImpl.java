package com.teyuntong.outer.export.service.service.rpc.ocr;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.teyuntong.outer.export.service.client.ocr.service.OCRRpcService;
import com.teyuntong.outer.export.service.client.ocr.vo.BusinessLicenseOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportBackOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportQuaCertOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.VehicleLicenseDeputyPageBackOcrRpcVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupOcrService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.BusinessLicenseOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQualificationCertificateOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VehicleLicenseDeputyPageBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.ocr.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开票RPC实现类
 *
 * <AUTHOR>
 * @since 2024-4-3 11:36:13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class OCRRpcServiceImpl implements OCRRpcService {

    private final GroupOcrService groupOcrService;

    @Override
    public RoadTransportBackOcrRpcVO roadTransportBackOcr(String url) {
        RoadTransportBackOcrVO result = groupOcrService.roadTransportBackOcr(url);
        return BeanUtil.copyProperties(result, RoadTransportBackOcrRpcVO.class);
    }

    @Override
    public VehicleLicenseDeputyPageBackOcrRpcVO vehicleLicenseDeputyPageBackOcr(String url) {
        VehicleLicenseDeputyPageBackOcrVO result = groupOcrService.vehicleLicenseDeputyPageBackOcr(url);
        return BeanUtil.copyProperties(result, VehicleLicenseDeputyPageBackOcrRpcVO.class);
    }

    @Override
    public RoadTransportQuaCertOcrRpcVO roadTransportQualificationCertificateOcr(String url) {
        RoadTransportQualificationCertificateOcrVO result = groupOcrService.roadTransportQualificationCertificateOcr(url);
        return BeanUtil.copyProperties(result, RoadTransportQuaCertOcrRpcVO.class);
    }

    @Override
    public RoadTransportQuaCertOcrRpcVO roadTransportQualificationCertificateBackOcr(String url) {
        RoadTransportQualificationCertificateOcrVO result = groupOcrService.roadTransportQualificationCertificateBackOcr(url);
        return BeanUtil.copyProperties(result, RoadTransportQuaCertOcrRpcVO.class);
    }

    @Override
    public BusinessLicenseOcrRpcVO businessLicenseOcr(String url) {
        BusinessLicenseOcrVO result = groupOcrService.businessLicenseOcr(url);
        BusinessLicenseOcrRpcVO businessLicenseOcrRpcVO = BeanUtil.copyProperties(result, BusinessLicenseOcrRpcVO.class);
        if (ObjectUtil.isNotNull(result)) {
            if (StringUtils.isNotBlank(result.getPeriod()) && result.getPeriod().contains("至")) {
                businessLicenseOcrRpcVO.setStartTime(result.getPeriod().substring(0, result.getPeriod().indexOf("至")));
                businessLicenseOcrRpcVO.setEndTime(result.getPeriod().substring(result.getPeriod().indexOf("至") + 1));
            }
        }
        return businessLicenseOcrRpcVO;
    }
}
