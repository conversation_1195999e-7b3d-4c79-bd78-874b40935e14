package com.teyuntong.outer.export.service.service.rpc.gaode;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.gaode.service.GaoDeMapRpcService;
import com.teyuntong.outer.export.service.client.gaode.vo.GaoDeRegeoVo;
import com.teyuntong.outer.export.service.service.biz.gaode.service.GaoDeMapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * cticloud task相关
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GaoDeMapRpcServiceImpl implements GaoDeMapRpcService {

    @Autowired
    private GaoDeMapService gaoDeMapService;

    @Override
    public GaoDeRegeoVo regeoAddress(String longitude, String latitude) {
        return gaoDeMapService.regeoAddress(longitude, latitude);
    }

    /**
     * 高德货车导航
     */
    @Override
    public String navigationTruck(DistanceRpcDTO dto) {
        return gaoDeMapService.navigateTruck(dto);
    }

}
