package com.teyuntong.outer.export.service.service.biz.megvii;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:27
 */
@Component
@RequiredArgsConstructor
public class FaceIdRetrofitSupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;

    @Override
    public Retrofit getRetrofit() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(3000))
                .readTimeout(Duration.ofMillis(3000))
                .build();

        return new Retrofit.Builder()
                .baseUrl("https://api.megvii.com/")
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(okHttpClient)
                .build();
    }
}