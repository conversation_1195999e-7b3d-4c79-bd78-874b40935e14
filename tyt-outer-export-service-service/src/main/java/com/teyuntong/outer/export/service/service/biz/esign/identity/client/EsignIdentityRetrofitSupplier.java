package com.teyuntong.outer.export.service.service.biz.esign.identity.client;

import cn.hutool.crypto.digest.MD5;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import com.teyuntong.outer.export.service.service.common.property.EsignSignProperty;
import com.teyuntong.outer.export.service.service.common.utils.RetrofitUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(EsignSignProperty.class)
public class EsignIdentityRetrofitSupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;
    private final EsignSignProperty esignSignProperty;

    private final Interceptor signInterceptor = new Interceptor() {

        private static final String AUTH_MODE = "X-Tsign-Open-Auth-Mode";
        private static final String APP_ID = "X-Tsign-Open-App-Id";
        private static final String CA_SIGNATURE = "X-Tsign-Open-Ca-Signature";
        private static final String CA_TIMESTAMP = "X-Tsign-Open-Ca-Timestamp";
        private static final String CONTENT_MD5 = "Content-MD5";
        private static final String SIGNATURE_HEADERS = "X-Tsign-open-Ca-Signature-Headers";

        private String getContentType(Request request){

            String contentType = request.header(HttpHeaders.CONTENT_TYPE);

            if(StringUtils.isBlank(contentType)){
                RequestBody requestBody = request.body();
                if(requestBody != null){
                    contentType = requestBody.contentType().toString();
                }
            }
            return contentType;
        }

        @NotNull
        @Override
        public Response intercept(@NotNull Chain chain) throws IOException {
            Request request = chain.request();

            // 临时接了一部分，没细接，只能保证 application/json + body 传参能调通
            if (!needSign(request)) {
                // 只处理 application/json
                return chain.proceed(request);
            }

            String contentType = this.getContentType(request);

            RequestBody requestBody = request.body();

            String contentMD5 = "";

            if (requestBody != null) {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                contentMD5 = Base64.getEncoder().encodeToString(MD5.create().digest(buffer.readByteArray()));
            }

            StringJoiner stringJoiner = new StringJoiner("\n");
            stringJoiner.add(request.method().toUpperCase())//HTTPMethod
                    .add("*/*")//Accept
                    .add(contentMD5)//Content-MD5
                    .add(contentType)//Content-Type
                    .add("")//Date
                    .add(APP_ID + ":" + esignSignProperty.getProjectId())//Headers
                    .add(request.url().encodedPath());//

            Request newRequest = request.newBuilder()
                    .header(AUTH_MODE, "Signature")
                    .header(APP_ID, esignSignProperty.getProjectId())
                    .header(CA_SIGNATURE, sign(stringJoiner.toString()))
                    .header(CONTENT_MD5, contentMD5)
                    //.header(CONTENT_MD5, contentMD5)
                    .header(CA_TIMESTAMP, String.valueOf(System.currentTimeMillis()))
                    .header("Accept", "*/*")
                    .header(SIGNATURE_HEADERS, APP_ID)
                    .build();

            return chain.proceed(newRequest);
        }

        private boolean needSign(Request request) {
            if ("get".equalsIgnoreCase(request.method())) {
                return true;
            }

            RequestBody requestBody = request.body();
            return requestBody != null && RetrofitUtils.isApplicationJsonMediaType(requestBody.contentType());
        }

        @SneakyThrows
        private String sign(String stringToSign) {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes = esignSignProperty.getProjectSecret().getBytes(StandardCharsets.UTF_8);
            byte[] messageBytes = stringToSign.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
            // 使用HmacSHA256对二进制数据消息Bytes计算摘要
            byte[] digestBytes = hmacSha256.doFinal(messageBytes);
            // 把摘要后的结果digestBytes使用Base64进行编码
            return Base64.getEncoder().encodeToString(digestBytes);
        }
    };

    @Override
    public Retrofit getRetrofit() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(signInterceptor)
                .connectTimeout(Duration.ofMillis(15000))
                .readTimeout(Duration.ofMillis(15000))
                .build();

        return new Retrofit.Builder()
                .baseUrl(esignSignProperty.getBaseUrl())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(okHttpClient)
                .build();
    }
}
