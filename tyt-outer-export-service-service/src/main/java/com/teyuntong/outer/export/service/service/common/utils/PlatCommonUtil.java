package com.teyuntong.outer.export.service.service.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.util.TypeUtils;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.model.CustomErrorCode;
import com.teyuntong.outer.export.service.client.model.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/2/26 16:24
 */
@Slf4j
public class PlatCommonUtil {

    /** 项目根目录 **/
    private volatile static String PROJECT_ROOT_PATH;

    /**
     * 生成随机数，包含起始
     *
     * @param min
     * @param max
     * @return
     */
    public static int getRundomNumber(int min, int max) {
        if(max < min){
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        Random random = new Random();

        int result = random.nextInt(max - min + 1) + min;

        return result;
    }

    /**
     * 将文本写入文件
     *
     * @param path
     * @param content
     * @param append
     */
    public static void writeFileContent(String path, String content, boolean append) {

        if(content == null){
            return;
        }

        createFileFolder(path);

        FileWriter fw = null;
        BufferedWriter writer = null;
        try {
            fw = new FileWriter(path, append);
            writer = new BufferedWriter(fw);

            writer.write(content);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            log.error("", e);
        } finally {
            try {
                writer.close();
                fw.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("", e);
            }
        }
    }

    /**
     * 将二进制转换成base64字符串格式
     *
     * @param byteData
     * @return
     */
    public static String encodeBase64(byte[] byteData) {
        String result = Base64.encodeBase64String(byteData);
        return result;
    }

    /**
     * base64 格式
     *
     * @param baseText
     * @return
     */
    public static byte[] decodeBase64(String baseText) {
        byte[] data = Base64.decodeBase64(baseText);
        return data;
    }

    /**
     * 创建文件包括目录
     *
     * @param filePath
     * @return
     * @throws IOException
     */
    public static void createFileFolder(String filePath) {

        File parentFile = new File(filePath).getParentFile();

        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
    }

    /**
     * 是否都为空
     *
     * @param objArray
     * @return
     */
    public static boolean isAllNull(Object... objArray) {
        boolean result = true;

        for (Object obj : objArray) {

            if (obj != null) {
                result = false;

                break;
            }

        }
        return result;
    }

    /**
     * 是否包含空
     *
     * @param objArray
     * @return
     */
    public static boolean hasNull(Object... objArray) {
        boolean result = false;

        for (Object obj : objArray) {

            if (obj == null) {
                result = true;

                break;
            }

        }
        return result;
    }

    /**
     * 类字段赋值验证
     *
     * @param clazz
     * @param fieldName
     * @param fieldValue
     * @return
     */
    public static boolean fastJsonValidate(Class clazz, String fieldName, Object fieldValue) {

        boolean flag = true;

        if (fieldValue != null) {

            try {
                Field field = clazz.getDeclaredField(fieldName);

                if (field != null) {

                    Class typeClazz = field.getType();

                    if (typeClazz != null) {
                        if (typeClazz.isAssignableFrom(Date.class)) {
                            //nothing to do

                        } else if (typeClazz.isAssignableFrom(Boolean.class)) {
                            TypeUtils.castToBoolean(fieldValue);

                        } else if (typeClazz.isAssignableFrom(String.class)) {
                            TypeUtils.castToString(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Double.class)) {
                            TypeUtils.castToDouble(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Float.class)) {
                            TypeUtils.castToFloat(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Byte.class)) {
                            TypeUtils.castToByte(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Short.class)) {
                            TypeUtils.castToShort(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Integer.class)) {
                            TypeUtils.castToInt(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Long.class)) {
                            TypeUtils.castToLong(fieldValue);

                        } else {
                            ResponseCode responseCode = CustomErrorCode.easyInfo(CommonErrorCode.ERROR_PARAMETER_ERROR, "字段类型错误");
                            throw BusinessException.createException(responseCode);
                        }
                    }

                }
            } catch (Exception e) {
                log.info("Validate Error : ", e);
                flag = false;
            }

        }
        return flag;
    }

    /**
     * 校验正则表达式(全校验)
     * @param regex
     * @param content
     * @return
     */
    public static boolean regMatch(String regex, String content){
        boolean isMatch = Pattern.matches(regex, content);
        return isMatch;
    }

    /**
     * 正则校验非全匹配
     *
     * @param reg
     * @param text
     * @return
     */
    public static boolean regFind(String reg, String text) {
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(text);

        boolean result = matcher.find();
        return result;
    }

    /**
     * 用冒号(:)拼接 redis key
     *
     * @param strArray
     * @return
     */
    public static String joinRedisKey(String... strArray) {
        StringBuilder builder = new StringBuilder();

        int i = 0;
        boolean endwithSplit = false;
        for (String onePart : strArray) {

            if(StringUtils.isBlank(onePart)){
                continue;
            }

            if (i > 0 && !endwithSplit) {
                builder.append(":");
            }

            builder.append(onePart);

            if (onePart.endsWith(":")) {
                endwithSplit = true;
            } else {
                endwithSplit = false;
            }

            i++;
        }

        return builder.toString();
    }

    /**
     * 生成uuid
     *
     * @param nonSplit 是否过滤分隔符
     * @return
     */
    public static String getUUID(boolean nonSplit) {
        String uuid = UUID.randomUUID().toString();

        if (nonSplit) {
            uuid = uuid.replaceAll("-", "");
        }

        return uuid;
    }

    /**
     * 拼接文件路径
     * 统一用 Linx 路径分隔符
     *
     * @param partArray
     * @return
     */
    public static String joinPath(String... partArray) {
        String fullPath = null;

        if (partArray != null) {
            fullPath = "";

            for (String onePart : partArray) {
                if (StringUtils.isNotBlank(onePart)) {

                    onePart = onePart.replace('\\', '/');

                    if (fullPath.length() > 0) {

                        if(onePart.startsWith("/")){
                            onePart = onePart.replaceAll("^/+", "");
                        }
                    }
                    if(StringUtils.isNotBlank(onePart)){
                        if (fullPath.length() > 0) {
                            if (!fullPath.endsWith("/")) {
                                fullPath = fullPath + "/";
                            }
                        }
                        fullPath = fullPath + onePart;
                    }
                }
            }
        }

        return fullPath;
    }

    /**
     * 将对象转成map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> result = null;

        if (obj != null) {
            String json = JSON.toJSONString(obj);

            result = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
        }

        return result;
    }

    /**
     * 解析url参数
     * @param url
     * @return
     */
    public static Map<String, String> getUrlParameterMap(String url) {

        String params = url.substring(url.indexOf("?") + 1);

        String[] keyValueArray = params.split("&");

        Map<String, String> paramMap = new HashMap<>();

        for(String oneKeyValue : keyValueArray){
            if(StringUtils.isNotBlank(oneKeyValue)) {

                int eqIndex = oneKeyValue.indexOf("=");

                if(eqIndex > -1) {
                    String oneKey = oneKeyValue.substring(0, eqIndex);
                    String oneValue = oneKeyValue.substring(eqIndex + 1);

                    try {
                        oneKey = URLDecoder.decode(oneKey, "UTF-8");
                        oneValue = URLDecoder.decode(oneValue, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        log.error("", e);
                        throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
                    }

                    paramMap.put(oneKey, oneValue);
                }
            }
        }

        return paramMap;
    }

    /**
     * 替换url 参数
     * @param url
     * @param key
     * @param value
     * @return
     */
    public static String replaceUrlParameter(String url, String key, String value){

        int indexStart = url.indexOf(key + "=");

        if(indexStart <= -1){
            return url;
        }

        int indexEnd = url.indexOf("&", indexStart);

        if(indexEnd <= -1){
            indexEnd = url.length();
        }

        try {
            value = URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("", e);
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        String urlBefore = url.substring(0, indexStart);

        String urlMiddle = key + "=" + value;

        String urlAfter = url.substring(indexEnd);

        String urlResult = urlBefore + urlMiddle + urlAfter;

        return urlResult;
    }

    /**
     * 是否全部为真
     * @param flags
     * @return
     */
    public static boolean allTrue(Boolean ... flags){

        boolean booleanResult = true;

        if(flags != null) {
            for (Boolean oneBolean : flags) {
                if(oneBolean == null || !oneBolean){
                    booleanResult = false;
                    break;
                }
            }
        }

        return booleanResult;
    }

    public static String getStringValue(String value, String defaultValue){
        if(value != null){
            return value;
        }else{
            return defaultValue;
        }
    }

    /**
     * 打印消耗时间
     * @param remark
     * @param startTime
     */
    public static void printSpendTime(String remark, long startTime){

        long endTime = System.currentTimeMillis();

        long spend = (endTime - startTime);

        log.info("#### spend_time_{} : spend : [{}] ms!", remark, spend);

    }

    /**
     * 循环休眠，测试用，正式不建议使用
     * @param millis
     * @param loopCount
     */
    public static void loopSleep(long millis, int loopCount, String msg){

        for(int i= loopCount; i > 0 ; i--){

            log.info(msg + " ==== 休眠 {} 毫秒，剩余 {} 次!", millis, i);

            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 防止出发报警.
     * @param remark remark
     * @param e e
     */
    public static void printErrorInfo(String remark, Exception e){
        if(remark == null){
            remark = "";
        }

        if(e instanceof BusinessException){
            BusinessException tytException = (BusinessException) e;
            log.warn(remark + " " + tytException.getMessage());
        }else {
            log.error(remark, e);
        }
    }

    /**
     * 打印日志，内容过长时会截取
     * @param label label
     * @param info info
     */
    public static void printFixLog(String label, Object info){
        String logText = "";

        if(info != null){
            if(info instanceof String){
                logText = (String) info;
            }else {
                logText = JSON.toJSONString(info);
            }

        }

        if (logText != null && logText.length() > 2000) {
            logText = logText.substring(0, 2000 - 1) + "...skipping...";
        }

        log.info(label + " : {}", logText);
    }

    public static void main(String[] args) {

        printFixLog("aaabb", "bbbcc");

        System.out.println("finished ... ");

    }

}
