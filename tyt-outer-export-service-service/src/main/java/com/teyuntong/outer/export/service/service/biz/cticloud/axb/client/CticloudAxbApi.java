package com.teyuntong.outer.export.service.service.biz.cticloud.axb.client;

import com.teyuntong.outer.export.service.client.cticloud.axb.vo.CDRsResp;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/9 下午3:41
 */
public interface CticloudAxbApi {

    /**
     * Axb绑定接口
     * <p>
     * API URL
     * 接口请求地址：
     * https://api-{region}.cticloud.cn/interface/{version}/axb/bind
     * 地址参数说明：
     * {region}为cticloud平台区域，{region}=1 表示华东1区，{region}=2 表示华北2区，{region}=5 表示华东5区，{region}=6 表示华北6区
     * {version}为接口版本，当前{version}=v10
     * 接口请求方式：
     * GET/POST请求,Content type: application/x-www-form-urlencoded
     *
     * @param telA       必传   电话号码A	手机号不加0，订购关系中的号码A
     * @param telB       必传   电话号码B	手机号不加0，订购关系中的号码B
     * @param telX       非必传    虚拟号码   客户可指定绑定的小号，若传该参数，则无需传areaCode
     * @param expiration 非必传 有效期	单位为秒，不传默认为0：永久有效
     * @param options    其他可选参数
     *                   areacode	否	String	所需虚拟号码X的所在区号	区号去掉0，如北京：10，上海：21 ，深圳：775，广州：20，若传telX，则无需传该参数
     *                   <p>
     *                   callrecording	否	String	是否需要录音	0：不需要录音 1：需要录音 2：彩铃录音，不传默认为0
     *                   <p>
     *                   anucode	否	String	放音编码	放音编码必须包含 3 个场景的编 码。按照“A->X,B->X,其他号码 ->X”的顺序填写编码，编码之间
     *                   以逗号分隔。比如:“1,2,3”表示 A->X 放音编 号为 1， B->X 放音编号为 2， 其 他号码->X 放音编号为
     *                   3。放音编码由平台生成，编号取值0-255；格式要求：8khz,16bit 单声道 A-law，wav或pcm格式，不超过7秒运营商支持
     *                   <p>
     *                   calldisplay	否	String	来显控制	针对AXB中的A或者B 作为主叫时，是否在被叫上显示 来话的真实号码。默认为 0(不 显示真实号码)
     *                   。0:不显示真实号码1:显示真实号码来显控制按照“A->B 时 B 上的 显示,B(N)->A 时在 A 上的显示” 的顺序填写编码，编码之间以逗 号分隔。
     *                   <p>
     *                   userField	否	String	自定义字段	自定义字段
     *                   <p>
     *                   remark	否	String	自定义字段	运营商支持
     *                   <p>
     *                   assignNumber	否	String	telX选择	当telX号码与areacode都不传时，使用assignNumber参数相对应的区号
     *                   0:使用A号码区号，1:使用B号码区号
     *                   <p>
     *                   requestId	否	String	请求id	请求id
     * @return
     */
    @GET("axb/bind")
    Call<CticloudResp<AXBBindResp>> axbBind(@Query("telA") String telA,
                                            @Query("telB") String telB,
                                            @Query("telX") String telX,
                                            @Query("expiration") String expiration,
                                            @QueryMap Map<String, String> options
    );

    /**
     * Axb解绑接口
     * <p>
     * API URL
     * 接口请求地址：
     * https://api-{region}.cticloud.cn/interface/{version}/axb/unBind
     * 地址参数说明：
     * {region}为cticloud平台区域，{region}=1 表示华东1区，{region}=2 表示华北2区，{region}=5 表示华东5区，{region}=6 表示华北6区
     * {version}为接口版本，当前{version}=v10
     * 接口请求方式：
     * GET/POST请求,Content type: application/x-www-form-urlencoded
     *
     * @param subId     必传  String	绑定关系唯一标识	绑定成功时由天润接口返回
     * @param requestId 非必传  String	请求id	请求id
     * @param decCalls  非必传  Integer	是否减对应A号码中继组并发(A路调度的A号码才生效)	1:减并发
     * @return
     */
    @GET("axb/unBind")
    Call<CticloudResp<Object>> axbUnbind(@Query("subId") String subId,
                                         @Query("requestId") String requestId,
                                         @Query("decCalls") String decCalls);

    /**
     * Axb更新绑定接口
     * <p>
     * 接口说明
     * <p>
     * API URL
     * 接口请求地址：
     * https://api-{region}.cticloud.cn/interface/{version}/axb/updateBind
     * 地址参数说明：
     * {region}为cticloud平台区域，{region}=1 表示华东1区，{region}=2 表示华北2区，{region}=5 表示华东5区，{region}=6 表示华北6区
     * {version}为接口版本，当前{version}=v10
     * 接口请求方式：
     * GET/POST请求,Content type: application/x-www-form-urlencoded
     *
     * @param subId      必传 subId
     * @param telA       非必传 telA、telB 不能同时更新
     * @param telB       非必传 telA、telB 不能同时更新
     * @param expiration 非必传 有效期
     * @param options    其他可选参数
     *                   <p>
     *                   callrecording	否	String	是否需要录音	0：不需要录音 1：需要录音 2：彩铃录音，不传默认为0
     *                   <p>
     *                   anucode	否	String	放音编码	放音编码必须包含 3 个场景的编 码。按照“A->X,B->X,其他号码 ->X”的顺序填写编码，编码之间
     *                   以逗号分隔。比如:“1,2,3”表示 A->X 放音编 号为 1， B->X 放音编号为 2， 其 他号码->X 放音编号为
     *                   3。放音编码由平台生成，编号取值0-255；格式要求：8khz,16bit 单声道 A-law，wav或pcm格式，不超过7秒运营商支持
     *                   <p>
     *                   calldisplay	否	String	来显控制	针对AXB中的A或者B 作为主叫时，是否在被叫上显示 来话的真实号码。默认为 0(不 显示真实号码)。0:不显示真实号码
     *                   1:显示真实号码来显控制按照“A->B 时 B 上的 显示,B(N)->A 时在 A 上的显示” 的顺序填写编码，编码之间以逗 号分隔。
     *                   <p>
     *                   userField	否	String	自定义字段	自定义字段
     *                   <p>
     *                   remark	否	String	自定义字段	运营商支持
     *                   <p>
     *                   assignNumber	否	String	telX选择	当telX号码与areacode都不传时，使用assignNumber参数相对应的区号
     *                   0:使用A号码区号，1:使用B号码区号
     *                   <p>
     *                   requestId	否	String	请求id	请求id
     * @return
     */
    @GET("axb/updateBind")
    Call<CticloudResp<Object>> axbUpdateBind(@Query("subId") String subId,
                                             @Query("telA") String telA,
                                             @Query("telB") String telB,
                                             @Query("expiration") String expiration,
                                             @QueryMap Map<String, String> options);

    @POST("vnc/call?action=queryCdr")
    Call<CticloudResp<CDRsResp>> vncCallQueryCdr(@Body Map<String, Object> body);

    @POST("vnc/call?action=queryRecord")
    Call<CticloudResp<String>> record(@Body Map<String, Object> body);

    @POST("vnc/call?action=asrCreate")
    Call<CticloudResp<String>> asrCreate(@Body Map<String, Object> body);

    @POST("vnc/call?action=queryCdrAsr")
    Call<CticloudResp<ASRResp>> queryCdrAsr(@Body Map<String, Object> body);

}
