package com.teyuntong.outer.export.service.service.biz.megvii.v3.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.service.biz.megvii.FaceIdRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.megvii.FaceIdSignInterceptor;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.MegviiBizTokenV3Resp;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.MegviiVerifyV3Resp;
import okhttp3.MultipartBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:16
 */
@RetrofitClient(value = FaceIdRetrofitSupplier.class, path = "faceid/v3/sdk/")
@UseInterceptor(FaceIdSignInterceptor.class)
@UseInterceptor(LogInfoInterceptor.class)
public interface FaceIdV3Api {

    /**
     * 获取 token
     * <p>
     * <a href="https://faceid.com/document/faceid-guide-docs/app_api_product_analyze">旷世接口文档地址</a>
     */
    @POST("get_biz_token")
    Call<MegviiBizTokenV3Resp> getBizToken(@Body MultipartBody data);

    /**
     * 比对认证
     * <p>
     * <a href="https://faceid.com/document/faceid-guide-docs/app_api_get_result">旷世接口文档地址</a>
     */
    @POST("verify")
    Call<MegviiVerifyV3Resp> verify(@Body MultipartBody data);
}
