package com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("phone_number_attribution")
public class PhoneNumberAttribution implements Serializable {
    /**
     * 自增ID,无意义
     */
    @TableId
    private Long id;

    /**
     * 手机号
     */
    @TableField("cell_phone")
    private String cellPhone;

    /**
     * 号段
     */
    private String num;

    /**
     * 省
     */
    private String prov;

    /**
     * 市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 号码类型
     */
    private String types;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 行政编码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 邮政编码
     */
    @TableField("zip_code")
    private String zipCode;

    /**
     * 响应结果code
     */
    private Integer code;

    /**
     * 响应信息
     */
    private String msg;

    /**
     * 状态: 1-成功 2-失败
     */
    private Integer status;

    /**
     * 版本:aliyun baidu ..
     */
    private String version;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    private static final long serialVersionUID = 1L;
}