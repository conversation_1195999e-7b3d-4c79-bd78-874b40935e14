package com.teyuntong.outer.export.service.service.biz.volcengine.client;

import com.volcengine.service.businessSecurity.BusinessSecurityService;
import com.volcengine.service.businessSecurity.impl.BusinessSecurityServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/01/10 15:51
 */
@Configuration
@EnableConfigurationProperties(VolcengineProperties.class)
public class VolcengineConfig {

    @Bean
    public BusinessSecurityService businessSecurityService(VolcengineProperties volcengineProperties) {

        VolcengineProperties.AppConfig businessSecurity = volcengineProperties.getBusinessSecurity();

        BusinessSecurityService businessSecurityService = BusinessSecurityServiceImpl.getInstance();
        businessSecurityService.setAccessKey(businessSecurity.getAccessKey());
        businessSecurityService.setSecretKey(businessSecurity.getSecretKey());
        return businessSecurityService;
    }
}
