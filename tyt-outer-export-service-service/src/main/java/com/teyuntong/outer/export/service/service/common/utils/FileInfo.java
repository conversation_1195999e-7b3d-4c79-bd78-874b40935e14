package com.teyuntong.outer.export.service.service.common.utils;

import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/12/18 10:36
 */
@Data
public class FileInfo {

    /**
     * 文件名称，包括后缀
     */
    private String fileName;

    /**
     * 文件名称，不包括后缀
     */
    private String nameOnly;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 文件字节流
     */
    private byte[] dataBytes;

    public FileInfo(String fileName, String nameOnly, String suffix) {
        this.fileName = fileName;
        this.nameOnly = nameOnly;
        this.suffix = suffix;
    }
}
