package com.teyuntong.outer.export.service.service.rpc.enterprise;

import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.manbang.api.enterprise.bean.CompanyRealNameReq;
import com.teyuntong.outer.export.service.client.manbang.vo.EnterpriseRealNameStatusVo;
import com.teyuntong.outer.export.service.client.model.FileDataInfo;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.OutEnterpriseRealService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 企业实名相关Controller
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/out/enterpriseReal")
public class OutEnterpriseRealController {

    @Autowired
    private OutEnterpriseRealService outEnterpriseRealService;

    /**
     * 上传文件
     */
    @PostMapping("/uploadFile")
    public String uploadFile(@RequestBody FileDataInfo fileDataInfo) {

        if(fileDataInfo == null){
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        String fileUrl = outEnterpriseRealService.uploadFile(fileDataInfo);

        return fileUrl;
    }

    /**
     * 提交企业实名认证
     */
    @PostMapping("/companyRealName")
    public void companyRealName(@Validated @RequestBody CompanyRealNameReq realNameReq) {

        if(realNameReq == null){
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        outEnterpriseRealService.companyRealName(realNameReq);
    }

    /**
     * 提交企业实名认证
     */
    @PostMapping("/getRealNameStatus")
    public EnterpriseRealNameStatusVo getRealNameStatus(String identityNo) {

        if(StringUtils.isBlank(identityNo)){
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        EnterpriseRealNameStatusVo realNameStatus = outEnterpriseRealService.getRealNameStatus(identityNo);
        return realNameStatus;
    }
}
