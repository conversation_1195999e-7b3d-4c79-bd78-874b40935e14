package com.teyuntong.outer.export.service.service.rpc.zj;

import com.teyuntong.outer.export.service.client.zj.service.CarLocationRpcService;
import com.teyuntong.outer.export.service.client.zj.vo.*;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.*;
import com.teyuntong.outer.export.service.service.biz.zj.service.CarLocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/06 11:22
 */
@RestController
@RequiredArgsConstructor
public class CarLocationRpcServiceImpl implements CarLocationRpcService {


    private final CarLocationService carLocationService;

    /**
     * 获取车辆实时位置
     * @param carHeadNo 车牌号，多个车牌号 英文逗号隔开 ,
     * @param color 车牌颜色 1 蓝色、2 黄色、3 黄绿色  可选参数，默认 2
     */
    @Override
    public ZJVehicleLocationResultVO getCarRealTimeLocation(String carHeadNo, String color) {
        ZJVehicleLocationResult zjVehicleLocationResult = carLocationService.getCarRealTimeLocation(carHeadNo, color);
        return convertZJVehicleLocationResultVO(zjVehicleLocationResult);

    }

    /**
     * 查询车辆历史轨迹
     * @param carHeadNo 车牌号
     * @param color 车牌颜色 1 蓝色、2 黄色、3 黄绿色  可选参数，默认 2
     * @param beginTime  查询开始时间 格式：yyyy-MM-dd HH:mm:ss   近 6 个月自然月
     * @param endTime  查询结束时间  格式：yyyy-MM-dd HH:mm:ss   与开始时间相差 72小时之内
     */
    @Override
    public ZJHistoryTrackResultVO getCarLocus(String carHeadNo, String color, String beginTime, String endTime) {
        ZJHistoryTrackResult zjHistoryTrackResult = carLocationService.getCarLocus(carHeadNo, color, beginTime, endTime);
        return convertZJHistoryTrackResultVO(zjHistoryTrackResult);
    }



    private ZJVehicleLocationResultVO convertZJVehicleLocationResultVO(ZJVehicleLocationResult zjVehicleLocationResult) {
        if (zjVehicleLocationResult == null) {
            return null;
        }

        ZJVehicleLocationResultVO vo = new ZJVehicleLocationResultVO();
        vo.setFirstVcl(convertZJVehicleLocation(zjVehicleLocationResult.getFirstVcl()));

        Optional.ofNullable(zjVehicleLocationResult.getOthers())
                .ifPresent(others -> vo.setOthers(others.stream()
                        .map(this::convertZJVehicleLocation)
                        .collect(Collectors.toList())));

        return vo;
    }

    private ZJVehicleLocationVO convertZJVehicleLocation(ZJVehicleLocation location) {
        if (location == null) {
            return null;
        }

        ZJVehicleLocationVO vo = new ZJVehicleLocationVO();
        vo.setVno(location.getVno());
        vo.setCode(location.getCode());
        vo.setLon(location.getLon());
        vo.setLat(location.getLat());
        vo.setAdr(location.getAdr());
        vo.setFormattedUtc(location.getFormattedUtc());
        vo.setSpd(location.getSpd());
        vo.setDrc(location.getDrc());
        vo.setProvince(location.getProvince());
        vo.setCity(location.getCity());
        vo.setCountry(location.getCountry());
        vo.setMil(location.getMil());
        vo.setOfflineState(location.getOfflineState());
        vo.setOfflineTime(location.getOfflineTime());
        vo.setRunDistance(location.getRunDistance());
        vo.setRemainDistance(location.getRemainDistance());
        vo.setOriginalLon(location.getOriginalLon());
        vo.setOriginalLat(location.getOriginalLat());
        return vo;
    }


    private ZJHistoryTrackResultVO convertZJHistoryTrackResultVO(ZJHistoryTrackResult result) {
        if (result == null) {
            return null;
        }

        ZJHistoryTrackResultVO vo = new ZJHistoryTrackResultVO();
        vo.setMileage(result.getMileage());
        vo.setParkSize(result.getParkSize());

        // 处理 cityArray 可能为空的情况
        Optional.ofNullable(result.getCityArray())
                .ifPresent(cities -> vo.setCityArray(cities.stream()
                        .map(this::convertZJCity)
                        .collect(Collectors.toList())));

        // 处理 parkArray 可能为空的情况
        Optional.ofNullable(result.getParkArray())
                .ifPresent(parks -> vo.setParkArray(parks.stream()
                        .map(this::convertZJPark)
                        .collect(Collectors.toList())));

        // 处理 trackArray 可能为空的情况
        Optional.ofNullable(result.getTrackArray())
                .ifPresent(tracks -> vo.setTrackArray(tracks.stream()
                        .map(this::convertZJTrack)
                        .collect(Collectors.toList())));

        return vo;
    }


    private ZJCityVO convertZJCity(ZJCity city) {
        if (city == null) {
            return null;
        }
        ZJCityVO vo = new ZJCityVO();
        vo.setCityName(city.getCityName());
        vo.setCityCode(city.getCityCode());
        vo.setVInOutTime(city.getVInOutTime());
        vo.setVInOutType(city.getVInOutType());

        return vo;
    }

    private ZJParkVO convertZJPark(ZJPark park) {
        if (park == null) {
            return null;
        }

        ZJParkVO vo = new ZJParkVO();
        vo.setParkAdr(park.getParkAdr());
        vo.setParkEte(park.getParkEte());
        vo.setParkBte(park.getParkBte());
        vo.setParkLat(park.getParkLat());
        vo.setParkLon(park.getParkLon());
        vo.setParkMins(park.getParkMins());

        return vo;
    }

    private ZJTrackVO convertZJTrack(ZJTrack track) {
        if (track == null) {
            return null;
        }

        ZJTrackVO vo = new ZJTrackVO();
        vo.setAgl(track.getAgl());
        vo.setGtm(track.getGtm());
        vo.setHgt(track.getHgt());
        vo.setLat(track.getLat());
        vo.setLon(track.getLon());
        vo.setMlg(track.getMlg());
        vo.setSpd(track.getSpd());
        vo.setFormattedGtm(track.getFormattedGtm());
        vo.setOriginalLon(track.getOriginalLon());
        vo.setOriginalLat(track.getOriginalLat());

        return vo;
    }
}
