package com.teyuntong.outer.export.service.service.common.mq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "rocket-mq-old")
public class OldMqProperties {

    private String nameSrvAddr;

    private String accessKey;

    private String secretKey;

}