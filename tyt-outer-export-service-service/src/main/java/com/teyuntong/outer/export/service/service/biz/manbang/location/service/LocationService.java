package com.teyuntong.outer.export.service.service.biz.manbang.location.service;

import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LocationTraceDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17 13:41
 */
public interface LocationService {


    /**
     * 根据车牌号查询定位
     *
     */
    List<VehicleLocationVO> getVehicleLastLocation(List<LastLocationDTO> lastLocationDTOList);

    List<SinoiovVehicleLastLocationVO> getVehicleSinoiovLastLocation(List<LastLocationDTO> lastLocationDTOList);



    LocationTraceVO getLocationTrace(LocationTraceDTO locationTraceDTO);


    List<AppLastPositionVO> getAppLastPosition(List<Long> userIds);

    List<LbsAuthVO> getLbsAuth(String vehicleNo);
}
