package com.teyuntong.outer.export.service.service.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/12/15 10:57
 */
public class TytFileUtil {

    private TytFileUtil() {
    }

    /**
     * 文件名信息.
     * @param filePath 文件路径或名称
     * @return FileInfo
     */
    public static FileInfo getFileInfo(String filePath) {
        if (filePath == null) {
            return null;
        }

        File tmpFile = new File(filePath);
        String fileName = tmpFile.getName();

        String nameOnly = "";
        String suffix = "";

        int pointIndex = fileName.lastIndexOf(".");

        if (pointIndex >= 0) {
            nameOnly = fileName.substring(0, pointIndex);
            suffix = fileName.substring(pointIndex);
        } else {
            nameOnly = fileName;
        }

        FileInfo fileInfo = new FileInfo(fileName, nameOnly, suffix);

        return fileInfo;
    }

    /**
     * 生成带 uuid 的唯一文件名.
     *
     * @param fileName   文件名称
     * @param keepOrigin 是否保留原始文件名
     * @return String
     */
    public static String uuidFileName(String fileName, boolean keepOrigin) {

        if(fileName == null){
            fileName = "";
        }

        FileInfo fileInfo = getFileInfo(fileName);

        String nameOnly = fileInfo.getNameOnly();
        String suffix = fileInfo.getSuffix();

        String uuid = PlatCommonUtil.getUUID(true);

        String originFileName = nameOnly;
        if (keepOrigin && StringUtils.isNotBlank(originFileName)) {
            originFileName = originFileName + "_";
        } else {
            originFileName = "";
        }

        String fullFileName = originFileName + uuid + suffix;
        return fullFileName;
    }

    /**
     * 生成文件路径，按照日期拆分.
     * path 规则： /${env}/${app_name}/${type_name}/${yyyy}/${MM}/${dd}/${uuid_file_name}
     * 示例： http://peimages.teyuntong.net/online/invoice/import/2023/12/15/cae513bd35d5782dafe98e18ffd3b5e5.xslx
     * @return String
     */
    public static String dateFolder(){

        Date nowTime = new Date();

        String nowTimeText = DateUtil.dateToString(nowTime, DateUtil.day_format);

        String[] timeArray = nowTimeText.split("-");

        String year = timeArray[0];
        String month = timeArray[1];
        String day = timeArray[2];

        String fullPath = PlatCommonUtil.joinPath(year, month, day);

        return fullPath;
    }

    /**
     * 获得文件后缀
     *
     * @param fileName
     * @return
     */
    public static String getFileSuffix(String fileName) {
        String suffix = "";

        if (StringUtils.isBlank(fileName)) {
            return suffix;
        }

        File tmpFile = new File(fileName);

        String nameOnly = tmpFile.getName();

        int index = nameOnly.lastIndexOf(".");

        if (index >= 0) {
            suffix = nameOnly.substring(index).toLowerCase();
        }
        return suffix;
    }

    /**
     * 获取不带后缀的文件名
     *
     * @param fileName
     * @return
     */
    public static String getFileNameWithoutSuffix(String fileName) {
        String result = fileName;

        int index = fileName.lastIndexOf(".");

        if (index >= 0) {
            result = fileName.substring(0, index);
        }

        return result;
    }

    /**
     * 删除文件及下级所有文件
     *
     * @param path
     * @return
     */
    public static boolean deleteFile(String path) {
        File file = new File(path);
        //判断是否待删除目录是否存在
        if (!file.exists()) {
            return false;
        }

        if (file.isDirectory()) {
            //取得当前目录下所有文件和文件夹
            File[] content = file.listFiles();

            for (File temp : content) {
                //递归调用，删除目录里的内容
                deleteFile(temp.getAbsolutePath());
            }
        }

        file.delete();

        return true;
    }

}
