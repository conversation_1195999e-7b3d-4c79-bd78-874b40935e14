package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlOrderEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlOrderService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 订单、运单相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlOrderServiceImpl implements XhlOrderService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> saveOrder(XhlOrderEntity orderEntity) throws Exception {
        log.info("新增订单-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveOrder(orderEntity).execute();
        log.info("新增订单-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateOrder(XhlOrderEntity orderEntity) throws Exception {
        log.info("修改订单-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateOrder(orderEntity).execute();
        log.info("修改订单-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryOrder(String companyName, String tpOrderNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询订单-请求参数，appId={}，companyName={}，tpOrderNo={}", appId, companyName, tpOrderNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryOrder(appId, companyName, tpOrderNo).execute();
        log.info("查询订单-返回结果，appId={}，companyName={}，tpOrderNo={}，响应参数：{} ", appId, companyName, tpOrderNo, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> sendOrder(XhlOrderEntity orderEntity) throws Exception {
        log.info("新增派单-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.sendOrder(orderEntity).execute();
        log.info("新增派单-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> departWaybill(XhlOrderEntity orderEntity) throws Exception {
        log.info("运单发车-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.departWaybill(orderEntity).execute();
        log.info("运单发车-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> signWaybill(XhlOrderEntity orderEntity) throws Exception {
        log.info("运单签收-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.signWaybill(orderEntity).execute();
        log.info("运单签收-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> deleteWaybill(XhlOrderEntity orderEntity) throws Exception {
        log.info("运单删除-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.deleteWaybill(orderEntity).execute();
        log.info("运单删除-返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateWaybill(XhlOrderEntity orderEntity) throws Exception {
        log.info("运单修改-请求参数，orderEntity={}", orderEntity);
        if (!Objects.isNull(orderEntity)) {
            orderEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateWaybill(orderEntity).execute();
        log.info("运单修改--返回结果，orderEntity={}，响应参数：{} ", orderEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryWaybill(String companyName, String tpWaybillNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("运单查询-请求参数，appId={}，companyName={}，tpWaybillNo={}", appId, companyName, tpWaybillNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryWaybill(appId, companyName, tpWaybillNo).execute();
        log.info("运单查询-返回结果，appId={}，companyName={}，tpWaybillNo={}，响应参数：{} ", appId, companyName, tpWaybillNo, response.body());
        return response.body();
    }
}
