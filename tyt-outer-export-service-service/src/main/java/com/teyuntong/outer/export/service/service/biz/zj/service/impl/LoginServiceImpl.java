package com.teyuntong.outer.export.service.service.biz.zj.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.manbang.util.HttpClientUtils;
import com.teyuntong.outer.export.service.service.biz.zj.constant.ZJConst;
import com.teyuntong.outer.export.service.service.biz.zj.service.LoginService;
import com.teyuntong.outer.export.service.service.common.property.ZJProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/11/05 14:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties({ZJProperties.class})
public class LoginServiceImpl implements LoginService {


    private final ZJProperties zjProperties;

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public String login() {

        String token = stringRedisTemplate.opsForValue().get(ZJConst.OPEN_API_TOKEN);
        log.info("从redis中获取中交接口token:{}",token);
        if(StringUtils.isNotBlank(token)){
            return token;
        }else {
            Map<String, String> loginParam = new HashMap<>();
            loginParam.put("appId", zjProperties.getAppId());
            loginParam.put("secretKey", zjProperties.getSecretKey());
            String loginResponse = HttpClientUtils.doPost(zjProperties.getUrl() + ZJConst.LOGIN_URL, JSON.toJSONString(loginParam));
            log.info("调用中交登录接口返回结果loginResponse:{}", loginResponse);
            JSONObject jsonObject = JSON.parseObject(loginResponse);
            if(Objects.nonNull(jsonObject)){
                if (jsonObject.get("code") != null && 0 == Integer.parseInt(jsonObject.get("code").toString())) {
                    String newToken = jsonObject.getString("data");
                    stringRedisTemplate.opsForValue().set(ZJConst.OPEN_API_TOKEN,newToken, ZJConst.OPEN_API_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                    return newToken;
                }else {
                    log.error("调用中交登录接口未返回正常结果code:{},msg:{}",jsonObject.get("code"),jsonObject.get("msg"));
                    return null;
                }
            }else {
                log.error("调用中交登录接口返回结果为空");
                return null;
            }
        }


    }
}
