package com.teyuntong.outer.export.service.service.biz.manbang.config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
* 请求满帮网关配置
* <AUTHOR>
* @since 2024/4/2 17:53
*/
@Data
@ConfigurationProperties("gateway-sign")
public class GateWayConfig {
    /**
     * 钱包网关地址
     */
    private String walletAddress;
    /**
     * 商户RSA 私钥---用户商户签名使用
     */
    private String privateKey;
    /**
     * 商户号
     */
    private String partnerId;
    /**
     * 延签方式
     */
    private String signType;
    /**
     * 商户CA证书地址
     */
    private String merchantKeystore;
    /**
     * 商户CA证书密码
     */
    private String merchantStorePass;
    /**
     * 商户CA证书别名
     */
    private String merchantAlias;

    /**
     * 商户CA证书密码
     */
    private String merchantAliasPass;

    /**
     * 钱包CA证书地址
     */
    private String keystore;
    /**
     * 钱包CA证书密码
     */
    private String storePass;
    /**
     * 钱包CA证书别名
     */
    private String alias;

    /**
     * 钱包RSA公钥--用户回调通知给商户签名验证使用
     */
    private String walletPublicKey;


    /**
     * 当使用连接池管理HTTP连接时，connectionRequestTimeout指的是从连接池中获取连接的超时时间。
     * 如果连接池中没有可用的连接，并且等待获取连接的时间超过了connectionRequestTimeout，就会抛出连接请求超时异常。
     * 这个超时时间有助于确保客户端不会无限期地等待从连接池中获取连接，从而提高了应用的响应性和可靠性。
     */
    private int connectionRequestTimeout;

    /**
     * 指的是客户端与服务器建立TCP连接时的超时时间。
     * 当客户端尝试与服务器建立连接时，
     * 如果在connectionTimeout指定的时间内无法成功建立连接，就会触发连接超时异常。
     * 这通常是因为网络问题、服务器不可达或服务器响应过慢等原因造成的。
     */
    private int connectionTimeout;

    /**
     * 通常指的是整个HTTP请求的超时时间，
     * 即从发起请求到接收到完整响应的最大允许时间。
     * 如果在这个时间内没有收到响应，请求将因为超时而被中断。
     * 这个超时时间涵盖了建立连接、发送请求、接收响应等所有阶段。
     */
    private int readTimeout;

}
