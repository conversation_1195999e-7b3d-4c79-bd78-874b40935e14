package com.teyuntong.outer.export.service.service.rpc.corporate;

import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.corporate.service.MobileRpcService;
import com.teyuntong.outer.export.service.client.corporate.vo.PhoneLocale;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.biz.corporate.service.PhoneNumberAttributionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class MobileRpcServiceImpl implements MobileRpcService {
    @Autowired
    private PhoneNumberAttributionService phoneNumberAttributionService;

    @Override
    public PhoneLocale getMobile(String mobile) throws Exception {
        try {
            log.info("手机号归属地查询api，mobile={}", mobile);
            if(StringUtils.isBlank(mobile)){
                throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
            }
            return phoneNumberAttributionService.addAndGetMobileLocale(mobile);
        } catch (Exception e) {
            log.info("手机号归属地查询api失败 原因：{}", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }
    }
}
