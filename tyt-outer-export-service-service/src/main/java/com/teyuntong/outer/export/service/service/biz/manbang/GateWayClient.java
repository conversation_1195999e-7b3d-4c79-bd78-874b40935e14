package com.teyuntong.outer.export.service.service.biz.manbang;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.service.biz.manbang.config.GateWayConfig;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MagHttpClientUtil;
import com.wlqq.wallet.gateway.client.core.ServiceFactory;
import com.wlqq.wallet.gateway.client.enums.AppServiceKind;
import com.wlqq.wallet.gateway.client.enums.ErrorCode;
import com.wlqq.wallet.gateway.client.enums.ServiceKind;
import com.wlqq.wallet.gateway.client.enums.SignTypeKind;
import com.wlqq.wallet.gateway.client.exception.GateWayClientException;
import com.wlqq.wallet.gateway.client.request.BaseRequest;
import com.wlqq.wallet.gateway.client.request.mgs.FileUploadRequest;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.service.BaseService;
import com.wlqq.wallet.gateway.client.util.BuildParamUtil;
import com.wlqq.wallet.gateway.client.util.Constants;
import com.wlqq.wallet.gateway.client.util.FileInfo;
import com.wlqq.wallet.gateway.client.util.sign.CA;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.PrintWriter;
import java.util.Map;

/**
*  访问集团client
* <AUTHOR>
* @since 2024/4/2 17:00
*/
@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(GateWayConfig.class)
public class GateWayClient {

    private String address;
    private String key;
    private BaseRequest commonRequest = new BaseRequest();

    private final GateWayConfig config;

    @PostConstruct
    public void init() {
        address = config.getWalletAddress();
        key = config.getPrivateKey();
        String partnerId = config.getPartnerId();
        String signType = config.getSignType();
        if (address == null || key == null || partnerId == null || signType == null) {
            log.error("读取配置参数出错，缺少支付网关必要参数");
            throw new GateWayClientException("读取配置文件出错，缺少支付网关必要参数", ErrorCode.INIT_PARAM_ERROR);
        }
        commonRequest.setPartnerId(partnerId);
        commonRequest.setVersion(Constants.version);
        commonRequest.setInputCharset(Constants.defaultCharset);
        commonRequest.setSignType(SignTypeKind.getByCode(signType));
    }

    /**
     * 访问钱包网关接口
     * @param paramRequest 请求
     * @param serviceKind 服务名称
     * @return 请求字符串结果
     */
    public <Request extends BaseRequest> BaseResponse doService(ServiceKind serviceKind, Request paramRequest) {
        return doService(serviceKind, paramRequest, BaseResponse.class);
    }

    /**
     * 访问钱包网关调用接口
     * @param serviceKind 服务名称
     * @param paramRequest 请求对象
     * @param clazz 返回对象泛型
     * @param <Request> 基本请求对象
     * @param <Response> 基本响应对象
     * @return 返回响应结果
     */
    public <Request extends BaseRequest, Response extends BaseResponse> Response doService(ServiceKind serviceKind,
                                                                                           Request paramRequest, Class<Response> clazz) {
        BaseService<Request, Response> baseService = ServiceFactory.getService(serviceKind);
        Map<String, String> origMap = baseService.bulidRequestParam(commonRequest, paramRequest);
        origMap.put(Constants.SERVICE_NAME, serviceKind.getCode());
        origMap = bulidSign(origMap, commonRequest);

        try {
            baseService.doAfterSign(origMap, paramRequest);
        } catch (Exception e) {
            log.error("[gateway-client][doAfterSign异常][result = " + e.getMessage() + "]", e);
            throw new GateWayClientException(ErrorCode.SYSTEM_ERROR);
        }

        String charset = commonRequest.getInputCharset();
        AppServiceKind appService = serviceKind.getAppService();
        String serviceUrlName = getServiceUrl(appService);

        log.debug("<==[wallet-gateway-client]request param={}, url={},urlDesc={}", JSON.toJSONString(origMap),serviceKind.getCode(),serviceKind.getDesc());
        String httpResult =
                MagHttpClientUtil.sendRequest(origMap, charset, address + serviceUrlName, 1000000, config.getConnectionTimeout(), config.getReadTimeout());
        log.debug("<==[wallet-gateway-client]接收到服务返回result={}", httpResult);
        Response response = null;
        try {
            response = baseService.buildResonse(httpResult);
        } catch (Exception e) {
            log.error("[gateway-client][http请求异常][result = " + httpResult + "]", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        }
        return response;
    }

    /**
     * 访问钱包网关调用接口
     * @param serviceKind 服务名称
     * @param paramRequest 请求对象
     * @param clazz 返回对象泛型
     * @param <Request> 基本请求对象
     * @param <Response> 基本响应对象
     * @return 返回响应结果
     */
    public <Request extends BaseRequest, Response extends BaseResponse> Response uploadFile(ServiceKind serviceKind,
                                                                                            Request paramRequest, Class<Response> clazz) {
        BaseService<Request, Response> baseService = ServiceFactory.getService(serviceKind);
        Map<String, String> origMap = baseService.bulidRequestParam(commonRequest, paramRequest);

        Map<String, FileInfo> fileInfoMap = null;
        if (paramRequest instanceof FileUploadRequest) {
            fileInfoMap = ((FileUploadRequest) paramRequest).getFileMap();
        }
        String charset = commonRequest.getInputCharset();
        AppServiceKind appService = serviceKind.getAppService();
        String serviceUrlName = getServiceUrl(appService);
        String httpResult = MagHttpClientUtil.form(address + Constants.mgsUploadFile, origMap, fileInfoMap, null);
        log.debug("<==[wallet-gateway-client]接收到服务返回result={}", httpResult);
        Response response = null;
        try {
            response = baseService.buildResonse(httpResult);
        } catch (Exception e) {
            log.error("[gateway-client][http请求异常][result = " + httpResult + "]", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        }
        return response;
    }

    /**
     * 获取网关地址
     * @param appServiceName 网关服务类型
     * @return 网关详细地址
     */
    private String getServiceUrl(AppServiceKind appServiceName) {
        if (AppServiceKind.MAG.equals(appServiceName)) {
            return Constants.magService;
        } else if (AppServiceKind.MGS.equals(appServiceName)) {
            return Constants.mgsService;
        }
        log.error("找不到自定的服务平台{}", appServiceName);
        throw new RuntimeException("找不到自定的服务平台:" + appServiceName.name());
    }

    /**
     * 添加秘钥
     * @param param 组装后的请求参数
     * @param request 基本请求参数实体
     * @return 添加签名后的请求参数
     * @throws Exception
     */
    private Map<String, String> bulidSign(Map<String, String> param, BaseRequest request) {
        String signType = request.getSignType().getCode();
        String privateKey = null;
        try {
            if (SignTypeKind.MD5.getCode().equalsIgnoreCase(signType)) {
                privateKey = key;
            } else if (SignTypeKind.RSA.getCode().equalsIgnoreCase(signType)) {
                privateKey = key;
            } else if (SignTypeKind.CA.getCode().equalsIgnoreCase(signType)) {
                privateKey = getCAPrivateKey();
            }
            return BuildParamUtil.buildRequestPara(param, signType, privateKey, request.getInputCharset());
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }


    public String getCAPrivateKey() {
        return CA.getPrivateKey(config.getMerchantKeystore(), config.getMerchantStorePass(), config.getMerchantAlias(),
                config.getMerchantAliasPass());
    }

    public static void main(String[] args) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PrintWriter out = new PrintWriter(bos);
        out.println("钱包交易订单号,商户订单ID,订单编号,付款方账号,收款方账号,商品名称,付款金额,交易提交时间,交易时间,支付状态,交易类型");

        out.flush();
        System.out.println(new String(bos.toByteArray()));
    }


}
