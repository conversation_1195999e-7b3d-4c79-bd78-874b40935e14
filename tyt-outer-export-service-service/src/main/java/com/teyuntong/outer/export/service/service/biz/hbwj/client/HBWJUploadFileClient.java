package com.teyuntong.outer.export.service.service.biz.hbwj.client;

import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.FileUpLoadResp;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJDataResult;
import okhttp3.MultipartBody;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */

public interface HBWJUploadFileClient {

    @POST("callback/file/upload")
    @Multipart
    Call<HBWJDataResult<FileUpLoadResp>> fileUpload( @Part MultipartBody.Part file);


}
