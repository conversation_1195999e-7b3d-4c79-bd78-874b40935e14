package com.teyuntong.outer.export.service.service.biz.manbang.account.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/04/15 14:15
 */
@Data
public class VehicleLicenseVO {

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 住址
     */
    private String address;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 品牌型号
     */
    private String model;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 注册日期
     */
    private Date registerDate;

    /**
     * ocr对应的过期时间
     */
    private Date ocrExpirationDate;

    /**
     * 发证日期
     */
    private Date issueDate;

    /**
     * 档案编码
     */
    private String recordNo;

    /**
     * 核定载人数
     */
    private Integer approvedPassengers;

    /**
     * 总质量（单位KG）
     */
    private Integer grossMass;

    /**
     * 整备质量（单位KG）
     */
    private Integer unladenMass;

    /**
     * 核定载质量（单位KG）
     */
    private Integer approvedLoad;

    /**
     * 外廓尺寸
     */
    private String dimension;

    /**
     * 车长mm
     */
    private Integer dimensionLength;

    /**
     * 车宽mm
     */
    private Integer dimensionWidth;

    /**
     * 车高mm
     */
    private Integer dimensionHeight;

    /**
     * 准牵引总质量(KG）
     */
    private Integer tractionMass;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 证芯编码
     */
    private String coreCode;

    /**
     * 检查记录
     */
    private String inspectionRecord;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 行驶证副页背面图片
     * */
    private String secondBackImageUrl;

    /**
     * 整备质量，单位（kg）
     * */
    private Integer curbWeight;

    /**
     * 报废期
     */
    private Date abolishDate;

    /**
     * 有效期限结束日期
     */
    private Date expirationDate;
}
