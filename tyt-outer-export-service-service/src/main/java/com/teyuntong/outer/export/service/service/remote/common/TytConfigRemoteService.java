package com.teyuntong.outer.export.service.service.remote.common;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.tytconfig.service.ConfigRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "configRpcService", fallbackFactory = TytConfigRemoteService.TytConfigRemoteFallbackFactory.class)
public interface TytConfigRemoteService extends ConfigRpcService {

    @Component
    class TytConfigRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TytConfigRemoteService>{

        protected TytConfigRemoteFallbackFactory() {
            super(true, TytConfigRemoteService.class);
        }
    }
}
