package com.teyuntong.outer.export.service.service.biz.volcengine.client;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * <AUTHOR>
 * @since 2024/01/10 15:54
 */
@Data
@ConfigurationProperties(prefix = "volcengine")
public class VolcengineProperties {

    private AppConfig businessSecurity = new AppConfig();

    @Data
    public static class AppConfig {
        /**
         * appId
         */
        private Integer appId;
        /**
         * ak
         */
        private String accessKey;
        /**
         * sk
         */
        private String secretKey;

    }
}
