package com.teyuntong.outer.export.service.service.common.utils;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.client.model.CustomErrorCode;
import com.teyuntong.outer.export.service.client.model.ResponseCode;
import com.teyuntong.outer.export.service.service.biz.esign.identity.client.IdentityAuthBaseResult;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJDataResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @since 2024/02/28 14:12
 */
@Slf4j
public class RetrofitUtils {

    public static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json");

    private RetrofitUtils() {
    }

    public static boolean mediaTypeEquals(MediaType left, MediaType right) {
        if (left == null || right == null) {
            return false;
        }

        return StringUtils.equals(left.type(), right.type()) &&
                StringUtils.equals(left.subtype(), right.subtype());
    }

    public static boolean isApplicationJsonMediaType(MediaType mediaType) {
        return mediaTypeEquals(mediaType, JSON_MEDIA_TYPE);
    }

    @SneakyThrows
    public static <T> T verifyResponse(Response<IdentityAuthBaseResult<T>> response) {
        if (!response.isSuccessful()) {
            try (ResponseBody errorBody = response.errorBody()) {
                String error = errorBody != null ? errorBody.string() : null;

                ResponseCode info = CustomErrorCode.easyInfo("500", "三方接口请求失败, 原始返回: " + error);

                throw BusinessException.createException(info);
            }
        }

        IdentityAuthBaseResult<T> body = response.body();

        if (body == null || body.getCode() != 0) {

            ResponseCode info = CustomErrorCode.easyInfo("500", "三方接口请求失败, 原始返回: " + body);
            throw BusinessException.createException(info);
        }
        return body.getData();
    }

    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    public static <T> T getDataDetail(Response<IdentityAuthBaseResult<T>> response) {
        log.info("common-api orc response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }

        IdentityAuthBaseResult<T> bodyResult = response.body();
        if(IdentityAuthBaseResult.checkSuccess(bodyResult)){
            return bodyResult.getData();
        }

        if(bodyResult == null){
            throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY);
        }

        LogPrintUtil.printFixLog("getDataDetail", bodyResult);

        int errCode = bodyResult.getCode();
        String msg = bodyResult.getMessage();

        ResponseCode extraCode = CustomErrorCode.easyInfo(errCode + "", msg);
        String resultJson = JSON.toJSONString(extraCode);

        ResponseCode responseCode = CustomErrorCode.easyInfo(OuterExportErrorCode.ESIGN_ERROR, resultJson);
        throw BusinessException.createException(responseCode);

    }


    /**
     * 获取数据，如果有错误则抛出异常
     * @param response response
     * @param <T> response
     * @return T
     */
    public static HBWJDataResult getDetail(Response<HBWJDataResult<Object>> response) {
        log.info("common-api orc response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }

        HBWJDataResult bodyResult = response.body();
        if(HBWJDataResult.checkSuccess(bodyResult)){
            return bodyResult;
        }
        return null;
    }

}
