package com.teyuntong.outer.export.service.service.biz.tencent.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.outer.export.service.service.biz.tencent.service.TencentMapService;
import com.teyuntong.outer.export.service.service.common.property.TencentLocationProperties;
import com.teyuntong.outer.export.service.service.common.utils.HttpClientUtil;
import com.teyuntong.outer.export.service.service.common.utils.NavUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 腾讯货车导航
 * https://lbs.qq.com/service/webService/webServiceGuide/route/directionTrucking#2
 *
 * <AUTHOR>
 * @since 2025-02-06 15:53
 */
@Service
@Slf4j
@EnableConfigurationProperties(TencentLocationProperties.class)
public class TencentMapServiceImpl implements TencentMapService {
    @Autowired
    private TencentLocationProperties tencentLocationProperties;

    @Override
    public DistanceRpcVO calculateDistance(DistanceRpcDTO dto) {
        String response = this.navigateTruck(dto);
        if (StringUtils.isNotBlank(response)) {
            JSONObject resultJson = JSONObject.parseObject(response);
            Integer distance = getDistance(resultJson);
            if (Objects.nonNull(distance)) {
                return DistanceRpcVO.builder().success(true).message("success").distance(distance).build();
            }
        }
        return DistanceRpcVO.builder().success(false).message("获取距离失败").build();
    }

    /**
     * 调用腾讯货车导航服务，返回导航数据
     */
    @Override
    public String navigateTruck(DistanceRpcDTO dto) {
        try {
            Map<String, String> paramMap = getParamMap(dto);

            URI uri = HttpClientUtil.createUri(tencentLocationProperties.getUrl(), paramMap);
            log.info("调用腾讯货车导航请求参数，url:{}", uri.toURL());
            HttpGet httpGet = new HttpGet(uri);

            CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpGet);
            String json = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            log.info("调用腾讯货车导航结果:{}", json);
            return json;
        } catch (IOException e) {
            log.error("调用腾讯货车导航失败, 异常：", e);
            return "";
        }
    }

    private static Integer getDistance(JSONObject resultJson) {
        if (resultJson == null) {
            return null;
        }
        Integer status = resultJson.getInteger("status");
        if (!Objects.equals(status, 0)) {
            return null;
        }
        JSONObject result = resultJson.getJSONObject("result");
        if (result == null) {
            return null;
        }
        JSONArray routes = result.getJSONArray("routes");
        if (routes == null || routes.isEmpty()) {
            return null;
        }
        JSONObject route = routes.getJSONObject(0);
        if (route == null) {
            return null;
        }
        return route.getInteger("distance");
    }

    private Map<String, String> getParamMap(DistanceRpcDTO dto) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("key", tencentLocationProperties.getKey());
        paramMap.put("from", dto.getFromLatitude() + "," + dto.getFromLongitude());
        paramMap.put("to", dto.getToLatitude() + "," + dto.getToLongitude());
        // 如果车重和载重为空，取默认车型
        if (dto.getWeight() == null || dto.getLoad() == null) {
            paramMap.put("size", "4");
            paramMap.put("height", "4");
            paramMap.put("width", "2.5");
            paramMap.put("length", "12");
            paramMap.put("weight", "20");
            paramMap.put("load", "12");
            paramMap.put("axle_count", "3");
            paramMap.put("plate_number", "冀PN9651");
        } else {
            paramMap.put("size", NavUtil.getTruckSize(dto.getLength(), dto.getWeight()));
            paramMap.put("height", dto.getHeight() == null ? "" : dto.getHeight().toString());
            paramMap.put("width", dto.getWidth() == null ? "" : dto.getWidth().toString());
            paramMap.put("length", dto.getLength() == null ? "" : dto.getLength().toString());
            paramMap.put("weight", dto.getWeight() == null ? "" : dto.getWeight().toString());
            paramMap.put("load", dto.getLoad() == null ? "" : dto.getLoad().toString());
            paramMap.put("axle_count", dto.getAxleCount() == null ? "" : dto.getAxleCount().toString());
            paramMap.put("plate_number", dto.getPlateNumber());
        }
        paramMap.put("no_step", "1"); // 是否返回路线步骤，取值:0：返回（默认）; 1：不返回
        if (dto.isOnlyDistance()) { // 只返回距离
            paramMap.put("no_polyline", "1"); // 是否返回路线坐标点串，取值：0：返回（默认）；1：不返回
        }else {
            paramMap.put("get_mp", "1"); // 是否返回多条路线方案，取值：0：返回一条推荐路线（默认）；1：返回1到3条路线
        }

        return paramMap;
    }
}
