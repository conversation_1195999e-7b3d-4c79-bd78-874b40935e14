package com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("corporate_base_info")
public class CorporateBaseInfo implements Serializable {
    /**
     * 企业id
     */
    @TableId
    private Long id;
    /**
     * 企业名
     */
    private String name;

    /**
     * 法人
     */
    @TableField("legal_person_name")
    private String legalPersonName;

    /**
     * 英文名
     */
    private String property3;

    /**
     * 曾用名
     */
    @TableField("history_names")
    private String historyNames;

    /**
     * 简称
     */
    private String alias;

    /**
     * 统一社会信用代码
     */
    @TableField("credit_code")
    private String creditCode;

    /**
     * 组织机构代码
     */
    @TableField("org_number")
    private String orgNumber;

    /**
     * 纳税人识别号
     */
    @TableField("tax_number")
    private String taxNumber;

    /**
     * 注册号
     */
    @TableField("reg_number")
    private String regNumber;

    /**
     * 法人类型，1 人 2 公司
     */
    private Integer type;

    /**
     * 网址
     */
    @TableField("website_list")
    private String websiteList;

    /**
     * 企业联系方式
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 企业类型
     */
    @TableField("company_org_type")
    private String companyOrgType;

    /**
     * 企业状态
     */
    @TableField("reg_status")
    private String regStatus;

    /**
     * 注销日期
     */
    @TableField("cancel_date")
    private Date cancelDate;

    /**
     * 注册资本
     */
    @TableField("reg_capital")
    private String regCapital;

    /**
     * 人员规模
     */
    @TableField("staff_num_range")
    private String staffNumRange;

    /**
     * 行业
     */
    private String industry;

    /**
     * 股票号
     */
    @TableField("bond_num")
    private String bondNum;

    /**
     * 股票名
     */
    @TableField("bond_name")
    private String bondName;

    /**
     * 吊销日期
     */
    @TableField("revoke_date")
    private Date revokeDate;

    /**
     * 吊销原因
     */
    @TableField("revoke_reason")
    private String revokeReason;

    /**
     * 股票曾用名
     */
    @TableField("used_bond_name")
    private String usedBondName;

    /**
     * 经营开始时间
     */
    @TableField("from_time")
    private Date fromTime;

    /**
     * 核准时间
     */
    @TableField("approved_time")
    private Date approvedTime;

    /**
     * 参保人数
     */
    @TableField("social_staff_num")
    private Integer socialStaffNum;

    /**
     * 实收注册资本币种 人⺠币 美元 欧元 等(暂未使用)
     */
    @TableField("actual_capital_currency")
    private String actualCapitalCurrency;

    /**
     * 注销原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 经营结束时间
     */
    @TableField("to_time")
    private Date toTime;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 实收注册资金
     */
    @TableField("actual_capital")
    private String actualCapital;

    /**
     * 成立日期
     */
    @TableField("estiblish_time")
    private Date estiblishTime;

    /**
     * 登记机关
     */
    @TableField("reg_institute")
    private String regInstitute;

    /**
     * 经营范围
     */
    @TableField("business_scope")
    private String businessScope;

    /**
     * 注册地址
     */
    @TableField("reg_location")
    private String regLocation;

    /**
     * 注册资本币种 人⺠币 美元 欧元 等(暂未使用)
     */
    @TableField("reg_capital_currency")
    private String regCapitalCurrency;

    /**
     * 企业标签
     */
    private String tags;

    /**
     * 股票类型
     */
    @TableField("bond_type")
    private String bondType;

    /**
     * 企业评分
     */
    @TableField("percentile_score")
    private Integer percentileScore;

    /**
     * 国⺠经济行业分类中类
     */
    @TableField("category_middle")
    private String categoryMiddle;

    /**
     * 国⺠经济行业分类大类
     */
    @TableField("category_big")
    private String categoryBig;

    /**
     * 国⺠经济行业分类⻔类
     */
    private String category;

    /**
     * 国⺠经济行业分类小类(未使用)
     */
    @TableField("category_small")
    private String categorySmall;

    /**
     * 是否是小微企业 0不是 1是
     */
    @TableField("is_micro_ent")
    private Integer isMicroEnt;

    /**
     * 省份简称
     */
    private String base;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    private static final long serialVersionUID = 1L;
}