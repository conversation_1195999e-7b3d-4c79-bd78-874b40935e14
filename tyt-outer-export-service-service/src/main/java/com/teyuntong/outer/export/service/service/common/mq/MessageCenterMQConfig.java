package com.teyuntong.outer.export.service.service.common.mq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "message-center")
public class MessageCenterMQConfig {
    private String mqTopic;
    private String mqTag;
    private String suffix;
    private String callbackUrl;
    
    private DingdingConfig dingding;

    @Data
    public static class DingdingConfig {
        private String mqTopic;
        private String groupTag;
        private String textTag;
        private String linkTag;
        private String dingTag;
    }
}
