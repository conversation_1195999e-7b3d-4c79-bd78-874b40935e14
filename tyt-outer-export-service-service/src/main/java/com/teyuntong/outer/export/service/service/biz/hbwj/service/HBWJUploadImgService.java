package com.teyuntong.outer.export.service.service.biz.hbwj.service;

import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.FileUpLoadResp;
import okhttp3.MultipartBody;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/23 14:05
 */
public interface HBWJUploadImgService {

    /**
     * 图片上传
     * <AUTHOR>
     * @param part 文件
     * @return void
     */
    FileUpLoadResp uploadFile(MultipartBody.Part part)throws Exception;
}
