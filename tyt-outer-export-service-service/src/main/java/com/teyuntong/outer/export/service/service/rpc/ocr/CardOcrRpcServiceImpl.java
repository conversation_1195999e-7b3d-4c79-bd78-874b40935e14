package com.teyuntong.outer.export.service.service.rpc.ocr;

import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.client.ocr.dto.OcrRpcDTO;
import com.teyuntong.outer.export.service.client.ocr.service.CardOcrRpcService;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.*;
import com.teyuntong.outer.export.service.service.biz.ocr.service.BaiduOcrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * 百度OCR 证件照识别
 *
 * <AUTHOR>
 * @since 2024/12/05 20:42
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class CardOcrRpcServiceImpl implements CardOcrRpcService {

    private final BaiduOcrService baiduOcrService;

    @Override
    public OcrIdCardFrontVo idCardFrontOcr(OcrRpcDTO ocrRpcDTO) {
        if(StringUtils.isBlank(ocrRpcDTO.getUrl())){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.idCardFrontOcr(ocrRpcDTO.getUrl());
    }

    @Override
    public OcrIdCardBackVo idCardBackOcr(OcrRpcDTO ocrRpcDTO) {
        if(StringUtils.isBlank(ocrRpcDTO.getUrl())){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.idCardBackOcr(ocrRpcDTO.getUrl());
    }

    @Override
    public DriverLicenseFrontVo driverLicenseFrontOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.driverLicenseFrontOcr(url);
    }

    @Override
    public DriverLicenseBackVo driverLicenseBackOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.driverLicenseBackOcr(url);
    }

    @Override
    public VehicleLicenseFrontVo vehicleLicenseMainOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.vehicleLicenseFrontOcr(url);
    }

    @Override
    public VehicleLicenseBackVo vehicleLicenseBackOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.vehicleLicenseBackOcr(url);
    }

    @Override
    public OcrBusinessLicenseVo businessLicenseOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.businessLicenseOcr(url);
    }

    @Override
    public RoadTransportVo roadTransportOcr(String url) {
        if(StringUtils.isBlank(url)){
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        return baiduOcrService.roadTransportOcr(url);
    }
}
