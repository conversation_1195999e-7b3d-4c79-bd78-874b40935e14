package com.teyuntong.outer.export.service.service.biz.corporate.service;

import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.PhoneNumberAttribution;
import com.teyuntong.outer.export.service.client.corporate.vo.PhoneLocale;

/**
 * <AUTHOR>
 * @version 10
 * @date 2020/06/25
 */
public interface PhoneNumberAttributionService {

    /**
     * 查询手机号归属地
     *
     * @param mobile
     * @return
     */
    PhoneLocale addAndGetMobileLocale(String mobile);

    /**
     * 数据库查询手机号归属地
     *
     * @param mobile
     * @return
     */
    PhoneNumberAttribution getAttribution(String mobile);

    /**
     * 忽略插入手机归属地信息
     *
     * @param attribution PhoneNumberAttribution
     * @return 更新条数
     */
    int insertAttribution(PhoneNumberAttribution attribution);
}
