package com.teyuntong.outer.export.service.service.biz.esign.identity.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.*;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */
@RetrofitClient(value = EsignIdentityRetrofitSupplier.class, path = "v2/identity/auth/")
@UseInterceptor(LogInfoInterceptor.class)
public interface EsignIndividualIdentityAuthClient {

    @POST("api/individual/telecom3Factors")
    Call<IdentityAuthBaseResult<Telecom3FactorsApiResp>>
    telecom3Factors(@Body Telecom3FactorsApiReq req);

    @PUT("pub/individual/{flowId}/telecom3Factors")
    Call<IdentityAuthBaseResult<Object>>
    telecom3FactorsVerify(@Path("flowId") String flowId, @Body Telecom3FactorsVerifyApiReq req);

    @POST("api/individual/{flowId}/telecom3FactorAuthCode")
    @Headers("Content-type: application/json")
    Call<IdentityAuthBaseResult<Object>> telecom3FactorsAuthCode(@Path("flowId") String flowId);

    @POST("api/individual/face")
    Call<IdentityAuthBaseResult<EsignFaceVerifyResp>> faceVerifyUrl(@Body EsignFaceVerifyReq req);

    @GET("pub/individual/{flowId}/face")
    @Headers("Content-type: application/json")
    Call<IdentityAuthBaseResult<EsignFaceResultResp>> getFaceResult(@Path("flowId") String flowId);

}
