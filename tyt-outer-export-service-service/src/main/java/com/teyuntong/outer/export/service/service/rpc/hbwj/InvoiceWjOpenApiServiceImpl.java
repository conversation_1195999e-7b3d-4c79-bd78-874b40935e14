package com.teyuntong.outer.export.service.service.rpc.hbwj;

import cn.hutool.core.util.ObjectUtil;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import com.teyuntong.outer.export.service.client.invoice.hbwj.service.InvoiceWjOpenApiService;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJService;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJUploadImgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.List;

/**
 * 开票-湖北我家开放接口服务实现类
 *
 * <AUTHOR>
 * @since 2024/07/17 13:21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class InvoiceWjOpenApiServiceImpl implements InvoiceWjOpenApiService {

    @Autowired
    private  HBWJService hbwjService;

    @Autowired
    private HBWJUploadImgService hbwjUploadImgService;


    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return CreateWaybillThreeResponse 创建运单返回对象
     */
    @Override
    public WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception {
        return hbwjService.addWaybillThree(createWaybillThreeRequest);
    }


    @Override
    public  WebResult<List<ApplyFreightResp>> applyFreight(ApplyFreightRequest applyFreightRequest, String userCode) throws Exception {
        return hbwjService.applyFreight(applyFreightRequest, userCode);
    }

    @Override
    public boolean amountUpdate(AmountUpdateRequest amountUpdateRequest) throws Exception {
        return hbwjService.updateAmount(amountUpdateRequest);
    }

    @Override
    public WebResult<EditWaybillThreeResponse> editWaybillThree(EditWaybillThreeRequest editWaybillThreeRequest) throws Exception {
        return hbwjService.editWaybillThree(editWaybillThreeRequest);
    }

    @Override
    public DriverDetailResponse createDriver(HbwjDriverReport report) throws Exception{
        return hbwjService.createDriver(report);
    }

    @Override
    public  WebResult<Object> pickGoods(PickGoodsThreeRequest pickGoodsThreeRequest) throws Exception {
        return hbwjService.pickGoods(pickGoodsThreeRequest);
    }


    @Override
    public  WebResult<Object> pickGoodsV3(PickGoodsThreeV3Request pickGoodsThreeV3Request) throws Exception {
        return hbwjService.pickGoodsV3(pickGoodsThreeV3Request);
    }

    @Override
    public  WebResult<Object> arriveGoods(ArriveGoodsThreeRequest arriveGoodsThreeRequest) throws Exception {
         return hbwjService.arriveGoods(arriveGoodsThreeRequest);
    }

    @Override
    public  WebResult<WaybillThreeOffsetResp> getWaybillThreeOffset(WaybillThreeOffsetRequest waybillThreeOffsetRequest) throws Exception {
        return hbwjService.getWaybillThreeOffset(waybillThreeOffsetRequest);
    }

    @Override
    public  WebResult<Object> cancelWaybill(CancelOrderThreeRequest cancelOrderThreeRequest) throws Exception {
        return hbwjService.cancelWaybill(cancelOrderThreeRequest);
    }

    @Override
    public FileUpLoadResp uploadFile(String uploadFileUrl) throws Exception {
        // 将 "myfile.txt" 替换为实际本地文件路径
        File file = new File(uploadFileUrl);
        RequestBody fileRQ = RequestBody.create(MediaType.parse("image/*"), file);
        MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), fileRQ);
        return hbwjUploadImgService.uploadFile(part);
    }


    @Override
    public String uploadWeightReceipts(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto,String userCode) throws Exception {
        return hbwjService.uploadWeightReceipts(uploadWeightReceiptsReqDto, userCode);
    }

    @Override
    public String getFundVerificationCode(String mobile, String userCode) throws Exception {
        return hbwjService.getFundVerificationCode(mobile, userCode);
    }

    @Override
    public DriverDetailResponse createCar(HbwjCarReport report) throws Exception{
        return hbwjService.createCar(report);
    }

    @Override
    public CreateCompanyResponse createCompany(CreateCompanyRequest createCompanyRequest) throws Exception {
        return hbwjService.createCompany(createCompanyRequest);
    }

    @Override
    public CreateContractResponse createContract(CreateContractRequest request) throws Exception {
        CreateContractResponse contract = hbwjService.createContract(request);
        if (ObjectUtil.isNotNull(contract) && contract.getContractId() != null) {
            return contract;
        }
        throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
    }

    @Override
    public CreateProjectResponse createProject(CreateProjectRequest request) throws Exception {
        CreateProjectResponse project = hbwjService.createProject(request, request.getUserCode());
        if (ObjectUtil.isNotNull(project) && project.getProjectId() != null) {
            return project;
        }
        throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
    }

    @Override
    public QueryWalletResponse selectCustomerRechargeWalletList(QueryWalletRequest request) throws Exception {
        return hbwjService.selectCustomerRechargeWalletList(request);
    }

    @Override
    public WebResult<Object> setPayPwd(SavePayPwdRequest request) throws Exception {
        return hbwjService.setPayPwd(request);
    }

    @Override
    public WebResult<Object> changePayPwd(ChangePayPwdRequest request) throws Exception {
        return hbwjService.changePayPwd(request);
    }

    @Override
    public WebResult<List<BatchDoPayResponse>> batchDoPay(BatchDoPayRequest batchDoPayRequest,String userCode) throws Exception {
        return hbwjService.batchDoPay(batchDoPayRequest,userCode);
    }

    @Override
    public WebResult<Object> changePhoneCaptchaSendForAccount(ChangePhoneCaptchaSendRequest changePhoneCaptchaSendRequest) throws Exception {
        hbwjService.changePhoneCaptchaSendForAccount(changePhoneCaptchaSendRequest);
        return WebResult.success();
    }

    @Override
    public WebResult<Object> changePhone(ChangePhoneRequest changePhoneRequest) throws Exception {
        hbwjService.changePhone(changePhoneRequest);
        return WebResult.success();
    }

    @Override
    public WebResult<ChangePhoneCaptchaCheckResponse> changePhoneCaptchaCheck(ChangePhoneCaptchaCheckRequest changePhoneCaptchaCheckRequest) throws Exception {
        return WebResult.success(hbwjService.changePhoneCaptchaCheck(changePhoneCaptchaCheckRequest));
    }


    @Override
    public WebResult<Long> invoiceCreate(InvoiceRequest invoiceRequest, String userCode) throws Exception {
        return hbwjService.invoiceCreate(invoiceRequest, userCode);
    }

    @Override
    public WebResult<Long> invoiceUpdate(InvoiceRequest invoiceRequest, String userCode) throws Exception {
        return hbwjService.invoiceUpdate(invoiceRequest, userCode);
    }

    @Override
    public WebResult<Object> doInvoiceCreate(InvoiceCreateRequest invoiceCreateRequest, String userCode) throws Exception {
        return hbwjService.doInvoiceCreate(invoiceCreateRequest, userCode);
    }

    @Override
    public WebResult<InvoiceResponse> queryInvoiceCallBackVo(InvoiceInfoRequest invoiceInfoRequest, String userCode) throws Exception {
        return hbwjService.queryInvoiceCallBackVo(invoiceInfoRequest, userCode);
    }

    @Override
    public WebResult<String> uploadWeightReceiptsNew(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, String userCode) throws Exception {
        return hbwjService.uploadWeightReceiptsNew(uploadWeightReceiptsReqDto, userCode);
    }

    @Override
    public WebResult<PaymentOrderResp> getPaymentDetailByNo(String paymentNo, String userCode) throws Exception {
        return hbwjService.getPaymentDetailByNo(paymentNo, userCode);
    }

    @Override
    public WebResult<Object> cancelFreight(CancelFreightRequest cancelFreightRequest, String userCode) throws Exception {
        return hbwjService.cancelFreight(cancelFreightRequest, userCode);
    }

    @Override
    public WebResult<Object> paymentCancel(PaymentCancelRequest paymentCancelRequest, String userCode) throws Exception {
        return hbwjService.paymentCancel(paymentCancelRequest, userCode);
    }

    @Override
    public  WebResult<CreateProtocolResponse> createProtocol(CreateProtocolRequest createProtocolRequest) throws Exception {
        return hbwjService.createProtocol(createProtocolRequest);
    }

    @Override
    public WebResult<Object> receiptsUpload(ReceiptsUploadRequest receiptsUploadRequest, String userCode) throws Exception {
        return hbwjService.receiptsUpload(receiptsUploadRequest, userCode);
    }

    @Override
    public WebResult<Object> arriveWaybill(ArriveWaybillRequest arriveWaybillRequest, String userCode) throws Exception {
        return hbwjService.arriveWaybill(arriveWaybillRequest, userCode);
    }

    @Override
    public WebResult<Object> fundSendMsg(String userCode) throws Exception {
        return hbwjService.fundSendMsg(userCode);
    }

    @Override
    public WebResult<Object> forgetpaypwd(ForgetPayPwdRequest forgetPayPwdRequest, String userCode) throws Exception {
        return hbwjService.forgetpaypwd(forgetPayPwdRequest, userCode);
    }

}
