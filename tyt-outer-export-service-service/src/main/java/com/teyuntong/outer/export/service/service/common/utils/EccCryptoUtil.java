package com.teyuntong.outer.export.service.service.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ByteUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.function.BinaryOperator;

/**
 * Ecc加密工具类
 * 不要擅自改！！！！！！
 * <AUTHOR>
 * @date 2024/01/09
 */
@Slf4j
@Component
public class EccCryptoUtil implements InitializingBean {

    /**
     * 加密操作器
     */
    public static final Map<EncryptionTypeEnum, BinaryOperator<String>> cryptoOperator = new LinkedHashMap<>();

    /**
     * 生成ECC密钥对。
     */
    @SneakyThrows
    public static Pair<String, String> generateECCKeyPair()  {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC");
        ECGenParameterSpec ecSpec = new ECGenParameterSpec("secp256r1");
        keyPairGenerator.initialize(ecSpec, new SecureRandom());
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 转 base64
        return Pair.of(Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded()), Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded()));

    }

    /**
     * 签名
     * @param signPrivateKey  签名私钥
     * @param cryptoPublicKey 加密公钥
     * @param json            json参数
     * @param encryptedFields 加密字段
     * @param nonce           nonce
     * @param timestamp       时间戳
     * @return {@link Pair}<{@link String} 签名, {@link String} 真实请求参数密文>
     */
    public static Pair<String, String> sign(String signPrivateKey, String cryptoPublicKey, long nonce, long timestamp, String json, List<String> encryptedFields)  {

        // 字段加密
        String jsonRequestBodyStr = startHandleJsonFields(json, cryptoPublicKey, EncryptionTypeEnum.ENCRYPT, encryptedFields);

        // json排序
        String signJsonBody = sortJsonByKey(jsonRequestBodyStr);
        // 签名
        String sign = sign(signPrivateKey, signJsonBody.getBytes(), nonce, timestamp);

        return Pair.of(sign, signJsonBody);
    }

    /**
     * 验证签名
     *
     * @param signPublicKey    签名公钥
     * @param cryptoPrivateKey 加密私钥
     * @param sign             签名
     * @param json             json参数
     * @param nonce            nonce
     * @param timestamp        时间戳
     * @param encryptedFields  加密字段
     * @return {@link Pair}<{@link Boolean} 验证结果, {@link String} 真实请求参数原文>
     */
    public static Pair<Boolean, String> verifySign(String signPublicKey, String cryptoPrivateKey, String sign,  long nonce, long timestamp, String json, List<String> encryptedFields)  {

        // 重排序
        String signJsonBody = sortJsonByKey(json);

        // 验签
        boolean verifySignature = verifySignature(signPublicKey, signJsonBody.getBytes(), sign, nonce, timestamp);

        // 解密
        String jsonBody = startHandleJsonFields(signJsonBody, cryptoPrivateKey, EncryptionTypeEnum.DECRYPT, encryptedFields);

        return Pair.of(verifySignature, jsonBody);

    }

    /**
     * 使用公钥加密数据。
     */
    @SneakyThrows
    private static String encrypt(String pkStr, String field)  {
        return encrypt(pkStr, field.getBytes());
    }

    /**
     * 使用公钥加密数据。
     */
    @SneakyThrows
    private static String encrypt(String pkStr, byte[] plaintext)  {
        PublicKey publicKey = publicKeyFromString(pkStr);
        Cipher cipher = Cipher.getInstance("ECIES", "BC");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return Base64.getEncoder().encodeToString(cipher.doFinal(plaintext));
    }

    /**
     * 使用私钥解密数据。
     */
    @SneakyThrows
    private static String decrypt(String skStr, String encryptedData) {
        PrivateKey privateKey = privateKeyFromString(skStr);
        Cipher cipher = Cipher.getInstance("ECIES", "BC");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(cipher.doFinal(Base64.getDecoder().decode(encryptedData)), StandardCharsets.UTF_8);
    }

    /**
     * 签名
     * @param skStr      私钥字符串
     * @param data       数据
     * @param nonce      现时标志
     * @param timestamp  时间戳
     * @return {@link String} base64 的签名
     * @throws NoSuchAlgorithmException 没有这样算法例外
     * @throws InvalidKeyException      无效密钥异常
     * @throws SignatureException
     */
    @SneakyThrows
    public static  String sign(String skStr, byte[] data, long nonce, long timestamp)  {
        PrivateKey privateKey = privateKeyFromString(skStr);
        Signature signature = Signature.getInstance("SHA256withECDSA");
        signature.initSign(privateKey);

        // 将原始数据、时间戳和nonce结合在一起进行签名
        signature.update(data);
        signature.update(ByteUtil.longToBytes(timestamp));
        signature.update(ByteUtil.longToBytes(nonce));

        return Base64.getEncoder().encodeToString(signature.sign());
    }

    /**
     * 验证签名
     *
     * @param pkStr          公钥字符串
     * @param data           数据
     * @param sign           base64 签名
     * @param nonce          现时标志
     * @param timestamp      时间戳
     * @return boolean
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws SignatureException
     */
    @SneakyThrows
    private static  boolean verifySignature(String pkStr, byte[] data, String sign, long nonce, long timestamp) {
        PublicKey publicKey = publicKeyFromString(pkStr);
        Signature signature = Signature.getInstance("SHA256withECDSA");
        signature.initVerify(publicKey);

        if (!isTimestampValid(timestamp)){
            log.error("无效签名 ， timestamp");
            throw new SignatureException("无效签名 ， timestamp");
        }

        // 将原始数据、时间戳和nonce结合在一起进行验签
        signature.update(data);
        signature.update(ByteUtil.longToBytes(timestamp));
        signature.update(ByteUtil.longToBytes(nonce));

        return signature.verify(Base64.getDecoder().decode(sign));
    }


    /**
     * 字符串到私钥
     * @param keyStr 关键str
     * @return {@link PrivateKey}
     * @throws Exception 异常
     */
    @SneakyThrows
    private static  PrivateKey privateKeyFromString(String keyStr)  {
        byte[] decodedKey = Base64.getDecoder().decode(keyStr);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        return keyFactory.generatePrivate(spec);
    }


    /**
     * 来自字符串公钥
     *
     * @param keyStr 关键str
     * @return {@link PublicKey}
     */
    @SneakyThrows
    private static  PublicKey publicKeyFromString(String keyStr) {
        byte[] decodedKey = Base64.getDecoder().decode(keyStr);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        return keyFactory.generatePublic(spec);
    }



    /**
     * 时间戳是否有效
     * 和服务器时间作对比， 10分钟以内则有效
     * @param timestamp 时间戳
     * @return boolean
     */
    private static  boolean isTimestampValid(long timestamp) {
        long currentTime = System.currentTimeMillis();
        long difference = Math.abs(currentTime - timestamp);
        return difference <= 10 * 60 * 1000; // 10分钟
    }


    /**
     * 按键排序json
     * 不要碰这个代码！谁碰我杀谁
     * @param jsonStr json str
     * @return {@link String}
     */
    public static  String sortJsonByKey(String jsonStr) {
        Object json = JSON.parse(jsonStr);
        Object sortedJson = sortObjectByKey(json);
        return JSON.toJSONString(sortedJson);
    }

    /**
     * 递归  按键排序json对象
     * 不要碰这个代码！谁碰我杀谁
     * @param json json
     * @return {@link Object}
     */
    private static  Object sortObjectByKey(Object json) {
        if (json instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) json;
            Map<String, Object> sortedMap = new LinkedHashMap<>();

            jsonObject.keySet().stream()
                    .sorted() // 默认的字符串排序，即字典序 (a-z)
                    .forEach(key -> sortedMap.put(key, sortObjectByKey(jsonObject.get(key))));

            return sortedMap;
        } else if (json instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) json;
            for (int i = 0; i < jsonArray.size(); i++) {
                jsonArray.set(i, sortObjectByKey(jsonArray.get(i)));
            }
            return jsonArray;
        }
        return json;
    }

    /**
     * 解密json字段， 对 fields 里面的字段进行解密替换
     *
     * @param jsonStr       json str
     * @param key 密钥（公钥加密，私钥解密）
     * @param encryptionTypeEnum 加解密类型
     * @return {@link String}
     */
    public static  String startHandleJsonFields(String jsonStr, String key, EncryptionTypeEnum encryptionTypeEnum, List<String> encryptedFields) {
        if (CollUtil.isEmpty(encryptedFields)) {
            return jsonStr;
        }
        return JSON.toJSONString(handleJsonFieldsDeep(JSON.parse(jsonStr), encryptedFields, key, cryptoOperator.get(encryptionTypeEnum)));
    }


    /**
     * 递归深度解密json字段
     *
     * @param json          json
     * @param fields        字段
     * @param key 他私钥
     * @return {@link Object}
     */
    private static  Object handleJsonFieldsDeep(Object json, List<String> fields, String key, BinaryOperator<String> operator) {
        if (json instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) json;
            Map<String, Object> processedMap = new LinkedHashMap<>();

            for (String jsonKey : jsonObject.keySet()) {
                Object value = jsonObject.get(jsonKey);
                if (fields.contains(jsonKey) && value instanceof String) {
                    // 加/解密
                    value = Optional.of(value).filter(ObjectUtil::isNotEmpty).map(str -> operator.apply(key, (String) str)).orElse((String) value);
                } else if (value instanceof JSONObject || value instanceof JSONArray) {
                    value = handleJsonFieldsDeep(value, fields, key, operator);
                }
                processedMap.put(jsonKey, value);
            }

            return processedMap;
        } else if (json instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) json;
            for (int i = 0; i < jsonArray.size(); i++) {
                jsonArray.set(i, handleJsonFieldsDeep(jsonArray.get(i), fields, key, operator));
            }
            return jsonArray;
        }
        return json;
    }

    public static long generateNonce() {
        long minimum = 100000000000000000L; // 10^17
        long maximum = 999999999999999999L; // Just below 10^18
        return minimum + ((long) (new SecureRandom().nextDouble() * (maximum - minimum)));
    }


    public static void main(String[] args) {
// 获取ecc加解密秘钥
        Pair<String, String> keyPairCrypto = EccCryptoUtil.generateECCKeyPair();
        // 获取验签秘钥
        Pair<String, String> keyPairSign = EccCryptoUtil.generateECCKeyPair();

        System.out.println("keyPairCryptoKey : " + keyPairCrypto.getKey()+ "\n  keyPairCryptoValue : " + keyPairCrypto.getValue());
        System.out.println("keyPairSign: " + keyPairSign.getKey()+ "\n  keyPairSignValue : " + keyPairSign.getValue());

        long nonce = EccCryptoUtil.generateNonce();
        long timestamp = System.currentTimeMillis();
        System.out.println("nonce: " + nonce + "\n  timestamp : " + timestamp);


        String json = "{\"identityIds\":[\"OWNER\"],\"companyName\":\"如新(中国)日用保健品有限公司\",\"unifiedCreditcCode\":\"91310000607216430D\",\"adminName\":\"李二狗\",\"adminPhone\":\"***********\",\"settlementPrincipalId\":1,\"businessLicenseUrl\":\"http://devimage.teyuntong.net/dispatch/APP/2023-4-6/6576a5b1980ecd84acf425e0e8ce3a9e.png\",\"tradeTimeStart\":\"2023-01-01 00:00:00\",\"tradeTimeEnd\":\"2025-12-31 23:59:59\",\"financeContact\":\"王大锤\",\"financeContactTel\":\"***********\",\"businessContact\":\"赵日天\",\"businessContactTel\":\"***********\"}";

        Pair<String, String> pair = EccCryptoUtil.sign(keyPairSign.getValue(), keyPairCrypto.getKey(), nonce, timestamp, json, Arrays.asList("idCardNum", "password"));
        System.out.println("sign: " + pair.getKey()+ "\n  jsonBody : " + pair.getValue());

        Pair<Boolean, String> verifyPair = EccCryptoUtil.verifySign(keyPairSign.getKey(), keyPairCrypto.getValue(), pair.getKey(), nonce, timestamp, pair.getValue(), Arrays.asList("idCardNum", "password"));

        System.out.println("verifySign: " + verifyPair.getKey()+ "\n  jsonBody : " + verifyPair.getValue());

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("植入三方加密算法 开始");
        // 植入三方加密算法
        Security.addProvider(new BouncyCastleProvider());
        log.info("植入三方加密算法 结束");

        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null){
            log.info("运行环境没有BouncyCastleProvider");
        }
        log.info("PROVIDER_NAME为：{}", Security.getProvider(BouncyCastleProvider.PROVIDER_NAME));
        double version = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME).getVersion();
        log.info("BouncyCastleProvider版本号为：{}", version);
        // 初始化加解密操作器
        cryptoOperator.put(EncryptionTypeEnum.ENCRYPT, EccCryptoUtil::encrypt);
        cryptoOperator.put(EncryptionTypeEnum.DECRYPT, EccCryptoUtil::decrypt);
    }

    @Getter
    public enum EncryptionTypeEnum {

        /**加密*/
        ENCRYPT(1,"加密"),
        /**解密*/
        DECRYPT(2,"解密");

        private final Integer code;

        private final String message;

        EncryptionTypeEnum(Integer code, String message){
            this.code = code;
            this.message = message;
        }
    }
}