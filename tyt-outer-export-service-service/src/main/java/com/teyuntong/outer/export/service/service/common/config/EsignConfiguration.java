package com.teyuntong.outer.export.service.service.common.config;

import com.teyuntong.outer.export.service.client.fallback.CommonExceptionFallbackFactory;
import com.teyuntong.outer.export.service.service.common.property.EsignSignProperty;
import com.timevale.esign.sdk.tech.bean.result.Result;
import com.timevale.esign.sdk.tech.v3.client.ServiceClient;
import com.timevale.esign.sdk.tech.v3.client.ServiceClientManager;
import com.timevale.tech.sdk.bean.HttpConnectionConfig;
import com.timevale.tech.sdk.bean.ProjectConfig;
import com.timevale.tech.sdk.bean.SignatureConfig;
import com.timevale.tech.sdk.constants.AlgorithmType;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/02/05 10:32
 */
@Configuration
@EnableConfigurationProperties(EsignSignProperty.class)
public class EsignConfiguration {

    @Bean(name = "esignSignServiceClient")
    public ServiceClient esignSignServiceClient(EsignSignProperty esignSignProperty) {
        ProjectConfig proCfg = new ProjectConfig();
        // 项目ID（应用ID）
        proCfg.setProjectId(esignSignProperty.getProjectId());
        // 项目Secret(应用Secret)
        proCfg.setProjectSecret(esignSignProperty.getProjectSecret());
        // 开放平台地址
        proCfg.setItsmApiUrl(esignSignProperty.getItsmApiUrl());

        HttpConnectionConfig httpConCfg = new HttpConnectionConfig();
        //连接超时时间配置，最大不能超过30秒
        httpConCfg.setTimeoutConnect(5);
        // 请求超时时间，最大不能超过30
        httpConCfg.setTimeoutRequest(5);

        SignatureConfig signCfg = new SignatureConfig();
        signCfg.setAlgorithm(AlgorithmType.HMACSHA256);
        Result result = ServiceClientManager.registClient(proCfg, httpConCfg, signCfg);

        if (result.getErrCode() != 0) {
            throw new IllegalStateException("esign 服务注册失败: " + result.getMsg());
        }

        return ServiceClientManager.get(esignSignProperty.getProjectId());
    }

    @Bean
    public CommonExceptionFallbackFactory getCommonExceptionFallbackFactory(){

        CommonExceptionFallbackFactory fallbackFactory = new CommonExceptionFallbackFactory();

        return fallbackFactory;
    }

}
