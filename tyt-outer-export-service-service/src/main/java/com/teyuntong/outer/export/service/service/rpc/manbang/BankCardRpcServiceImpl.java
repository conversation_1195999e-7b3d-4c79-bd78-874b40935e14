package com.teyuntong.outer.export.service.service.rpc.manbang;

import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.outer.export.service.client.manbang.service.BankCardRpcService;
import com.teyuntong.outer.export.service.client.manbang.vo.BankAccountDetailVO;
import com.teyuntong.outer.export.service.service.biz.manbang.bankcard.service.BankAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 集团银行卡相关RPC实现类
 *
 * <AUTHOR>
 * @since 2024-4-3 11:36:13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class BankCardRpcServiceImpl implements BankCardRpcService {

    private final BankAccountService bankAccountService;

    @Override
    public List<BankAccountDetailVO> getGroupBankCardList(Long userId) {
        return BeanUtil.copyToList(bankAccountService.getGroupBankCardList(userId), BankAccountDetailVO.class);
    }
}
