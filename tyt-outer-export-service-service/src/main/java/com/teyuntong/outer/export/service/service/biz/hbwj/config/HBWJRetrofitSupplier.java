package com.teyuntong.outer.export.service.service.biz.hbwj.config;

import cn.hutool.crypto.digest.MD5;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import com.teyuntong.outer.export.service.service.common.property.MjSignProperty;
import lombok.RequiredArgsConstructor;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.Base64;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(MjSignProperty.class)
public class HBWJRetrofitSupplier implements RetrofitSupplier {

    private final MjSignProperty mjSignProperty;

    private final Interceptor signInterceptor = new Interceptor() {
        private String getContentType(Request request){


            String contentType = request.header(HttpHeaders.CONTENT_TYPE);

            if(StringUtils.isBlank(contentType)){
                RequestBody requestBody = request.body();
                if(requestBody != null){
                    contentType = requestBody.contentType().toString();
                }
            }
            return contentType;
        }

        @NotNull
        @Override
        public Response intercept(@NotNull Chain chain) throws IOException {
            Request request = chain.request();
            String contentType = this.getContentType(request);

            RequestBody requestBody = request.body();

            String contentMD5 = "";

            if (requestBody != null) {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                contentMD5 = Base64.getEncoder().encodeToString(MD5.create().digest(buffer.readByteArray()));
            }
            StringJoiner stringJoiner = new StringJoiner("\n");
            stringJoiner.add(request.method().toUpperCase())//HTTPMethod
                    .add("*/*")//Accept
                    .add(contentType)//Content-Type
                    .add("")//Date
                    .add(request.url().encodedPath());//


            Request newRequest = request.newBuilder()
                    .header("Content-Type","application/x-www-form-urlencoded")
                    .header("timestamp", String.valueOf(System.currentTimeMillis()))
                    .build();

            return chain.proceed(newRequest);
        }
    };

    @Override
    public Retrofit getRetrofit() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(signInterceptor)
                .connectTimeout(Duration.ofMillis(150000))
                .readTimeout(Duration.ofMillis(150000))
                .build();

        return new Retrofit.Builder()
                .baseUrl(mjSignProperty.getWjApiUrl())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create())
                .client(okHttpClient)
                .build();
    }
}
