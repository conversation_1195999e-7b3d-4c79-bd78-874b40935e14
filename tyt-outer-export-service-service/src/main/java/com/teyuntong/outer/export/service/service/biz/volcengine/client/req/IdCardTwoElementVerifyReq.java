package com.teyuntong.outer.export.service.service.biz.volcengine.client.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/10 16:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdCardTwoElementVerifyReq {

    /**
     * 待查询身份证姓名，目前只支持明文，如果姓名中带"·"的，建议使用英文半角点
     */
    @JsonProperty("idcard_name")
    private String idCardName;

    /**
     * 待查询身份证号，目前只支持明文
     */
    @JsonProperty("idcard_no")
    private String idCardNo;

    /**
     * 精确到秒的操作时间戳，格林威治时间，如1522555200
     */
    @JsonProperty("operate_time")
    private Long operateTime;

}
