package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlFreightEntity;

/**
 * 翔和翎 运费相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlFreightService {

    /**
     * 运费打款
     *
     * @param freightEntity 运费实体
     * @return 返回操作结果的封装对象
     */
    XhlDataResult<Object> payMoney(XhlFreightEntity freightEntity) throws Exception;

    /**
     * 修改运费
     *
     * @param freightEntity 运费实体
     * @return 返回操作结果的封装对象
     */
    XhlDataResult<Object> updateCharge(XhlFreightEntity freightEntity) throws Exception;

    /**
     * 查询运费支付结果
     *
     * @param companyName   公司名称
     * @param tpWaybillNo   第三方运单号
     * @return 返回查询结果的封装对象
     */
    XhlDataResult<Object> queryPay(String companyName, String tpWaybillNo) throws Exception;


}
