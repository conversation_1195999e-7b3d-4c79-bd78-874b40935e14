package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlInvoiceEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlInvoiceService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 发票相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlInvoiceServiceImpl implements XhlInvoiceService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> applyInvoice(XhlInvoiceEntity invoiceEntity) throws Exception {
        log.info("发票申请-请求参数，invoiceEntity={}", invoiceEntity);
        if (!Objects.isNull(invoiceEntity)) {
            invoiceEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.applyInvoice(invoiceEntity).execute();
        log.info("发票申请-返回结果，invoiceEntity={}，响应参数：{} ", invoiceEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> deleteInvoice(XhlInvoiceEntity invoiceEntity) throws Exception {
        log.info("发票取消-请求参数，invoiceEntity={}", invoiceEntity);
        if (!Objects.isNull(invoiceEntity)) {
            invoiceEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.deleteInvoice(invoiceEntity).execute();
        log.info("发票取消-返回结果，invoiceEntity={}，响应参数：{} ", invoiceEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryInvoice(String invoiceApplyNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询发票申请-请求参数，appId={}，invoiceApplyNo={}", appId, invoiceApplyNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryInvoice(appId, invoiceApplyNo).execute();
        log.info("查询发票申请-返回结果，appId={}，invoiceApplyNo={}，响应参数：{} ", appId, invoiceApplyNo, response.body());
        return response.body();
    }

}
