package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2024/02/05 10:29
 */
@Data
@ConfigurationProperties("esign-sign")
public class EsignSignProperty {

    private String projectId;

    private String projectSecret;

    private String itsmApiUrl;

    private String baseUrl;

}
