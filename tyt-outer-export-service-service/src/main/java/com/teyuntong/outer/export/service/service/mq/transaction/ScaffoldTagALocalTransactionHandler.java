package com.teyuntong.outer.export.service.service.mq.transaction;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.transaction.TransactionStatus;
import com.teyuntong.infra.common.rocketmq.message.MessageTypeBase;
import com.teyuntong.infra.common.rocketmq.transcation.CovertBodyLocalTransactionHandler;
import com.teyuntong.outer.export.service.service.common.mq.OuterExportMessageType;
import com.teyuntong.outer.export.service.service.mq.TmpMessageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/08 13:35
 */
@Slf4j
@Component
public class ScaffoldTagALocalTransactionHandler extends CovertBodyLocalTransactionHandler<TmpMessageBean> {

    public ScaffoldTagALocalTransactionHandler() {
        super(TmpMessageBean.class);
    }

    @Override
    protected TransactionStatus check(Message msg, TmpMessageBean body) {
        log.info("check transaction message with commit!, body:{}", body);
        return TransactionStatus.CommitTransaction;
    }

    @Override
    protected TransactionStatus execute(Message msg, TmpMessageBean body, Object arg) {
        log.info("execute transaction message with unknown!, arg: {}, body:{}", arg, body);
        return TransactionStatus.Unknow;
    }

    @Override
    public MessageTypeBase getMessageType() {
        return OuterExportMessageType.OUTER_EXPORT_NORMAL_TAG_A;
    }
}
