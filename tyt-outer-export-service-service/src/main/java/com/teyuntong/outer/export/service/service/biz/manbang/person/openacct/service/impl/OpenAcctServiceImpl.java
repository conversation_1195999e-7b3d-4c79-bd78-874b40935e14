package com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.impl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.client.person.VO.*;
import com.teyuntong.outer.export.service.service.biz.manbang.GateWayClient;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.OpenAcctService;
import com.wlqq.wallet.gateway.client.enums.AccountTypeKind;
import com.wlqq.wallet.gateway.client.enums.IdentityType;
import com.wlqq.wallet.gateway.client.enums.ServiceKind;
import com.wlqq.wallet.gateway.client.request.mgs.*;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.CheckPasswordResponse;
import com.wlqq.wallet.gateway.client.response.mgs.CreatePersonalResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryMemberAccountResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryUserVerifyResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class OpenAcctServiceImpl implements OpenAcctService {

    private final GateWayClient client;

    @Value("${open.account.check.userId}")
    private String openAcctCheckUserId;
    @Override
    public CreatePersonalResponse applyInactiveAcct(CreatePersonalReqVO vo) {
        log.info("【个人开户】入参：{}",vo);
        CreatePersonalRequest request = new CreatePersonalRequest();
        request.setUid(vo.getUid());
        request.setLoginName(vo.getLoginName());
        request.setMember_name(vo.getMember_name());
        request.setReal_name(vo.getReal_name());
        request.setMobile(vo.getMobile());
        CreatePersonalResponse response = client.doService(ServiceKind.create_personal_member, request, CreatePersonalResponse.class);
        log.info("【个人开户】返回：{}",response);
        return response;
    }

    @Override
    public BaseResponse cancelVerify(CommonVO commonVO) {
        log.info("<注销实名> cancelVerify userId :【{}】", commonVO.getUserId());
        CancelVerifyRequest request = new CancelVerifyRequest();
        request.setIdentityNo("46_"+commonVO.getUserId());
        request.setIdentityType(IdentityType.UID.getCode());
        BaseResponse response = client.doService(ServiceKind.cancel_verify, request, BaseResponse.class);
        log.info("<注销实名> userId:【{}】,返回结果 :【{}】", commonVO.getUserId(), JSON.toJSONString(response));
        return response;
    }

    @Override
    public UserRealResponse getUserRealInfo(QueryUserVerifyInfoReVo vo) {
        log.info("<满帮个人实名查询> getUserRealInfo QueryUserVerifyInfoReVo :【{}】", JSON.toJSONString(vo));
        QueryUserVerifyInfoRequest request = new QueryUserVerifyInfoRequest();
        request.setUid("46_"+vo.getUserId());
        if(null != vo.getNeedImage()){
            request.setNeedImage(vo.getNeedImage());
        }
        QueryUserVerifyResponse response = client.doService(ServiceKind.query_user_verify_info, request, QueryUserVerifyResponse.class);
        log.info("<满帮个人实名查询> 返回结果 :【{}】", JSON.toJSONString(response));
        log.info("<满帮检查用户是否设置支付密码> checkPayPassword CheckPasswordReVo :【{}】", JSON.toJSONString(vo));
        CheckPasswordRequest requestCheckPassword = new CheckPasswordRequest();
        requestCheckPassword.setIdentityNo("46_"+vo.getUserId());
        requestCheckPassword.setIdentityType(IdentityType.UID);
        CheckPasswordResponse responseCheckPassword = client.doService(ServiceKind.check_pay_password, requestCheckPassword, CheckPasswordResponse.class);
        log.info("<满帮检查用户是否设置支付密码> 返回结果 :【{}】", JSON.toJSONString(responseCheckPassword));

        UserRealResponse realResponse = new UserRealResponse();
        realResponse.setAuthLevel(response.getAuthLevel());
        realResponse.setSetFlag(responseCheckPassword.getSetFlag());
        return realResponse;
    }

    @Override
    public QueryMemberAccountResponse queryMemberAccountInfo(QueryMemberAccountReqVO vo) {
        log.info("<获取满帮个人账号信息> queryMemberAccountInfo  QueryMemberAccountReqVO :【{}】",JSON.toJSONString(vo));
        QueryMemberAccountRequest request = new QueryMemberAccountRequest();
        request.setIdentityNo(vo.getIdentityNo());
        request.setIdentityType(IdentityType.UID);
        QueryMemberAccountResponse response = client.doService(ServiceKind.query_member_account_info, request, QueryMemberAccountResponse.class);
        log.info("<获取满帮个人账号信息> 返回结果 :【{}】", JSON.toJSONString(response));
        return response;
    }

    @Override
    public BaseResponse depositAccountApply(CommonVO commonVO) {
        log.info("<满帮个人保证金账户开户> depositAccountApply  userId :【{}】",commonVO.getUserId());
        String userId = commonVO.getUserId().toString();
        //防止测试用户占用线上，测试用户ID大于**********
       /* if (Integer.valueOf(userId) < Integer.parseInt(openAcctCheckUserId)) {
          return new BaseResponse();
        }*/
        OpenAccountRequest  request = new OpenAccountRequest();
        request.setIdentityNo("46_"+userId);
        request.setIdentityType(IdentityType.UID);
        request.setAccountType(AccountTypeKind.PERSON_GUARANTEE_ACCOUNT.getMsg());
        BaseResponse response = client.doService(ServiceKind.open_account,request);
        log.info("<满帮个保证金账户人开户> 返回结果 :【{}】", JSON.toJSONString(response));
        return response;
    }
}
