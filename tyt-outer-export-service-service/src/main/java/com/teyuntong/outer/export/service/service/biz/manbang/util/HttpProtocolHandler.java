package com.teyuntong.outer.export.service.service.biz.manbang.util;

import com.wlqq.wallet.gateway.client.enums.ErrorCode;
import com.wlqq.wallet.gateway.client.exception.GateWayClientException;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpRequest;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpResponse;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpResultType;
import org.apache.http.HttpEntity;
import org.apache.http.HttpException;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/* *
 *功能：HttpClient方式访问
 *详细：获取远程HTTP数据
 */

public class HttpProtocolHandler {

    private static String              DEFAULT_CHARSET                     = "UTF-8";

    /** 连接超时时间，由bean factory设置，缺省为8秒钟 */
    private int                        defaultConnectionTimeout            = 5000;

    /** 回应超时时间, 由bean factory设置，缺省为30秒钟 */
    private int                        defaultSoTimeout                    = 15000;

    /** 闲置连接超时时间, 由bean factory设置，缺省为60秒钟 */
    private int                        connectionRequestTimeout              = 5000;

    private int                        defaultMaxConnPerHost               = 30;

    private int                        defaultMaxTotalConn                 = 80;

    /**
     * HTTP连接管理器，该连接管理器必须是线程安全的.
     */
    private Logger logger = LoggerFactory.getLogger(HttpProtocolHandler.class);

    private static HttpProtocolHandler httpProtocolHandler  = new HttpProtocolHandler();
    
    private CloseableHttpClient httpClient;

    /**
     * 工厂方法
     *
     * @return
     */
    public static HttpProtocolHandler getInstance() {
        return httpProtocolHandler;
    }

    /**
     * 私有的构造方法
     */
    private HttpProtocolHandler() {
        // 创建一个线程安全的HTTP连接池
        PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager();
        poolingHttpClientConnectionManager.setDefaultMaxPerRoute(defaultMaxConnPerHost);
        //here just one route
        poolingHttpClientConnectionManager.setMaxTotal(defaultMaxTotalConn);

        httpClient = HttpClientFactory.getHttpClientWithRetry();

    }

    /**
     * 执行Http请求
     *
     * @param request 请求数据
     * @param strParaFileName 文件类型的参数名
     * @param strFilePath 文件路径
     * @return
     * @throws HttpException, IOException
     * @throws UnsupportedEncodingException
     */
    public HttpResponse execute(HttpRequest request, String strParaFileName, String strFilePath) throws HttpException, UnsupportedEncodingException {
        
        // 设置获取连接超时
        int defaultConnectionRequestTimeout = connectionRequestTimeout;
        if (request.getConnectionRequestTimeout() > 0) {
            defaultConnectionRequestTimeout = request.getConnectionRequestTimeout();
        }
        // 设置连接超时
        int connectionTimeout = defaultConnectionTimeout;
        if (request.getConnectionTimeout() > 0) {
            connectionTimeout = request.getConnectionTimeout();
        }

        // 设置回应超时
        int soTimeout = defaultSoTimeout;
        if (request.getTimeout() > 0) {
            soTimeout = request.getTimeout();
        }
        String charset = request.getCharset();
        charset = charset == null ? DEFAULT_CHARSET : charset;

        String url = request.getUrl();
        String result = null;
        //get模式且不带上传文件
        if (request.getMethod().equals(HttpRequest.METHOD_GET)) {
            result = httpGet(url, request.getParameters(), charset, defaultConnectionRequestTimeout, connectionTimeout, soTimeout);
        } else if (org.apache.commons.lang.StringUtils.isEmpty(strParaFileName) && org.apache.commons.lang.StringUtils.isEmpty(strFilePath)) {
            //post模式且不带上传文件
            result = httpPost(url, request.getParameters(), charset, defaultConnectionRequestTimeout, connectionTimeout, soTimeout);
        }

        // 设置Http Header中的User-Agent属性
        HttpResponse response = new HttpResponse();
        result = org.apache.commons.lang.StringUtils.isEmpty(result) ? "" : result;
        if (request.getResultType().equals(HttpResultType.STRING)) {
            response.setStringResult(result);
        } else if (request.getResultType().equals(HttpResultType.BYTES)) {
            response.setByteResult(result.getBytes(charset));
        }
        return response;
    }

    
    
    //post请求
    private String httpPost(String url, Map<String, String> params, String charset, int requestTimeout, int connectionTimeout, int soTimeout) {
        
        if(url == null || url.equals("")) {
            return null;
        }
        
        String result = null;
        //超时设置
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(requestTimeout).setConnectTimeout(connectionTimeout).setSocketTimeout(soTimeout).build();
        
      //参数组装
        List<NameValuePair> pairs   = new ArrayList<NameValuePair>();
        for(Entry<String, String> entry : params.entrySet()) {
            String key      = entry.getKey();
            String value    = entry.getValue();
            pairs.add(new BasicNameValuePair(key, formatStr(value)));
        }

        
        HttpPost httpPost               = null;
        String responseBody             = null;
        CloseableHttpResponse response  = null;

        try {
            httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, charset));

            //泳道
//            httpPost.setHeader("gateway-swimlane","shadow");

            response = httpClient.execute(httpPost);
            
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPost.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            
            HttpEntity entity   = response.getEntity();
            responseBody        = EntityUtils.toString(entity, charset);
            result              = responseBody; 
        } catch (Exception e) {
            logger.error("[http请求异常]", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        } finally {
            try {
                // 关闭连接,释放资源  
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        
        return result;
    }

    //get请求
    private String httpGet(String url, Map<String, String> params, String charset, int requestTimeout, int connectionTimeout, int soTimeout) {
        
        if(url == null || url.equals("")) {
            return null;
        }
        
        String result = null;
        //超时设置
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(requestTimeout).setConnectTimeout(connectionTimeout).setSocketTimeout(soTimeout).build();

        HttpGet httpGet                 = null;
        String responseBody             = null;
        CloseableHttpResponse response  = null;

        try {
            
            if(params != null && !params.isEmpty()) {
                List<NameValuePair> pairs   = new ArrayList<NameValuePair>();
                for(Entry<String, String> entry : params.entrySet()) {
                    String key      = entry.getKey();
                    String value    = entry.getValue();
                    pairs.add(new BasicNameValuePair(key, formatStr(value)));
                }
                url = url + "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            
            httpGet     = new HttpGet(url);
            httpGet.setConfig(requestConfig);
            response    = httpClient.execute(httpGet);
            
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }

            HttpEntity entity   = response.getEntity();
            responseBody        = EntityUtils.toString(entity, charset);
            result              = responseBody; 
        } catch (Exception e) {
            logger.error("[http请求异常]", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        } finally {
            try {
                // 关闭连接,释放资源  
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        
        return result;
    }
    
    public static String formatStr(String text) {
        return (text == null ? "" : text.trim());
    }
}
