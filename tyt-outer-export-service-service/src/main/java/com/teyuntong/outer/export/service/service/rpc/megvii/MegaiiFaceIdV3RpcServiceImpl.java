package com.teyuntong.outer.export.service.service.rpc.megvii;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.client.megvii.service.MegaiiFaceIdV3RpcService;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.*;
import com.teyuntong.outer.export.service.service.biz.megvii.v3.client.FaceIdV3Api;
import com.teyuntong.outer.export.service.service.biz.megvii.v3.client.req.MegviiBizTokenV3Req;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.ResponseBody;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 旷世人脸识别-v3版本
 *
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class MegaiiFaceIdV3RpcServiceImpl implements MegaiiFaceIdV3RpcService {
    private final FaceIdV3Api faceIdV3Api;
    private final ObjectMapper mapper;


    @Override
    public MegviiBizTokenV3Resp getBizToken(GetBizTokenV3Req req) {
        MegviiBizTokenV3Req megviiBizTokenV3Req = new MegviiBizTokenV3Req();
        BeanUtils.copyProperties(req, megviiBizTokenV3Req);

        try {
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM);
            megviiBizTokenV3Req.toMap(mapper).forEach(builder::addFormDataPart);

            Response<MegviiBizTokenV3Resp> execute = faceIdV3Api.getBizToken(builder.build()).execute();

            MegviiBizTokenV3Resp body = null;
            if (execute.isSuccessful()) {
                body = execute.body();
            } else {
                try (ResponseBody responseBody = execute.errorBody()) {
                    if (responseBody != null) {
                        body = mapper.readValue(responseBody.byteStream(), MegviiBizTokenV3Resp.class);
                    }
                }
            }
            return body;
        } catch (IOException e) {
            log.error("get biz token error:", e);
        }
        return null;
    }

    @Override
    public VerifyV3Resp verify(VerifyV3Req req) {
        try {
            MultipartBody build = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("biz_token", req.getBizToken())
                    .addFormDataPart("meglive_data", "meglive_data.txt", okhttp3.RequestBody.create(
                            Base64.getDecoder().decode(req.getMegliveDataBase64().getBytes(StandardCharsets.UTF_8)),
                            MediaType.parse("application/octet-stream")))
                    .build();
            Response<MegviiVerifyV3Resp> execute = faceIdV3Api.verify(build).execute();

            MegviiVerifyV3Resp body = null;
            if (execute.isSuccessful()) {
                body = execute.body();
            } else {
                try (ResponseBody responseBody = execute.errorBody()) {
                    if (responseBody != null) {
                        body = mapper.readValue(responseBody.byteStream(), MegviiVerifyV3Resp.class);
                    }
                }
            }

            if (body == null) {
                body = new MegviiVerifyV3Resp();
            }

            VerifyV3Resp verifyV3Resp = new VerifyV3Resp();
            BeanUtils.copyProperties(body, verifyV3Resp);
            return verifyV3Resp;
        } catch (IOException e) {
            log.error("verify error:", e);
        }
        return null;
    }
}
