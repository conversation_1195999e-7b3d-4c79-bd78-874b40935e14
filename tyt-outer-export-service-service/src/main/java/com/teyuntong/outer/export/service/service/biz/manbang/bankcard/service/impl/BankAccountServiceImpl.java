package com.teyuntong.outer.export.service.service.biz.manbang.bankcard.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.service.biz.manbang.GateWayClient;
import com.teyuntong.outer.export.service.service.biz.manbang.bankcard.service.BankAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.config.GateWayConfig;
import com.wlqq.wallet.gateway.client.enums.IdentityType;
import com.wlqq.wallet.gateway.client.enums.ServiceKind;
import com.wlqq.wallet.gateway.client.request.mgs.QueryBankAccountRequest;
import com.wlqq.wallet.gateway.client.response.mgs.BankAccountDetail;
import com.wlqq.wallet.gateway.client.response.mgs.QueryBankAccountResponse;
import com.wlqq.wallet.gateway.client.util.sign.RSA;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants.GROUP_USER_PREFIX;

@Slf4j
@RestController
@RequiredArgsConstructor
public class BankAccountServiceImpl implements BankAccountService {

    private final GateWayClient client;

    private final GateWayConfig config;

    @Override
    public List<BankAccountDetail> getGroupBankCardList(Long userId) {
        QueryBankAccountRequest request = new QueryBankAccountRequest();
        request.setIdentityNo(GROUP_USER_PREFIX + userId);
        request.setIdentityType(IdentityType.UID);
        request.setPayAttribute("normal");
        request.setNeedAllStatus("N");
        request.setNeedRaw("Y");
        request.setCardType("1");
        log.info("request group query bank account encrypt param:{}", JSON.toJSONString(request));
        QueryBankAccountResponse response = client.doService(ServiceKind.query_bank_account_encrypt, request, QueryBankAccountResponse.class);
        log.info("request group query bank account encrypt result:{}", JSON.toJSONString(request));
        if (ObjectUtil.isNotNull(response)) {
            List<BankAccountDetail> bankAccountDetailList = response.getBankAccountDetails();
            if (ObjectUtil.isNotEmpty(bankAccountDetailList)) {
                for (BankAccountDetail bankAccountDetail : bankAccountDetailList) {
                    bankAccountDetail.setRealName(doDecryptRSA(bankAccountDetail.getRealName()));
                    bankAccountDetail.setRawCertNum(doDecryptRSA(bankAccountDetail.getRawCertNum()));
                    bankAccountDetail.setRawMobileNum(doDecryptRSA(bankAccountDetail.getRawMobileNum()));
                    bankAccountDetail.setRawBankCardNo(doDecryptRSA(bankAccountDetail.getRawBankCardNo()));
                }
            }
            return bankAccountDetailList;
        }
        return null;
    }

    private String doDecryptRSA(String decryptKey) {
        if (StringUtils.isBlank(decryptKey)) {
            return null;
        }
        try {
            return new String(RSA.decryptByPrivateKey(Base64.decodeBase64(decryptKey),config.getPrivateKey()));
        } catch (Exception e) {
            log.error("decrypt for RSA error:", e);
            return null;
        }
    }

}
