package com.teyuntong.outer.export.service.service.biz.zj.service;


import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJHistoryTrackResult;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJVehicleLocationResult;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/05 13:51
 */
public interface CarLocationService {


    ZJVehicleLocationResult getCarRealTimeLocation(String carHeadNo, String color);


    ZJHistoryTrackResult getCarLocus(String carHeadNo, String color, String beginTime, String endTime);
}
