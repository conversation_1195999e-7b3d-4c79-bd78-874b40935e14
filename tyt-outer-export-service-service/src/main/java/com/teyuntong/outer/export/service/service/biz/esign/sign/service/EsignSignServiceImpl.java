package com.teyuntong.outer.export.service.service.biz.esign.sign.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.esign.enums.sign.*;
import com.teyuntong.outer.export.service.client.esign.vo.sign.*;
import com.teyuntong.outer.export.service.client.model.CustomErrorCode;
import com.teyuntong.outer.export.service.client.model.ResponseCode;
import com.teyuntong.outer.export.service.service.biz.esign.util.EsignEnumUtil;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.common.utils.LogPrintUtil;
import com.timevale.esign.sdk.tech.bean.OrganizeBean;
import com.timevale.esign.sdk.tech.bean.PersonBean;
import com.timevale.esign.sdk.tech.bean.PosBean;
import com.timevale.esign.sdk.tech.bean.SignPDFStreamBean;
import com.timevale.esign.sdk.tech.bean.result.*;
import com.timevale.esign.sdk.tech.bean.seal.OrganizeTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.PersonTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.SealColor;
import com.timevale.esign.sdk.tech.impl.constants.LegalAreaType;
import com.timevale.esign.sdk.tech.impl.constants.OrganRegType;
import com.timevale.esign.sdk.tech.impl.constants.SignType;
import com.timevale.esign.sdk.tech.service.*;
import com.timevale.esign.sdk.tech.v3.client.ServiceClient;
import com.timevale.esign.sdk.tech.v3.service.PdfDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/02/05 10:54
 */
@Service
@Slf4j
public class EsignSignServiceImpl implements EsignSignService {

    private final ServiceClient serviceClient;
    private final ObjectMapper mapper;

    public EsignSignServiceImpl(@Qualifier("esignSignServiceClient") ServiceClient serviceClient, ObjectMapper mapper) {
        this.serviceClient = serviceClient;
        this.mapper = mapper;
    }

    @Override
    public CreatePdfVO createPdf(CreatePdfReq req) {
        PdfDocumentService pdfDocumentService = serviceClient.pdfDocumentService();

        SignPDFStreamBean fileBean = new SignPDFStreamBean();
        fileBean.setStream(Base64.getDecoder().decode(req.getPdfFileBase64()));
        fileBean.setFileName(req.getPdfFileName());

        FileCreateFromTemplateResult result = pdfDocumentService.createFileFromTemplate(fileBean,
                true, req.getTxtFields() != null ? req.getTxtFields() : Collections.emptyMap());
        this.checkEsignSuccess(result);

        return CreatePdfVO.builder().resultStreamBase64(Base64.getEncoder().encodeToString(result.getStream())).build();
    }

    @Override
    public AddAccountVO addAccount(AddAccountReq req) {
        AccountService accountService = serviceClient.accountService();

        OrganRegTypeEnum regTypeReq = req.getRegType();
        OrganRegType regType = EsignEnumUtil.getOrganRegType(regTypeReq);

        OrganizeBean organizeBean = new OrganizeBean();
        organizeBean.setEmail(req.getEmail());
        organizeBean.setMobile(req.getMobile());
        organizeBean.setName(req.getName());
        organizeBean.setOrganType(req.getOrganType());
        organizeBean.setOrganCode(req.getOrganCode());
        organizeBean.setUserType(req.getUserType());
        organizeBean.setAgentName(req.getAgentName());
        organizeBean.setAgentIdNo(req.getAgentIdNo());
        organizeBean.setLegalName(req.getLegalName());
        organizeBean.setLegalArea(req.getLegalArea());
        organizeBean.setLegalIdNo(req.getLegalIdNo());
        organizeBean.setRegType(regType);
        organizeBean.setAddress(req.getAddress());
        organizeBean.setScope(req.getScope());

        // 创建企业账号
        AddAccountResult addAccountResult = accountService.addAccount(organizeBean);
        this.checkEsignSuccess(addAccountResult);

        return AddAccountVO.builder()
                .accountId(addAccountResult.getAccountId())
                .fingerprint(addAccountResult.getFingerprint()).build();
    }

    @Override
    public AddAccountVO addPersonAccount(AddPersonAccountReq req) {

        LegalAreaTypeEnum areaTypeReq = req.getPersonArea();
        LegalAreaType personArea = EsignEnumUtil.getLegalAreaType(areaTypeReq);

        PersonBean person = new PersonBean();
        person.setEmail(req.getEmail());
        person.setMobile(req.getMobile());
        person.setName(req.getName());
        person.setIdNo(req.getIdNo());
        person.setPersonArea(personArea);
        person.setOrgan(req.getOrgan());
        person.setTitle(req.getTitle());
        person.setAddress(req.getAddress());
        person.setCountry(req.getCountry());
        person.setProvince(req.getProvince());
        person.setCity(req.getCity());
        person.setDepartment(req.getDepartment());

        AccountService accountService = serviceClient.accountService();
        // 创建企业账号
        AddAccountResult addAccountResult = accountService.addAccount(person);
        this.checkEsignSuccess(addAccountResult);

        return AddAccountVO.builder()
                .accountId(addAccountResult.getAccountId())
                .fingerprint(addAccountResult.getFingerprint()).build();
    }

    @Override
    public TemplateSealVO addTemplateSeal(TemplateSealReq req) {
        SealService sealService = serviceClient.sealService();

        SealColorEnum colorEnumReq = req.getColor();
        SealColor sealColor = EsignEnumUtil.getSealColor(colorEnumReq);

        OrganizeTemplateTypeEnum templateTypeReq = req.getTemplateType();
        OrganizeTemplateType templateType = EsignEnumUtil.getOrganizeTemplateType(templateTypeReq);

        AddSealResult addSealResult = sealService.addTemplateSeal(req.getAccountId(), templateType,
                sealColor, req.getHText(), req.getQText());

        this.checkEsignSuccess(addSealResult);

        return TemplateSealVO.builder().sealData(addSealResult.getSealData()).build();
    }

    @Override
    public TemplateSealVO addPersonSeal(PersonTemplateSealReq req) {
        SealService sealService = serviceClient.sealService();

        PersonTemplateTypeEnum templateTypeReq = req.getTemplateType();
        PersonTemplateType templateType = EsignEnumUtil.getPersonTemplateType(templateTypeReq);

        SealColorEnum colorReq = req.getColor();
        SealColor sealColor = EsignEnumUtil.getSealColor(colorReq);

        AddSealResult addSealResult = sealService.addTemplateSeal(req.getAccountId(), templateType, sealColor);

        this.checkEsignSuccess(addSealResult);

        return TemplateSealVO.builder().sealData(addSealResult.getSealData()).build();
    }

    @Override
    public void sendSignMobileCode3rd(SendSignMobileCode3rdReq req) {
        MobileService mobileService = serviceClient.mobileService();

        Result result = mobileService.sendSignMobileCode3rd(req.getAccountId(), req.getMobile(), req.isInland());

        this.checkEsignSuccess(result);

    }

    @Override
    public LocalSafeSignPDF3rdVO localSafeSignPDF3rd(LocalSafeSignPDF3rdReq req) {
        UserSignService userSignService = serviceClient.userSignService();

        SignSealTypeEnum signTypeReq = req.getSignType();
        SignType signType = EsignEnumUtil.getSignType(signTypeReq);

        // 签署文档信息
        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq pdfInfo = req.getPdfInfo();
        SignPDFStreamBean stream = new SignPDFStreamBean();
        stream.setStream(Base64.getDecoder().decode(pdfInfo.getFileBase64()));
        stream.setDstPdfFile(pdfInfo.getFileName());
        stream.setMarkBit(pdfInfo.getMarkBit());
        stream.setOwnerPassword(pdfInfo.getOwnerPassword());

        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPosReq posInfo = req.getPosInfo();
        PosBean signPos = new PosBean();
        signPos.setPosType(posInfo.getPosType());
        signPos.setPosPage(posInfo.getPosPage());
        signPos.setPosX(posInfo.getPosX());
        signPos.setPosY(posInfo.getPosY());
        signPos.setKey(posInfo.getKey());
        signPos.setWidth(posInfo.getWidth());
        signPos.setQrcodeSign(posInfo.isQrCodeSign());
        signPos.setCacellingSign(posInfo.isCancellingSign());
        signPos.setAddSignTime(posInfo.isAddSignTime());

        FileDigestSignResult result = userSignService.localSafeSignPDF3rd(
                req.getAccountId(), req.getSealData(), stream, signPos, signType, req.getMobile(),
                req.getCode());

        this.checkEsignSuccess(result);

        return LocalSafeSignPDF3rdVO.builder()
                .markBit(result.getMarkBit())
                .signServiceId(result.getSignServiceId())
                .pdfBase64(Base64.getEncoder().encodeToString(result.getStream()))
                .build();
    }

    @Override
    public LocalSafeSignPDF3rdVO multiPositionSign3rd(MultiPositionSignPDF3rdReq req) {
        UserSignService userSignService = serviceClient.userSignService();

        SignSealTypeEnum signTypeReq = req.getSignType();
        SignType signType = EsignEnumUtil.getSignType(signTypeReq);

        // 签署文档信息
        MultiPositionSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq pdfInfo = req.getPdfInfo();
        SignPDFStreamBean stream = new SignPDFStreamBean();
        stream.setStream(Base64.getDecoder().decode(pdfInfo.getFileBase64()));
        stream.setDstPdfFile(pdfInfo.getFileName());
        stream.setMarkBit(pdfInfo.getMarkBit());
        stream.setOwnerPassword(pdfInfo.getOwnerPassword());

        List<MultiPositionSignPDF3rdReq.LocalSafeSignPDF3rdPosReq> posInfoList = req.getPosInfoList();
        List<PosBean> posBeanList = new ArrayList<>();

        for(MultiPositionSignPDF3rdReq.LocalSafeSignPDF3rdPosReq posInfo: posInfoList){
            PosBean signPos = new PosBean();
            signPos.setPosType(posInfo.getPosType());
            signPos.setPosPage(posInfo.getPosPage());
            signPos.setPosX(posInfo.getPosX());
            signPos.setPosY(posInfo.getPosY());
            signPos.setKey(posInfo.getKey());
            signPos.setWidth(posInfo.getWidth());
            signPos.setQrcodeSign(posInfo.isQrCodeSign());
            signPos.setCacellingSign(posInfo.isCancellingSign());
            signPos.setAddSignTime(posInfo.isAddSignTime());

            posBeanList.add(signPos);
        }

        FileDigestSignResult result = userSignService.multiPositionSafeSign3rd(
                req.getAccountId(), req.getSealData(), stream, posBeanList, signType, req.getMobile(),
                req.getCode());

        this.checkEsignSuccess(result);

        return LocalSafeSignPDF3rdVO.builder()
                .markBit(result.getMarkBit())
                .signServiceId(result.getSignServiceId())
                .pdfBase64(Base64.getEncoder().encodeToString(result.getStream()))
                .build();
    }

    @Override
    public LocalSignPdfVO sealIdSign(LocalSignPdfReq req) {
        SelfSignService selfSignService = serviceClient.selfSignService();

        SignSealTypeEnum signTypeReq = req.getSignType();
        SignType signType = EsignEnumUtil.getSignType(signTypeReq);

        // 签署文档信息
        LocalSignPdfReq.LocalSafeSignPDF3rdPdfReq pdfInfo = req.getPdfInfo();
        SignPDFStreamBean stream = new SignPDFStreamBean();
        stream.setStream(Base64.getDecoder().decode(pdfInfo.getFileBase64()));
        stream.setDstPdfFile(pdfInfo.getFileName());
        stream.setMarkBit(pdfInfo.getMarkBit());
        stream.setOwnerPassword(pdfInfo.getOwnerPassword());

        LocalSignPdfReq.LocalSafeSignPDF3rdPosReq posInfo = req.getPosInfo();
        PosBean signPos = new PosBean();
        signPos.setPosType(posInfo.getPosType());
        signPos.setPosPage(posInfo.getPosPage());
        signPos.setPosX(posInfo.getPosX());
        signPos.setPosY(posInfo.getPosY());
        signPos.setKey(posInfo.getKey());
        signPos.setWidth(posInfo.getWidth());
        signPos.setQrcodeSign(posInfo.isQrCodeSign());
        signPos.setCacellingSign(posInfo.isCancellingSign());
        signPos.setAddSignTime(posInfo.isAddSignTime());

        FileDigestSignResult result = selfSignService.localSignPdfV2(stream, signPos,
                req.getSealId(), signType);

        this.checkEsignSuccess(result);

        return LocalSignPdfVO.builder()
                .markBit(result.getMarkBit())
                .signServiceId(result.getSignServiceId())
                .pdfBase64(Base64.getEncoder().encodeToString(result.getStream()))
                .build();
    }

    @Override
    public LocalVerifyPdfVO localVerifyPdf(LocalVerifyPdfReq req) throws JsonProcessingException {
        SignService signService = serviceClient.signService();

        VerifyPdfResult result = signService.localVerifyPdf(Base64.getDecoder().decode(req.getPdfBase64()));

        this.checkEsignSuccess(result);

        return mapper.readValue(mapper.writeValueAsString(result), LocalVerifyPdfVO.class);
    }

    /**
     * 获取数据，如果有错误则抛出异常
     */
    public void checkEsignSuccess(Result result) {

        if(result != null && result.getErrCode() == 0){
            return;
        }
        if(result == null){
            throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY);
        }

        LogPrintUtil.printFixLog("checkEsignSuccess", result);

        int errCode = result.getErrCode();
        String msg = result.getMsg();

        ResponseCode extraCode = CustomErrorCode.easyInfo(errCode + "", msg);
        String resultJson = JSON.toJSONString(extraCode);

        ResponseCode responseCode = CustomErrorCode.easyInfo(OuterExportErrorCode.ESIGN_ERROR, resultJson);
        throw BusinessException.createException(responseCode);
    }

    @Override
    public LocalSafeSignPDF3rdVO silentUserSealSign(SilentUserSealSignReq req) {
        UserSignService userSignService = serviceClient.userSignService();

        // 签署文档信息
        SilentUserSealSignReq.LocalSafeSignPDF3rdPdfReq pdfInfo = req.getPdfInfo();
        SignPDFStreamBean stream = new SignPDFStreamBean();
        stream.setStream(Base64.getDecoder().decode(pdfInfo.getFileBase64()));
        stream.setDstPdfFile(pdfInfo.getFileName());
        stream.setOwnerPassword(pdfInfo.getOwnerPassword());

        SilentUserSealSignReq.LocalSafeSignPDF3rdPosReq posInfo = req.getPosInfo();
        PosBean signPos = new PosBean();
        signPos.setPosType(posInfo.getPosType());
        signPos.setPosPage(posInfo.getPosPage());
        signPos.setPosX(posInfo.getPosX());
        signPos.setPosY(posInfo.getPosY());
        signPos.setKey(posInfo.getKey());
        signPos.setWidth(posInfo.getWidth());
        signPos.setQrcodeSign(posInfo.isQrCodeSign());
        signPos.setCacellingSign(posInfo.isCancellingSign());
        signPos.setAddSignTime(posInfo.isAddSignTime());

        SignSealTypeEnum signTypeReq = req.getSignType();
        SignType signType = EsignEnumUtil.getSignType(signTypeReq);

        FileDigestSignResult signResult = userSignService.localSignPDF(req.getAccountId(), req.getSealData(), stream, signPos, signType);

        this.checkEsignSuccess(signResult);

        LocalSafeSignPDF3rdVO pdfResult = LocalSafeSignPDF3rdVO.builder()
                .markBit(signResult.getMarkBit())
                .signServiceId(signResult.getSignServiceId())
                .pdfBase64(Base64.getEncoder().encodeToString(signResult.getStream()))
                .build();

        return pdfResult;
    }

}
