package com.teyuntong.outer.export.service.service.common.mq;

import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.SendResult;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 用于初始化多个 producer
 *
 * <AUTHOR>
 * @since 2024/11/19 13:33
 */

@Component
public class MqProducerService {
    private static OldMqProperties oldMqProperties;

    private static Producer oldMqProducer;
    public MqProducerService(OldMqProperties oldMqProperties){
        this.oldMqProperties = oldMqProperties;
        initOldMqProducer();
    }

    public  static void initOldMqProducer() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, oldMqProperties.getAccessKey());
        properties.setProperty(PropertyKeyConst.SecretKey, oldMqProperties.getSecretKey());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, oldMqProperties.getNameSrvAddr());
        oldMqProducer = ONSFactory.createProducer(properties);
        oldMqProducer.start();
    }

    public SendResult sendNormal(MqMessage mqMessage){
        SendResult sendResult = oldMqProducer.send(mqMessage);
        return sendResult;
    }

}
