package com.teyuntong.outer.export.service.service.rpc.corporate;

import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.corporate.service.CorporateBaseInfoRpcService;
import com.teyuntong.outer.export.service.client.corporate.vo.CorporateBaseInfoBean;
import com.teyuntong.outer.export.service.client.corporate.vo.IcBasicInfoNormalResp;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.biz.corporate.service.CorporateBaseInfoService;
import com.teyuntong.outer.export.service.service.biz.tyanyancha.client.TYCApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class CorporateBaseInfoRpcServiceImpl implements CorporateBaseInfoRpcService {
    @Autowired
    private CorporateBaseInfoService corporateBaseInfoService;

    @Autowired
    private TYCApi tycApi;

    @Override
    public CorporateBaseInfoBean getCorporate(String keyword) {
        try {
            log.info("企业基本信息查询api，keyword={}", keyword);
            if (StringUtils.isBlank(keyword)) {
                throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
            }
            return corporateBaseInfoService.addAndGetCorporateBaseInfo(keyword);
        } catch (Exception e) {
            log.error("异常信息", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }
    }

    @Override
    public IcBasicInfoNormalResp getIcBasicInfoNormal(String keyword) throws IOException {
        try {
            log.info("企业基本信息查询api，keyword={}", keyword);
            if (StringUtils.isBlank(keyword)) {
                throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
            }

            Call<IcBasicInfoNormalResp> call = tycApi.getIcBasicInfoNormal(keyword);
            Response<IcBasicInfoNormalResp> response = call.execute();
            log.info("天眼查企业基本信息api，keyword={},response={}", keyword, response);

            if (response.isSuccessful()) {
                return response.body();
            } else {
                throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
            }
        } catch (Exception e) {
            log.error("异常信息", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }
    }
}
