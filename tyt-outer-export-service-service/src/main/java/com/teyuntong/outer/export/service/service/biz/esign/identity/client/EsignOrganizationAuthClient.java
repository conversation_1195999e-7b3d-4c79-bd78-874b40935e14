package com.teyuntong.outer.export.service.service.biz.esign.identity.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */
@RetrofitClient(value = EsignIdentityRetrofitSupplier.class, path = "v2/identity/auth/")
@UseInterceptor(LogInfoInterceptor.class)
public interface EsignOrganizationAuthClient {

    @POST("api/organization/enterprise/fourFactors")
    Call<IdentityAuthBaseResult<EnterpriseFourFactorsApiResp>> enterpriseFourFactors(@Body EnterpriseFourFactorsApiReq req);

    @POST("api/organization/threeFactors")
    Call<IdentityAuthBaseResult<EnterpriseThreeFactorsApiResp>> enterpriseThreeFactors(@Body EnterpriseThreeFactorsApiReq req);

    @POST("api/organization/{flowId}/legalRepSign")
    Call<IdentityAuthBaseResult<Object>> legalRepSign(@Path("flowId") String flowId, @Body LegalRepSignApiReq req);

    @GET("pub/organization/{flowId}/signUrl")
    Call<IdentityAuthBaseResult<SignUrlApiResp>> signUrl(@Path("flowId") String flowId);

    @GET("pub/organization/{flowId}/legalRepSignResult")
    Call<IdentityAuthBaseResult<LegalRepSignResultApiResp>> legalRepSignResult(@Path("flowId") String flowId);

}
