package com.teyuntong.outer.export.service.service.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

/**
 * spring-cloud-circuitbreaker-resilience4j示例
 *
 * <AUTHOR>
 * @since 2023/10/17 13:26
 */
@FeignClient(name = "tyt-user", contextId = "fallBackTestService", url = "https://httpbin.org")
public interface CircuitBreakerTestService {

    @GetMapping(path = "/delay/{delayInSeconds}", produces = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Object> getBinWithDelayInSeconds(@PathVariable("delayInSeconds") int delayInSeconds);

    @Slf4j
    @Component
    class CircuitBreakerTestServiceFallback implements FallbackFactory<CircuitBreakerTestService> {

        @Override
        public CircuitBreakerTestService create(Throwable cause) {
            return delayInSeconds -> {
                log.warn(cause.getMessage());
                return Map.of("1", 1);
            };
        }
    }
}