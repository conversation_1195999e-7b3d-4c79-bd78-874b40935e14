package com.teyuntong.outer.export.service.service.rpc.esign;


import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.*;
import com.teyuntong.outer.export.service.client.esign.vo.identity.*;
import com.teyuntong.outer.export.service.service.biz.esign.identity.client.EsignOrganizationAuthClient;
import com.teyuntong.outer.export.service.service.biz.esign.identity.client.IdentityAuthBaseResult;
import com.teyuntong.outer.export.service.service.common.utils.RetrofitUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import retrofit2.Response;

import java.io.IOException;

/**
 * 易签宝-企业用户认证
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 *
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/esignIdentityAuthOrganization")
public class EsignOrganizationAuthController {

    private final EsignOrganizationAuthClient esignOrganizationAuthClient;

    /**
     * 【4要素】企业核身认证
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ikp0qg">官方文档链接</a>
     */
    @PostMapping("/enterprise/fourFactors")
    public WebResult<EnterpriseFourFactorsVO> enterpriseFourFactors(@RequestBody @Validated EnterpriseFourFactorsReq req) throws IOException {
        EnterpriseFourFactorsApiReq enterpriseFourFactorsApiReq = new EnterpriseFourFactorsApiReq();
        BeanUtils.copyProperties(req, enterpriseFourFactorsApiReq);
        Response<IdentityAuthBaseResult<EnterpriseFourFactorsApiResp>> response =
                esignOrganizationAuthClient.enterpriseFourFactors(enterpriseFourFactorsApiReq).execute();
        EnterpriseFourFactorsApiResp body = RetrofitUtils.getDataDetail(response);
        return WebResult.success(EnterpriseFourFactorsVO.builder().flowId(body.getFlowId()).build());
    }

    /**
     * 【3要素】企业核身认证
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/dzvhtp">官方文档链接</a>
     */
    @PostMapping("/enterpriseThreeFactors")
    public WebResult<EnterpriseThreeFactorsVO> enterpriseThreeFactors(@RequestBody @Validated EnterpriseThreeFactorsReq req) throws IOException {
        EnterpriseThreeFactorsApiReq enterpriseThreeFactorsApiReq = new EnterpriseThreeFactorsApiReq();
        BeanUtils.copyProperties(req, enterpriseThreeFactorsApiReq);
        Response<IdentityAuthBaseResult<EnterpriseThreeFactorsApiResp>> response =
                esignOrganizationAuthClient.enterpriseThreeFactors(enterpriseThreeFactorsApiReq).execute();
        EnterpriseThreeFactorsApiResp body = RetrofitUtils.getDataDetail(response);

        EnterpriseThreeFactorsVO threeFactorsVO = new EnterpriseThreeFactorsVO();
        threeFactorsVO.setFlowId(body.getFlowId());

        return WebResult.success(threeFactorsVO);
    }

    /**
     * 【授权书认证】发起授权书签署
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/dyyohd">官方文档链接</a>
     */
    @PostMapping("/legalRepSign")
    public WebResult<Object> legalRepSign(@RequestBody @Validated LegalRepSignReq req) throws IOException {
        LegalRepSignApiReq enterpriseFourFactorsApiReq = new LegalRepSignApiReq();
        BeanUtils.copyProperties(req, enterpriseFourFactorsApiReq);
        Response<IdentityAuthBaseResult<Object>> response =
                esignOrganizationAuthClient.legalRepSign(req.getFlowId(), enterpriseFourFactorsApiReq).execute();

        RetrofitUtils.getDataDetail(response);

        return WebResult.success();
    }

    /**
     * 【授权书认证】获取签署链接
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/cgiwtx">官方文档链接</a>
     */
    @GetMapping("/signUrl")
    public WebResult<SignUrlResp> signUrl(@RequestParam String flowId) throws IOException {
        Response<IdentityAuthBaseResult<SignUrlApiResp>> response =
                esignOrganizationAuthClient.signUrl(flowId).execute();

        SignUrlApiResp signUrlApiResp = RetrofitUtils.getDataDetail(response);

        return WebResult.success(SignUrlResp.builder().signUrl(signUrlApiResp.getSignUrl()).build());
    }

    /**
     * 【授权书认证】查询签署状态
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ih16c4">官方文档链接</a>
     */
    @GetMapping("/legalRepSignResult")
    public WebResult<LegalRepSignResultResp> legalRepSignResult(@RequestParam String flowId) throws IOException {
        Response<IdentityAuthBaseResult<LegalRepSignResultApiResp>> response =
                esignOrganizationAuthClient.legalRepSignResult(flowId).execute();

        LegalRepSignResultApiResp signUrlApiResp = RetrofitUtils.getDataDetail(response);

        return WebResult.success(LegalRepSignResultResp.builder().status(signUrlApiResp.getStatus())
                .message(signUrlApiResp.getMessage()).build());
    }
}
