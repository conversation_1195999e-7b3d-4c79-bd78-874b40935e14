package com.teyuntong.outer.export.service.service.common.config;

import com.teyuntong.outer.export.service.service.rpc.modify.ModifyResourceOrConfigRpcServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 钉钉配置刷新监听器
 * 监听RefreshScope刷新事件，当配置刷新时更新DingTalkClient
 */
@Slf4j
@Component
public class DingTalkConfigRefresher implements ApplicationListener<RefreshScopeRefreshedEvent> {

    @Autowired
    private ModifyResourceOrConfigRpcServiceImpl modifyResourceOrConfigRpcService;

    @Override
    public void onApplicationEvent(RefreshScopeRefreshedEvent event) {
        log.info("RefreshScope refreshed, updating DingTalkClient");
        modifyResourceOrConfigRpcService.refreshDingTalkClient();
    }
}
