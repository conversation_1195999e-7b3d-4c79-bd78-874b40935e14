package com.teyuntong.outer.export.service.service.biz.manbang.ocr.service;

import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportQCVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQCVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportVerifyResultVO;

/**
 * 集团验真服务类
 *
 * <AUTHOR>
 * @since 2024-4-26 14:17:45
 */
public interface GroupVerifyService {

    /**
     * 道路运输证验真
     *
     * @param dto 验真入参
     * @return 验真结果
     */
    RoadTransportVerifyResultVO roadTransportVerify(RoadTransportVerifyDTO dto);

    /**
     * 道路运输道路运输从业资格证验真
     *
     * @param dto 验真入参
     * @return 验真结果
     */
    RoadTransportQCVerifyResultVO roadTransportQCVerify(RoadTransportQCVerifyDTO dto);
}
