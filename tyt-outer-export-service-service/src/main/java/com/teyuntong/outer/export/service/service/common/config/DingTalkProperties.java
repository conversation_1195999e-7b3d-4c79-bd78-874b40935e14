package com.teyuntong.outer.export.service.service.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 钉钉机器人配置属性
 * 使用@RefreshScope注解，当配置中心的配置发生变化时，会自动刷新这个Bean
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "dingtalk.robot")
public class DingTalkProperties {
    
    /**
     * 钉钉机器人webhook URL
     */
    private String webhookUrl;
}
