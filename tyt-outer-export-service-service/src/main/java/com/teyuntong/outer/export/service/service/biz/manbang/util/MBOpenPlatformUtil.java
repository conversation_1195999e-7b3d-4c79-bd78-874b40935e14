package com.teyuntong.outer.export.service.service.biz.manbang.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.remote.common.TytConfigRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.ssl.SSLContexts;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.Arrays;
import java.util.Collection;

import static com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants.*;

/**
 * 满帮https证书工具
 *
 * <AUTHOR>
 * @since 2024/04/25 16:02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MBOpenPlatformUtil {

    private static final String REDIS_MB_ACCESS_TOKEN_KEY = "mb:openplatform:access:token";

    private static final String REDIS_MB_ACCESS_TOKEN_CLIENT_KEY = "mb:openplatform:access_token";

    private final StringRedisTemplate stringRedisTemplate;

    private final TytConfigRemoteService configRpcService;

    /**
     * 获去信任自签证书的trustManager
     *
     * @param is 自签证书输入流
     * @param password 密码
     * @return 信任自签证书的trustManager
     */
    public X509TrustManager buildTrustManager(KeyStore keyStore, InputStream is,
                                                     String password)
            throws GeneralSecurityException {
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        // 通过证书工厂得到自签证书对象集合
        Collection<? extends Certificate> certificates = certificateFactory
                .generateCertificates(is);
        if (certificates.isEmpty()) {
            throw new IllegalArgumentException("No trusted certificates");
        }

        int index = 0;
        // 将证书放入keystore中
        for (Certificate certificate : certificates) {
            String certificateAlias = Integer.toString(index++);
            keyStore.setCertificateEntry(certificateAlias, certificate);
        }
        // Use it to build an X509 trust manager.
        // 使用包含自签证书信息的keyStore去构建一个X509TrustManager
        KeyManagerFactory keyManagerFactory = KeyManagerFactory
                .getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(keyStore, password.toCharArray());

        TrustManagerFactory trustManagerFactory =
                TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(keyStore);

        TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
        if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
            throw new IllegalStateException(
                    "Unexpected default trust managers:" + Arrays.toString(trustManagers));
        }
        return (X509TrustManager) trustManagers[0];
    }

    public KeyStore getKeyStore(InputStream is, char[] password)
            throws GeneralSecurityException {
        try {
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(is, password);
            return keyStore;
        } catch (IOException e) {
            throw new AssertionError(e);
        }
    }

    public SSLSocketFactory getSSLSocketFactory(KeyStore keyStore, String password) {
        try {
            SSLContext sslcontext =
                    SSLContexts.custom().loadKeyMaterial(keyStore, password.toCharArray()).build();
            // 获得sslSocketFactory对象
            return sslcontext.getSocketFactory();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    public String doPost(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        log.info("mb open platform request ymmOpenPlatFormHost:{}, requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", ymmOpenPlatFormHost,requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            is = this.getClass().getResourceAsStream(crtUrl);
            pis = this.getClass().getResourceAsStream(p12Path);

            KeyStore keyStore = getKeyStore(pis, password.toCharArray());
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject responseJson = JSON.parseObject(responseBody.string());
                log.info("mb open platform response:{}", JSON.toJSONString(responseJson));
                if (responseJson != null) {
                    int code = responseJson.getIntValue("code");
                    if (code == 1) {
                        return responseJson.getString("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("doOcr error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }

    public String doPostForDecode(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_CLIENT_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        String appName = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_APP_NAME);
        String appToken = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_APP_TOKEN);
        log.info("mb open platform request requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            pis = this.getClass().getResourceAsStream(p12Path);
            KeyStore keyStore = getKeyStore(pis, password.toCharArray());

            is = this.getClass().getResourceAsStream(crtUrl);
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);

            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    addHeader(APP_NAME_STR, appName).
                    addHeader(APP_TOKEN_STR, appToken).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject responseJson = JSON.parseObject(responseBody.string());
                log.info("mb open platform response:{}", JSON.toJSONString(responseJson));
                if (responseJson != null) {
                    int result = responseJson.getIntValue("result");
                    if (result == 1) {
                        return responseJson.getString("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("doOcr error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }



    public String doPostLocation(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        log.info("mb location open platform request ymmOpenPlatFormHost:{}, requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", ymmOpenPlatFormHost,requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            is = this.getClass().getResourceAsStream(crtUrl);
            pis = this.getClass().getResourceAsStream(p12Path);

            KeyStore keyStore = getKeyStore(pis, password.toCharArray());
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject responseJson = JSON.parseObject(responseBody.string());
                log.info("mb location open platform response:{}", JSON.toJSONString(responseJson));
                if (responseJson != null) {
                    if (responseJson.getBoolean("success")) {
                        return responseJson.getString("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("doOcr error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }


    public String doPostAppPosition(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        log.info("mb doPostAppPosition open platform request ymmOpenPlatFormHost:{}, requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", ymmOpenPlatFormHost,requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            is = this.getClass().getResourceAsStream(crtUrl);
            pis = this.getClass().getResourceAsStream(p12Path);

            KeyStore keyStore = getKeyStore(pis, password.toCharArray());
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                String responseStr = responseBody.string();
                log.info("mb doPostAppPosition open platform response:{}", responseStr);
                return responseStr;
            }
        } catch (Exception e) {
            log.error("doOcr error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }

    public String doPostLbsAuth(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        log.info("mb open platform doPostLbsAuth request ymmOpenPlatFormHost:{}, requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", ymmOpenPlatFormHost,requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            is = this.getClass().getResourceAsStream(crtUrl);
            pis = this.getClass().getResourceAsStream(p12Path);

            KeyStore keyStore = getKeyStore(pis, password.toCharArray());
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject responseJson = JSON.parseObject(responseBody.string());
                log.info("mb open platform doPostLbsAuth response:{}", JSON.toJSONString(responseJson));
                if (responseJson != null) {
                    String code = responseJson.getString("code");
                    if ("**********".equals(code)) {
                        return responseJson.getString("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("doOcr error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }


    public String doPostAccount(String requestUrl, RequestBody requestBody) {
        String token = stringRedisTemplate.opsForValue().get(REDIS_MB_ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrorCode.UN_AUTHENTICATED);
        }
        String ymmOpenPlatFormHost = configRpcService.getStringValue(Constants.YMM_OPEN_PLATFORM_HOST);
        String p12Path = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_P12_PATH);
        String crtUrl = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_CRT_PATH);
        String password = configRpcService.getStringValue(Constants.MB_OPENPLATFORM_PASSWORD);
        log.info("mb account open platform request ymmOpenPlatFormHost:{}, requestUrl:{}, requestBody:{}, token:{}, crtUrl:{}, p12Path:{}", ymmOpenPlatFormHost,requestUrl, requestBody, token, crtUrl, p12Path);
        InputStream is = null;
        InputStream pis = null;
        try {
            is = this.getClass().getResourceAsStream(crtUrl);
            pis = this.getClass().getResourceAsStream(p12Path);

            KeyStore keyStore = getKeyStore(pis, password.toCharArray());
            X509TrustManager trustManager = buildTrustManager(keyStore, is, password);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(
                            getSSLSocketFactory(keyStore, password),
                            trustManager).build();

            Request request = new Request.Builder().
                    url(ymmOpenPlatFormHost + requestUrl).
                    post(requestBody).
                    addHeader(ACCESS_TOKEN_STR, token).
                    build();

            Response response = okHttpClient.newCall(request).execute();
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                JSONObject responseJson = JSON.parseObject(responseBody.string());
                log.info("mb account open platform response:{}", JSON.toJSONString(responseJson));
                if (responseJson != null) {
                    if ("**********".equals(responseJson.getString("code"))) {
                        return responseJson.getString("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("account error:", e);
        } finally {
            try {
                if (pis != null) {
                    pis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("trust manager input stream close error:", e);
            }
        }
        return null;
    }





}
