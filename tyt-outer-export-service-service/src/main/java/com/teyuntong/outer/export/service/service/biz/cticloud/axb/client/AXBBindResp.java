package com.teyuntong.outer.export.service.service.biz.cticloud.axb.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/12 上午10:26
 */
@NoArgsConstructor
@Data
public class AXBBindResp implements Serializable {

    /**
     * subId：绑定关系唯一标识，用于更新和解绑
     */
    @JsonProperty("subId")
    private String subId;
    /**
     * requestId：请求id
     */
    @JsonProperty("requestId")
    private String requestId;
    /**
     * telX：天润分配的虚拟号X
     */
    @JsonProperty("telX")
    private String telX;
}
