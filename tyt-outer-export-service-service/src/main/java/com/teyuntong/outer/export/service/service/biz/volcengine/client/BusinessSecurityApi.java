package com.teyuntong.outer.export.service.service.biz.volcengine.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.service.biz.volcengine.client.req.IdCardTwoElementVerifyReq;
import com.volcengine.model.request.RiskDetectionRequest;
import com.volcengine.model.response.ElementVerifyResponseV2;
import com.volcengine.service.businessSecurity.BusinessSecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/01/10 15:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties(VolcengineProperties.class)
public class BusinessSecurityApi {

    private static final String ID_CARD_TWO_ELEMENT_VERIFY = "idcard_two_element_verify";
    private final BusinessSecurityService businessSecurityService;
    private final VolcengineProperties volcengineProperties;
    private final ObjectMapper mapper;

    public ElementVerifyResponseV2 idCardTwoElementVerify(IdCardTwoElementVerifyReq req) {
        try {
            RiskDetectionRequest riskDetectionRequest = new RiskDetectionRequest();
            riskDetectionRequest.setService(ID_CARD_TWO_ELEMENT_VERIFY);
            riskDetectionRequest.setAppId(volcengineProperties.getBusinessSecurity().getAppId());
            riskDetectionRequest.setParameters(mapper.writeValueAsString(req));
            //明文请求方式
            ElementVerifyResponseV2 elementVerifyResponseV2 =
                    businessSecurityService.ElementVerifyV2(riskDetectionRequest);
            log.info("火山二要素认证请求成功, req: {}, resp: {}", req, elementVerifyResponseV2);
            return elementVerifyResponseV2;
        } catch (Exception e) {
            log.error("火山二要素认证请求失败, req: {}", req, e);
        }
        return null;
    }
}
