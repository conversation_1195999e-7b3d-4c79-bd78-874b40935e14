package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.*;

/**
 * 翔和翎 司机、人车合照、司机授权相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlDriverService {

    /**
     * 新增驾驶员
     *
     * @param driverEntity 司机信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveDriver(XhlDriverEntity driverEntity) throws Exception;

    /**
     * 更新驾驶员
     *
     * @param driverEntity 司机信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> updateDriver(XhlDriverEntity driverEntity) throws Exception;

    /**
     * 查询驾驶员
     *
     * @param idCardNo 身份证号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryDriver(String idCardNo) throws Exception;

    /**
     * 新增驾驶员银行卡信息
     *
     * @param driverEntity 司机信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveBank(XhlDriverEntity driverEntity) throws Exception;

    /**
     * 驾驶员解绑银行卡
     *
     * @param driverEntity 司机信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> deleteBank(XhlDriverEntity driverEntity) throws Exception;

    /**
     * 新增人车合照
     *
     * @param driverTruckEntity 人车合照实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveDriverTruck(XhlDriverTruckEntity driverTruckEntity) throws Exception;

    /**
     * 更新人车合照
     *
     * @param driverTruckEntity 人车合照实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> updateDriverTruck(XhlDriverTruckEntity driverTruckEntity) throws Exception;

    /**
     * 查询人车合照
     *
     * @param idCardNo    身份证号
     * @param plateNumber 车牌号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryDriverTruck(String idCardNo, String plateNumber) throws Exception;

    /**
     * 新增司机授权
     *
     * @param driverEntity 司机信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveEmpowerDriver(XhlDriverEntity driverEntity) throws Exception;

    /**
     * 查询司机授权
     *
     * @param idCardNo 身份证号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryEmpowerDriver(String idCardNo) throws Exception;

    /**
     * 司机人脸认证
     */
    XhlDataResult<Object> saveAppFaceAuth(XhlFaceAuthEntity xhlFaceAuthEntity);

    /**
     * 司机人脸结果查询
     */
    XhlDataResult<Object> queryAppFaceResult(XhlFaceAuthEntity xhlFaceAuthEntity);
}
