package com.teyuntong.outer.export.service.service.biz.manbang.constant;


import java.util.List;

/**
* 用户ID前缀常量类
* <AUTHOR>
* @since 2024/4/3 11:01
*/
public class Constants {

    /**
     * 用户ID前缀
     */
    public static final String GROUP_USER_PREFIX = "46_";

    /**
     * 企业ID前缀
     */
    public static final String GROUP_ENTERPRISE_PREFIX = "86_";

    /**
     * 企业网商开户主体名称
     */
    public static final String ENTERPRISE_MAIN_NAME="甘肃邦利德网络科技有限公司";

    public static final String HCB_USER_PREFIX = "1_";

    public static final String APP_SERVICE_ID_KEY = "appServiceId";

    public static final String IMG_URL_KEY = "imgUrl";

    public static final String VEHICLE_NO_KEY = "vehicleNo";

    public static final String VEHICLE_VIN_KEY = "vehicleVIN";

    public static final String ID_CARD_KEY = "idCard";

    public static final String CERTIFICATE_CODE_KEY = "certificateCode";

    public static final String DECODE_VALUE_KEY = "value";

    public static final String ACCESS_TOKEN_STR = "accessToken";

    public static final String APP_NAME_STR = "app-name";

    public static final String APP_TOKEN_STR = "app-token";

    public static final String VEHICLE_INFO_LIST = "vehicleInfoList";


    public static final String VEHICLE_NO = "vehicleNo";

    public static final String VEHICLE_NOS = "vehicleNos";

    public static final String START_TIME = "startTime";

    public static final String END_TIME = "endTime";

    public static final String CHARGE_CHANNELS = "chargeChannels";

    public static final String USER_ID = "userId";

    public static final String USER_IDS = "userIds";


    public static final String TRUCK_NUMBER = "truckNumber";


    public static final String MOBILE = "mobile";

    public static final String ACCOUNT_ID = "accountId";

    public static final String LICENSE_TYPES = "licenseTypes";









    /**
     * 运满满开放平台域名配置KEY
     */
    public static final String YMM_OPEN_PLATFORM_HOST = "YMM_OPEN_PLATFORM_HOST";
    public static final String MB_OPENPLATFORM_P12_PATH = "mb.openplatform.p12.path";
    public static final String MB_OPENPLATFORM_CRT_PATH = "mb.openplatform.crt.path";
    public static final String MB_OPENPLATFORM_PASSWORD = "mb.openplatform.password";

    public static final String MB_OPENPLATFORM_APP_NAME = "mb.openplatform.app-name";

    public static final String MB_OPENPLATFORM_APP_TOKEN = "mb.openplatform.app-token";

    public static final String ROAD_TRANSPORT_BACK_OCR_URL = "/ocr/roadTransportBackOcr";

    public static final String NEW_CARGO_SENSITIVE_CHECK_URL = "/NewCargoSensitiveCheck";

    public static final String LAST_LOCATION_URL = "/trace/getVehicleLastLocationRedis";

    public static final String LOCATION_TRACE_URL = "/trace/queryDriverTrace";

    public static final String APP_LAST_POSITION_URL = "/trace/getLastPositions";

    public static final String LIST_SINOIOV_LAST_LOCATION_URL = "/trace/listSinoiovLastLocation";


    public static final String LBS_AUTH_URL = "/truck/lbsAuth/query";

    public static final String VEHICLE_LICENSE_DEPUTY_PAGE_BACK_OCR_URL = "/ocr/vehicleLicenseDeputyPageBackOcr";

    public static final String ROAD_TRANSPORT_QUALIFICATION_CERTIFICATE_OCR_URL = "/ocr/roadTransportQualificationCertificateOcr";

    public static final String ROAD_TRANSPORT_QUALIFICATION_CERTIFICATE_BACK_OCR_URL = "/ocr/roadTransportQualificationCertificateBackOcr";

    public static final String BUSINESS_LICENSE_OCR_URL = "/ocr/businessLicenseOcr";

    public static final String ROAD_TRANSPORT_VERIFY_URL = "/ps/certificate-verify/road-transport-verify";

    public static final String ROAD_TRANSPORT_QC_VERIFY_URL = "/ps/certificate-verify/road-transport-qc-verify";

    public static final String YMM_OPEN_PLATFORM_DECODE_URL = "/thaad/getOriginalTextBatchWithToken";

    /**
     * 根据手机号查询用户信息
     */
    public static final String YMM_OPEN_PLATFORM_QUERY_ACCOUNTS_MOBILE_URL = "/ucInfoCenter/queryAccountsByMobile";

    /**
     * 根据证件类型列表和用户Id查询证件信息
     */
    public static final String YMM_OPEN_PLATFORM_QUERY_LICENSE_TYPES_URL = "/ucInfoCenter/queryLicenseByTypes";

}
