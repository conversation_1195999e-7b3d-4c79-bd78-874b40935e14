package com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cticloud_axb_log")
public class CticloudAxbLog implements Serializable {
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 绑定关系唯一标识，用于更新和解绑
     */
    @TableField("sub_id")
    private String subId;

    /**
     * 请求body
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 请求param
     */
    @TableField("request_param")
    private String requestParam;

    /**
     * 响应
     */
    private String response;

    /**
     * 请求是否成功 0没成功 1成功
     */
    @TableField("is_request_success")
    private Boolean isRequestSuccess;

    /**
     * 第三方返回是否成功 0没成功 1成功
     */
    @TableField("is_cticloud_success")
    private Boolean isCticloudSuccess;

    /**
     * 请求类型 1 绑定 2解绑 3更新
     */
    @TableField("request_type")
    private Integer requestType;

    /**
     * 请求url
     */
    @TableField("request_url")
    private String requestUrl;

    private static final long serialVersionUID = 1L;
}