package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.impl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.cache.redis.ExpireCache;
import com.teyuntong.outer.export.service.client.manbang.api.enterprise.bean.CompanyRealNameReq;
import com.teyuntong.outer.export.service.client.manbang.vo.EnterpriseRealNameStatusVo;
import com.teyuntong.outer.export.service.client.model.FileDataInfo;
import com.teyuntong.outer.export.service.service.biz.manbang.GateWayClient;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.OutEnterpriseRealService;
import com.teyuntong.outer.export.service.service.common.utils.ManbangResponseUtil;
import com.teyuntong.outer.export.service.service.common.utils.PlatCommonUtil;
import com.wlqq.wallet.gateway.client.enums.ServiceKind;
import com.wlqq.wallet.gateway.client.request.mgs.CompanyRealNameRequest;
import com.wlqq.wallet.gateway.client.request.mgs.FileUploadRequest;
import com.wlqq.wallet.gateway.client.request.mgs.RealNameReviewStatusRequest;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.FileUploadResponse;
import com.wlqq.wallet.gateway.client.response.mgs.RealNameReviewStatusResponse;
import com.wlqq.wallet.gateway.client.util.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/26 14:31
 */
@Slf4j
@Service
public class OutEnterpriseRealServiceImpl implements OutEnterpriseRealService {

    @Autowired
    private GateWayClient client;

    @Override
    public String uploadFile(FileDataInfo fileDataInfo) {
        try {

            String fileName = fileDataInfo.getFileName();
            String dataBase64 = fileDataInfo.getBase64();

            byte[] fileBytes = PlatCommonUtil.decodeBase64(dataBase64);

            FileInfo fileInfo = new FileInfo();
            fileInfo.setFile(fileBytes);
            fileInfo.setFileName(fileName);

            String fileKey = "dataFile";
            Map<String, FileInfo> fileInfoMap = new HashMap<>();
            fileInfoMap.put(fileKey, fileInfo);

            FileUploadRequest request = new FileUploadRequest();
            request.setFileMap(fileInfoMap);
            FileUploadResponse uploadResponse = client.uploadFile(ServiceKind.mgs_upload_file, request, FileUploadResponse.class);

            log.info("manabang_uploadFile_response : " + JSON.toJSONString(uploadResponse));

            if(ManbangResponseUtil.check(uploadResponse)){
                Map<String, String> fileMap = uploadResponse.getFileMap();

                if(fileMap != null){
                    String fileUrl = fileMap.get(fileKey);
                    return fileUrl;
                }

            }
        } catch (Exception e) {
            log.error("manabang_uploadFile_error", e);
        }
        return null;
    }

    @Override
    public void companyRealName(CompanyRealNameReq realNameReq) {

        CompanyRealNameRequest request = new CompanyRealNameRequest();

        BeanUtils.copyProperties(realNameReq, request);

        BaseResponse response = client.doService(ServiceKind.company_real_name, request, BaseResponse.class);

        boolean check = ManbangResponseUtil.check(response);
        log.info("company_real_name_result : {}", check);
    }

    @Override
    @Cacheable(value="OutEnterpriseReal::getRealNameStatus", key = "#identityNo", unless="#result == null")
    @ExpireCache(ttl = 1, timeUint = TimeUnit.MINUTES)
    public EnterpriseRealNameStatusVo getRealNameStatus(String identityNo) {

        RealNameReviewStatusRequest request = new RealNameReviewStatusRequest();
        request.setIdentityNo(identityNo);
        request.setIdentityType("UID");

        RealNameReviewStatusResponse response = client.doService(ServiceKind.real_name_review_status, request, RealNameReviewStatusResponse.class);

        if(ManbangResponseUtil.check(response)){
            EnterpriseRealNameStatusVo enterpriseRealNameStatus = new EnterpriseRealNameStatusVo();

            String reviewStatus = response.getReviewStatus();
            String subStatus = response.getSubStatus();
            String message = response.getMessage();

            enterpriseRealNameStatus.setReviewStatus(reviewStatus);
            enterpriseRealNameStatus.setSubStatus(subStatus);
            enterpriseRealNameStatus.setMessage(message);

            return enterpriseRealNameStatus;
        }
        return null;
    }
}
