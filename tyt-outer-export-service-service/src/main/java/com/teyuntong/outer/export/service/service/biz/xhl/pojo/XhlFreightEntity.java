package com.teyuntong.outer.export.service.service.biz.xhl.pojo;

import lombok.Data;

/**
 * 翔和翎 运费实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlFreightEntity {

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 三方运单号
     */
    private String tpWaybillNo;

    /**
     * 货达交易流水号
     */
    private String hdRunningNumber;

    /**
     * 承运费，每次支付金额，单位元
     */
    private String freight;

    /**
     * 服务费，单位元
     */
    private String serviceCharge;

    /**
     * 收款类型，1司机
     */
    private String agentWay;

    /**
     * 支付方式，1现付2到付3回付，默认到付2
     */
    private String payType;

    /**
     * 持卡人（收款人）
     */
    private String ownerName;

    /**
     * 持卡人（收款人）身份证号
     */
    private String idCardNo;

    /**
     * 持卡人（收款人）银行卡号
     */
    private String bankNo;

    /**
     * 持卡人银行名称
     */
    private String bankTypeName;

    /**
     * 银行行号
     */
    private String bankBranchNo;


    /**
     * 托运费(含税)
     */
    private String shippingCharge;

    /**
     * 发车重量
     */
    private String departWeight;

    /**
     * 签收重量
     */
    private String signWeight;

    /**
     * 结算重量
     */
    private String settleWeight;
    /**
     * 支付时间，时间戳格式
     */
    private String payTime;
    /**
     * 支付类型，1:现付，2:到付，3:回付
     */
    private String type;
    /**
     * 状态，1:待付款，2:付款中，3:失败，4打款成功
     */
    private String payStatus;
    /**
     * 支付结果
     */
    private String payLog;
    /**
     * 支付回单地址
     */
    private String bankReceiptUrl;
}
