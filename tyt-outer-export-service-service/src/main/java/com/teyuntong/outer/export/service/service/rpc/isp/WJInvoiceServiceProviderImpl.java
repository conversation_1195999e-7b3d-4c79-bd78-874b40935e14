//package com.teyuntong.outer.export.service.service.rpc.isp;
//
//import com.teyuntong.infra.common.definition.bean.WebResult;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeRequest;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeResponse;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.EditWaybillThreeRequest;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.EditWaybillThreeResponse;
//import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * 开票-湖北我家开放接口服务实现类
// *
// * <AUTHOR>
// * @since 2024/07/17 13:21
// */
//@Slf4j
//@Service("wjInvoiceServiceProviderImpl")
//@RequiredArgsConstructor
//public class WJInvoiceServiceProviderImpl extends AbstractInvoiceServiceProvider {
//
//    @Autowired
//    private  HBWJService hbwjService;
//
//    /**
//     * 创建运单接口
//     *
//     * <AUTHOR>
//     * @param createWaybillThreeRequest 创建运单请求对象
//     * @return CreateWaybillThreeResponse 创建运单返回对象
//     */
//    @Override
//    public WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception {
//        return hbwjService.addWaybillThree(createWaybillThreeRequest);
//    }
//
//    @Override
//    public EditWaybillThreeResponse editWaybillThree(EditWaybillThreeRequest editWaybillThreeRequest) throws Exception {
//        return null;
//    }
//}
