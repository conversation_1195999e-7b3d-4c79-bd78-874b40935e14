package com.teyuntong.outer.export.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.enterprise.service.ThirdEnterpriseRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "thirdEnterpriseRpcService",
        fallbackFactory = ThirdEnterpriseRemoteService.ThirdEnterpriseRemoteFallbackFactory.class)
public interface ThirdEnterpriseRemoteService extends ThirdEnterpriseRpcService {

    @Component
    class ThirdEnterpriseRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ThirdEnterpriseRemoteService> {
        protected ThirdEnterpriseRemoteFallbackFactory() {
            super(true, ThirdEnterpriseRemoteService.class);
        }
    }
}
