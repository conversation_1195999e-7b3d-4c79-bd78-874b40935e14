package com.teyuntong.outer.export.service.service.biz.gaode.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.outer.export.service.client.gaode.vo.GaoDeRegeoVo;
import com.teyuntong.outer.export.service.service.biz.gaode.contant.GaoDeMapConstant;
import com.teyuntong.outer.export.service.service.biz.gaode.service.GaoDeMapService;
import com.teyuntong.outer.export.service.service.common.property.GaodeLocationProperties;
import com.teyuntong.outer.export.service.service.common.utils.HttpClientUtil;
import com.teyuntong.outer.export.service.service.common.utils.NavUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.text.StrPool.COMMA;

/**
 * 高德货车导航
 * https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPj6gZYDSBN0kLYlWzN67Mw4
 *
 * <AUTHOR>
 * @since 2024/01/16 10:04
 */
@Slf4j
@Service("gaoDeMapService")
@EnableConfigurationProperties(GaodeLocationProperties.class)
public class GaoDeMapServiceImpl implements GaoDeMapService {

    @Autowired
    private GaodeLocationProperties gaodeLocationProperties;

    @Override
    public GaoDeRegeoVo regeoAddress(String longitude, String latitude) {
        Map<String, Object> map = new HashMap<>();
        map.put("key", GaoDeMapConstant.GAODE_KEY);
        map.put("location", String.join(COMMA, longitude, latitude));
        log.info("调用高德API开始,参数:{}", JSONUtil.toJsonStr(map));
        String resultStr = HttpUtil.post(GaoDeMapConstant.ADDRESS_RESOLUTION, map);
        log.info("调用高德API结束,返回值:{}", resultStr);
        if (StringUtils.isBlank(resultStr)) {
            return null;
        }
        GaoDeRegeoVo gaoDeRegeoVo = JSONUtil.toBean(resultStr, GaoDeRegeoVo.class);
        if (gaoDeRegeoVo.getStatus() != 1) {
            log.error("调用高德API失败......,location:{},message:{}", map.get("location"), gaoDeRegeoVo.getInfo());
            return null;
        }
        return gaoDeRegeoVo;
    }

    /**
     * 计算距离
     */
    @Override
    public DistanceRpcVO calculateDistance(DistanceRpcDTO dto) {
        dto.setOnlyDistance(true);
        String response = this.navigateTruck(dto);
        if (StringUtils.isNotBlank(response)) {
            JSONObject resultJson = JSONObject.parseObject(response);
            Integer distance = getDistance(resultJson);
            if (Objects.nonNull(distance)) {
                return DistanceRpcVO.builder().success(true).message("success").distance(distance).build();
            }
        }
        return DistanceRpcVO.builder().success(false).message("获取距离失败").build();
    }

    /**
     * 调用高德货车导航服务，返回导航数据
     */
    @Override
    public String navigateTruck(DistanceRpcDTO dto) {
        try {
            Map<String, String> paramMap = getParamMap(dto);

            URI uri = HttpClientUtil.createUri(gaodeLocationProperties.getUrl(), paramMap);
            log.info("调用高德货车导航请求参数，url:{}", uri.toURL());
            HttpGet httpGet = new HttpGet(uri);

            CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpGet);
            String json = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            log.info("调用高德货车导航结果:{}", json);
            return json;
        } catch (IOException e) {
            log.error("调用高德货车导航计算距离失败, 异常：", e);
            return "";
        }
    }

    private static Integer getDistance(JSONObject resultJson) {
        if (resultJson == null) {
            return null;
        }
        Integer status = resultJson.getInteger("errcode");
        if (!Objects.equals(status, 10000)) {
            return null;
        }
        JSONObject data = resultJson.getJSONObject("data");
        if (data == null) {
            return null;
        }
        JSONObject route = data.getJSONObject("route");
        if (route == null) {
            return null;
        }
        JSONArray paths = route.getJSONArray("paths");
        if (paths == null || paths.isEmpty()) {
            return null;
        }
        JSONObject path = paths.getJSONObject(0);
        if (path == null) {
            return null;
        }
        return path.getInteger("distance");
    }

    private Map<String, String> getParamMap(DistanceRpcDTO dto) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("key", gaodeLocationProperties.getKey());
        // “ 117.500244, 40.417801 ”，小数点后不得超过6位
        paramMap.put("origin", NavUtil.format(dto.getFromLongitude()) + "," + NavUtil.format(dto.getFromLatitude()));
        paramMap.put("destination", NavUtil.format(dto.getToLongitude()) + "," + NavUtil.format(dto.getToLatitude()));
        // 如果车重和载重为空，取默认车型
        if (dto.getWeight() == null || dto.getLoad() == null) {
            paramMap.put("size", "4");
            paramMap.put("height", "4");
            paramMap.put("width", "2.5");
            // paramMap.put("length", "12");
            // 注意：高德车辆总重是load，载重是weight；腾讯车辆总重是weight，载重是load
            paramMap.put("load", "20");
            paramMap.put("weight", "12");
            paramMap.put("axis", "3");
            paramMap.put("plate", "冀PN9651");
        } else {
            paramMap.put("size", NavUtil.getTruckSize(dto.getLength(), dto.getWeight()));
            paramMap.put("height", dto.getHeight() == null ? "" : dto.getHeight().toString());
            paramMap.put("width", dto.getWidth() == null ? "" : dto.getWidth().toString());
            // paramMap.put("length", dto.getLength() == null ? "" : dto.getLength().toString());
            // 注意：高德车辆总重是load，载重是weight；腾讯车辆总重是weight，载重是load
            // 高德load取值[0,100]吨，weight取值[0,100)，如果超重，load设为100，weight设为99
            paramMap.put("load", NavUtil.exceed100T(dto.getWeight(), "100"));
            paramMap.put("weight", NavUtil.exceed100T(dto.getLoad(), "99"));
            paramMap.put("axis", dto.getAxleCount() == null ? "" : dto.getAxleCount().toString());
            paramMap.put("plate", dto.getPlateNumber());
        }
        if (dto.isOnlyDistance()) { // 只返回距离
            paramMap.put("showpolyline", "0"); // 不返回路线数据
            paramMap.put("nosteps", "1"); // 不返回steps内容
        }
        return paramMap;
    }

}
