package com.teyuntong.outer.export.service.service.rpc.ocr;

import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.outer.export.service.client.ocr.service.VerifyRpcService;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportQCVerifyRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportVerifyRpcVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportQCVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupVerifyService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQCVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportVerifyResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开票RPC实现类
 *
 * <AUTHOR>
 * @since 2024-4-3 11:36:13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class VerifyRpcServiceImpl implements VerifyRpcService {

    private final GroupVerifyService groupVerifyService;

    @Override
    public RoadTransportVerifyRpcVO roadTransportVerify(String vehicleNo, String vehicleVIN) {
        RoadTransportVerifyResultVO result = groupVerifyService.roadTransportVerify(RoadTransportVerifyDTO.builder().vehicleNo(vehicleNo).vehicleVIN(vehicleVIN).build());
        return BeanUtil.copyProperties(result, RoadTransportVerifyRpcVO.class);
    }

    @Override
    public RoadTransportQCVerifyRpcVO roadTransportQCVerify(String idCard) {
        RoadTransportQCVerifyResultVO result = groupVerifyService.roadTransportQCVerify(RoadTransportQCVerifyDTO.builder().idCard(idCard).build());
        return BeanUtil.copyProperties(result, RoadTransportQCVerifyRpcVO.class);
    }
}
