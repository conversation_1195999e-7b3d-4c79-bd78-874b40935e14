package com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.PhoneNumberAttribution;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("common")
public interface PhoneNumberAttributionMapper extends BaseMapper<PhoneNumberAttribution> {

    /**
     * 忽略插入手机归属地信息，手机号唯一索引
     *
     * @param attribution PhoneNumberAttribution
     * @return 插入的条数
     */
    int addAttribution(PhoneNumberAttribution attribution);

    /**
     * 根据手机号查询归属地信息
     *
     * @param cellPhone
     * @return
     */
    PhoneNumberAttribution getAttribution(String cellPhone);


}