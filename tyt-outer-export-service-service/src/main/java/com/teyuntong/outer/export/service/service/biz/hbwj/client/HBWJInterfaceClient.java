package com.teyuntong.outer.export.service.service.biz.hbwj.client;

import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJDataResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.*;
import com.teyuntong.outer.export.service.service.common.utils.HttpClientUtil;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.POST;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/15 14:01
 */
public interface HBWJInterfaceClient {

    /**
     * 查询主体列表
     */
    @POST("api-server/openapi/principal/principalList")
    @Headers("Content-Type: application/x-www-form-urlencoded;charset=UTF-8")
    Call<HBWJDataResult<List<PrincipalInfoVO>>> principalList(@Body PrincipalListDO principalListDO);

    /**
     * 创建企业认证信息
     */
    @POST("api-server/openapi/company/create")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateCompanyResponse>> createCompany(@Body CreateCompanyRequest createCompanyRequest);

    /**
     * 查询企业信息
     */
    @POST("api-server/openapi/company/detail")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateDetailVO>> queryCompanyDetail(@Body CreateDetailDO createDetailDO);

    /**
     * 创建运单接口
     *
     * @param createWaybillThreeRequest
     * @return Call<HBWJDataResult < CreateWaybillThreeResponse>>
     * <AUTHOR>
     */
    @POST("api-server/openapi/waybill/addWaybillThree")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateWaybillThreeResponse>> addWaybillThree(@Body CreateWaybillThreeRequest createWaybillThreeRequest, @Header("user-code") String userCode);

    /**
     * 编辑运费
     *
     * @param amountUpdateRequest
     * @return Call<HBWJDataResult < amountUpdateRequest>>
     * <AUTHOR>
     */
    @POST("api-server/openapi/waybill/amountUpdate")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<AmountUpdateResponse>> amountUpdate(@RequestBody @Body AmountUpdateRequest amountUpdateRequest, @Header("user-code") String userCode);
    /**
     * 编辑运单
     *
     * @param editWaybillThreeRequest
     * @return Call<HBWJDataResult < EditWaybillThreeResponse>>
     * <AUTHOR>
     */
    @POST("api-server/openapi/waybill/setWaybillThree")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<EditWaybillThreeResponse>> editWaybillThree(@Body EditWaybillThreeRequest editWaybillThreeRequest, @Header("user-code") String userCode);

    /**
     * 运费申请
     *
     * @param applyFreightRequest
     * @return Call<HBWJDataResult < CreateWaybillThreeResponse>>
     * <AUTHOR>
     */
    @POST("api-server/openapi/payment/applyFreight")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<List<ApplyFreightResp>>> applyFreight(@Body ApplyFreightRequest applyFreightRequest, @Header("user-code") String code);


    /**
     * 创建司机
     * @param report
     * @return Call<HBWJDataResult<DriverDetailResponse>>
     */
    @POST("api-server/openapi/driver/v2/create")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<DriverDetailResponse>> createDriver(@Body HbwjDriverReport report,@Header("user-code") String code);

    @POST("api-server/openapi/waybill/setPickGoodsThree")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> pickGoods(@Body PickGoodsThreeRequest report,@Header("user-code") String code);


    @POST("api-server/openapi/v3/waybill/pickgoods")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResultV3<Object>> pickGoodsV3(@Body PickGoodsThreeV3Request report,@Header("user-code") String code);


    @POST("api-server/openapi/waybill/getWaybillThreeOffset")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<WaybillThreeOffsetResp>> getWaybillThreeOffset(@Body WaybillThreeOffsetRequest report,@Header("user-code") String code);


    @POST("api-server/openapi/waybill/setArriveThree")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> arriveGoods(@Body ArriveGoodsThreeRequest report,@Header("user-code") String code);


    @POST("api-server/openapi/waybill/cancelWaybill")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> cancelWaybill(@Body CancelOrderThreeRequest report,@Header("user-code") String code);


    @Headers("Content-type: multipart/form-data;charset=UTF-8")
    @POST("api-server/openapi//callback/file/upload")
    Call<HBWJDataResult<FileUpLoadResp>> fileUpload(@Body MultipartFile file);

    @POST("api-server/openapi/waybill/uploadWeightReceipts")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<String>> uploadWeightReceipts(@Body UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, @Header("user-code") String userCode);

    @POST("api-server/openapi/fund/getVerificationCode")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<String>> getFundVerificationCode(@Body FundVerificationCodeReqDto fundVerificationCodeReqDto,@Header("user-code") String userCode);

    /**
     * 新增合同
     */
    @POST("api-server/openapi/contract/create")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateContractResponse>> createContract(@Body CreateContractRequest request,@Header("user-code") String userCode);

    /**
     * 新增项目
     */
    @POST("api-server/openapi/project/addProjectThree")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateProjectResponse>> createProject(@Body CreateProjectRequest request, @Header("user-code") String userCode);

    /**
     * 查询钱包
     */
    @POST("api-server/openapi/fund/selectCustomerRechargeWalletList")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<QueryWalletResponse>> selectCustomerRechargeWalletList(@Body QueryWalletRequest request, @Header("user-code") String userCode);

    /**
     * 设置支付密码
     */
    @POST("api-server/openapi/fund/setPayPwd")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> setPayPwd(@Body SavePayPwdRequest request, @Header("user-code") String userCode);

    /**
     * 修改支付密码
     */
    @POST("api-server/openapi/fund/changePayPwd")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> changePayPwd(@Body ChangePayPwdRequest request, @Header("user-code") String userCode);

    /**
     * 发送更换手机验证码
     */
    @POST("mobile/account/changePhoneCaptchaSend")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> changePhoneCaptchaSendForAccount(@Body ChangePhoneCaptchaSendRequest request, @Header("user-code") String userCode);

    /**
     * 修改手机号
     */
    @POST("mobile/account/changePhone")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> changePhone(@Body ChangePhoneRequest request, @Header("user-code") String userCode);

    /**
     * 检查更换手机号验证码
     */
    @POST("mobile/account/changePhoneCaptchaCheck")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<String>> changePhoneCaptchaCheck(@Body ChangePhoneCaptchaCheckRequest request, @Header("user-code") String userCode);

    /**
     * 创建车辆
     * @param report
     * @return Call<HBWJDataResult<DriverDetailResponse>>
     */
    @POST("api-server/openapi/car/v2/create")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<DriverDetailResponse>> createCar(@Body HbwjCarReport report,@Header("user-code") String userCode);

    /**
     * 获得高德地区列表
     */
    @POST("api-server/openapi/application/getGaoDeDistrictVoList")
    @Headers("Content-Type: application/x-www-form-urlencoded;charset=UTF-8")
    Call<HBWJDataResult<Object>> getGaoDeDistrictVoList();

    /**
     * 生成合同
     */
    @POST("api-server/openapi/contract/createSignContract")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<CreateProtocolResponse>> createProtocol(@Body CreateProtocolRequest request,@Header("user-code") String userCode);

    /**
     * 批量支付
     * @param batchDoPayRequest
     * @return HttpProtocolHandler
     */
    @POST("api-server/openapi/payment/batchDoPay")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<List<BatchDoPayResponse>>> batchDoPay(@Body BatchDoPayRequest batchDoPayRequest,@Header("user-code") String userCode);

    /**
     * 新增开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return
     */
    @POST("api-server/openapi/invoice/create")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Long>> invoiceCreate(@Body InvoiceRequest invoiceRequest, @Header("user-code") String userCode);

    /**
     * 修改开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return
     */
    @POST("api-server/openapi/invoice/update")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Long>> invoiceUpdate(@Body InvoiceRequest invoiceRequest, @Header("user-code") String userCode);

    /**
     * 发票创建
     *
     * @param invoiceCreateRequest
     * @param userCode
     * @return
     */
    @POST("api-server/openapi/invoice/invoiceCreate")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> doInvoiceCreate(@Body InvoiceCreateRequest invoiceCreateRequest, @Header("user-code") String userCode);

    /**
     * 查询发票状态
     * @param invoiceInfoRequest
     * @param userCode
     * @return Call<HBWJDataResult<InvoiceResponse>>
     */
    @POST("api-server/openapi/invoice/queryInvoiceCallBackVo")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<InvoiceResponse>> queryInvoiceCallBackVo(@Body InvoiceInfoRequest invoiceInfoRequest, @Header("user-code") String userCode);

    @POST("api-server/openapi/payment/getPaymentDetailByNo")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<PaymentOrderResp>> getPaymentDetailByNo(@Body PaymentInfoRequest paymentInfoRequest,@Header("user-code") String userCode);


    /**
     * 撤销运单的支付单 - 只能撤销支付单审核还未通过的，已通过的财务进行作废
     * @param cancelFreightRequest
     * @param code
     * @return
     */
    @POST("api-server/openapi/waybill/cancelFreight")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> cancelFreight(@Body CancelFreightRequest cancelFreightRequest, @Header("user-code") String code);

    /**
     * 支付单作废
     * @param paymentCancelRequest
     * @param userCode
     * @return
     */
    @POST("api-server/openapi/payment/cancel")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResult<Object>> paymentCancel(@Body PaymentCancelRequest paymentCancelRequest,@Header("user-code") String userCode);

    /**
     * 运单凭证上传(V3)
     */
    @POST("api-server/openapi/v3/waybill/receipts/upload")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResultV3<Object>> receiptsUpload(@Body ReceiptsUploadRequest receiptsUploadRequest, @Header("user-code") String userCode);

    /**
     * 运单运抵(V3)
     */
    @POST("api-server/openapi/v3/waybill/arrive")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResultV3<Object>> arriveWaybill(@Body ArriveWaybillRequest arriveWaybillRequest, @Header("user-code") String userCode);


    /**
     * 重置支付密码 - 发送短信验证码(V3)
     * 短信验证码 默认发送到资金安全手机号（企业管理员手机号）中。
     */
    @POST("api-server/openapi/v3/fund/sendmsg")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResultV3<Object>> fundSendMsg(@Header("user-code") String userCode);

    /**
     * 重置支付密码
     */
    @POST("api-server/openapi/v3/fund/forgetpaypwd")
    @Headers("Content-Type: application/json;charset=UTF-8")
    Call<HBWJDataResultV3<Object>> forgetpaypwd(@Body ForgetPayPwdRequest forgetPayPwdRequest, @Header("user-code") String userCode);
}
