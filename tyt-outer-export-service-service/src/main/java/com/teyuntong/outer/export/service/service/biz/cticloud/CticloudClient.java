package com.teyuntong.outer.export.service.service.biz.cticloud;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.MD5;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.CticloudAxbApi;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.CticloudConfig;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.CticloudConfigTwo;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.client.CticloudCallConfig;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.client.TaskApi;
import lombok.SneakyThrows;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/6/9 下午3:53
 */
@Component
@EnableConfigurationProperties({CticloudConfig.class, CticloudCallConfig.class, CticloudConfigTwo.class})
public class CticloudClient {

    private final Retrofit retrofit;
    private final Retrofit retrofit2;
    private final Retrofit cticloudCallRetrofit;
    private final Cache<Class<?>, Object> cache = CacheBuilder.newBuilder().build();

    public CticloudClient(CticloudConfig cticloudConfig, CticloudCallConfig cticloudCallConfig, CticloudConfigTwo cticloudConfigTwo, ObjectMapper mapper) {
        retrofit = initRetrofit(cticloudConfig, mapper);
        retrofit2 = initRetrofit(cticloudConfigTwo, mapper);
        cticloudCallRetrofit = initRetrofit(cticloudCallConfig, mapper);
    }

    private Retrofit initRetrofit(CticloudBaseConfig cticloudConfig, ObjectMapper mapper) {
        // cticloud 鉴权拦截器
        Interceptor interceptor = chain -> {
            Request oldRequest = chain.request();

            Integer validateType = cticloudConfig.getValidateType();
            String departmentId = cticloudConfig.getDepartmentId();
            String enterpriseId = cticloudConfig.getEnterpriseId();
            long timestamp = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());

            String md5Key;

            if (Objects.equals(1, validateType)) {
                // validateType=1时，sign=MD5({departmentId}+{timestamp}+{部门token值})；
                md5Key = departmentId + timestamp + cticloudConfig.getToken();
            } else if (Objects.equals(2, validateType)) {
                // validateType=2时，sign=MD5({enterpriseId}+{timestamp}+{部门token值})；
                md5Key = enterpriseId + timestamp + cticloudConfig.getToken();
            } else {
                throw new IllegalArgumentException(String.format("不支持的 validateType 类型:%d", validateType));
            }
            String sign = HexUtil.encodeHexStr(MD5.create().digest(md5Key));

            HttpUrl httpUrl = oldRequest.url()
                    .newBuilder()
                    .addQueryParameter("timestamp", String.valueOf(timestamp))
                    .addQueryParameter("departmentId", departmentId)
                    .addQueryParameter("validateType", String.valueOf(validateType))
                    .addQueryParameter("sign", sign)
                    .addQueryParameter("enterpriseId", enterpriseId)
                    .build();
            // 构建新请求
            Request request = oldRequest.newBuilder().url(httpUrl).build();
            return chain.proceed(request);
        };

        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(5))
                .readTimeout(Duration.ofSeconds(5))
                .addInterceptor(interceptor)
                .build();

        return new Retrofit.Builder()
                .baseUrl(String.format(cticloudConfig.getBaseUrlFormat(),
                        cticloudConfig.getRegion(),
                        cticloudConfig.getVersion()))
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(httpClient)
                .build();
    }

    @SneakyThrows
    public CticloudAxbApi getCticloudAxBApi() {
        return (CticloudAxbApi) cache.get(CticloudAxbApi.class, () -> retrofit.create(CticloudAxbApi.class));
    }

    @SneakyThrows
    public CticloudAxbApi getCticloudAxBApi2() {
        return retrofit2.create(CticloudAxbApi.class);
    }

    @SneakyThrows
    public TaskApi getTaskApi() {
        return (TaskApi) cache.get(TaskApi.class, () -> cticloudCallRetrofit.create(TaskApi.class));
    }
}