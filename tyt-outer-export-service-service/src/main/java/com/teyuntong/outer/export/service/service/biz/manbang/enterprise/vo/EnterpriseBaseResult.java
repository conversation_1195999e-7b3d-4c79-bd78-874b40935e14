package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:39
 */
@NoArgsConstructor
@Data
public class EnterpriseBaseResult<T> {

    private Integer code;
    private String message;
    private T data;

    public static boolean checkSuccess(EnterpriseBaseResult<?> authBaseResult){
        return (authBaseResult != null && authBaseResult.getCode() == 0);
    }

}
