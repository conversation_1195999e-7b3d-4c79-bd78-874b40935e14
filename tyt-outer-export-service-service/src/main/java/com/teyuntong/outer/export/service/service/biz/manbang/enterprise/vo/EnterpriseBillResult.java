package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.vo;

import com.wlqq.wallet.gateway.client.response.BaseResponse;

import java.util.List;
import java.util.Map;

public class EnterpriseBillResult extends BaseResponse {
    private String currentPage;
    private String totalSize;
    private List<Map<String, Object>> billList;

    public EnterpriseBillResult() {
    }

    public String getTotalSize() {
        return this.totalSize;
    }

    public void setTotalSize(String totalSize) {
        this.totalSize = totalSize;
    }

    public String getCurrentPage() {
        return this.currentPage;
    }

    public void setCurrentPage(String currentPage) {
        this.currentPage = currentPage;
    }

    public List<Map<String, Object>> getBillList() {
        return this.billList;
    }

    public void setBillList(List<Map<String, Object>> billList) {
        this.billList = billList;
    }
}
