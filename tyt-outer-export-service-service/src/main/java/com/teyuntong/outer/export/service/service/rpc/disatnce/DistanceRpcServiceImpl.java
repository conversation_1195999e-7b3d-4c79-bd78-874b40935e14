package com.teyuntong.outer.export.service.service.rpc.disatnce;

import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.service.DistanceRpcService;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.outer.export.service.service.biz.gaode.service.GaoDeMapService;
import com.teyuntong.outer.export.service.service.biz.tencent.service.TencentMapService;
import com.teyuntong.outer.export.service.service.remote.common.TytConfigRemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Objects;

/**
 * 两点距离计算
 *
 * <AUTHOR>
 * @since 2025-02-06 15:01
 */
@RestController
@RequiredArgsConstructor
public class DistanceRpcServiceImpl implements DistanceRpcService {

    private final TencentMapService tencentMapService;
    private final GaoDeMapService gaoDeMapService;
    private final TytConfigRemoteService configRemoteService;
    private final RedisUtil redisUtil;

    @Override
    public DistanceRpcVO calculateDistance(DistanceRpcDTO dto) {
        setNavTypeIfNull(dto); // 设置导航类型
        // 先从缓存中获取距离
        String distanceCacheKey = getDistanceCacheKey(dto);
        Integer distance = redisUtil.getInt(distanceCacheKey);
        if (distance != null) {
            return DistanceRpcVO.builder().success(true).message("success").distance(distance).build();
        }
        // 调用三方接口
        DistanceRpcVO distanceRpcVO = switch (dto.getTruckNavigationType()) {
            case 1 -> gaoDeMapService.calculateDistance(dto);
            case 2 -> tencentMapService.calculateDistance(dto);
            default -> DistanceRpcVO.builder().success(false).message("导航类型错误").build();
        };
        // 如果成功，记录到缓存
        if (distanceRpcVO.isSuccess()) {
            redisUtil.set(distanceCacheKey, distanceRpcVO.getDistance(), Duration.ofMinutes(5));
        }
        return distanceRpcVO;
    }

    /**
     * 货车导航，返回三方导航JSON格式数据
     */
    @Override
    public String navigationTruck(DistanceRpcDTO dto) {
        setNavTypeIfNull(dto); // 设置导航类型
        if (Objects.equals(dto.getTruckNavigationType(), 1)) {
            return gaoDeMapService.navigateTruck(dto);
        }
        return tencentMapService.navigateTruck(dto);
    }

    /**
     * 设置导航类型
     */
    private void setNavTypeIfNull(DistanceRpcDTO dto) {
        // 如果不传货导航类型，取开关配置
        if (dto.getTruckNavigationType() == null) {
            // 货车导航选用类型 1高德；2腾讯
            Integer navType = configRemoteService.getIntValue("truck_navigation_type", 1);
            dto.setTruckNavigationType(navType);
        }
    }

    /**
     * 缓存导航距离的 key
     */
    private String getDistanceCacheKey(DistanceRpcDTO dto) {
        return "outer:nav:dist:" + String.join("_",
                String.valueOf(dto.getTruckNavigationType()),
                dto.getFromLongitude(),
                dto.getFromLatitude(),
                dto.getToLongitude(),
                dto.getToLatitude(),
                String.valueOf(dto.getLength()),
                String.valueOf(dto.getWidth()),
                String.valueOf(dto.getHeight()),
                String.valueOf(dto.getWeight()),
                String.valueOf(dto.getLoad()),
                String.valueOf(dto.getAxleCount()),
                dto.getPlateNumber());
    }
}
