package com.teyuntong.outer.export.service.service.biz.transport.service.impl;

import com.teyuntong.outer.export.service.service.biz.transport.pojo.GoodsUnique;
import com.teyuntong.outer.export.service.service.biz.transport.service.WordSplitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 分词
 */
@Slf4j
@Service
public class WordSplitServiceImpl implements WordSplitService {

    @Override
    public List<String> splitContent(String content) {

        if(StringUtils.isBlank(content)){
            return new ArrayList<>();
        }
        GoodsUnique goodsUnique = GoodsUnique.getInstance();
        List<String> nameList = goodsUnique.getListkeys(content);
        return nameList;
    }
}
