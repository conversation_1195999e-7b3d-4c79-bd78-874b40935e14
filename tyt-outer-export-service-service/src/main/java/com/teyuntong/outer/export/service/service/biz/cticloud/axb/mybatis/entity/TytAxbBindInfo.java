package com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tyt_axb_bind_info")
public class TytAxbBindInfo implements Serializable {
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 调用第三方接口返回的唯一标识
     */
    @TableField("third_party_id")
    private String thirdPartyId;

    /**
     * 第三方类型  1 cticloud
     */
    @TableField("third_party_type")
    private Integer thirdPartyType;

    /**
     * 电话A
     */
    @TableField("tel_a")
    private String telA;

    /**
     * 电话B
     */
    @TableField("tel_b")
    private String telB;

    /**
     * 电话X
     */
    @TableField("tel_x")
    private String telX;

    /**
     * 是否永久有效 0 否 1是
     */
    @TableField("is_permanent")
    private Boolean isPermanent;

    /**
     * 失效日期
     */
    @TableField("expiration_date")
    private Date expirationDate;

    /**
     * 是否删除
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 此条数据对应的业务id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 词条数据对应的业务类型
     */
    @TableField("biz_type")
    private Integer bizType;

    /**
     * 自定义字段
     */
    @TableField("extra_field")
    private String extraField;

    private static final long serialVersionUID = 1L;
}