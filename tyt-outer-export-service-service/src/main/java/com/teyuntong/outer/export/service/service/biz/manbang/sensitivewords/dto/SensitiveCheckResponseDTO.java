package com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.dto;

import lombok.Data;

import java.util.List;

/**
 * 敏感词检测接口响应DTO
 */
@Data
public class SensitiveCheckResponseDTO {
    
    /**
     * 决策结果，可能是字符串"accept"或者是一个对象
     */
    private Object decision;
    
    /**
     * 规则列表
     */
    private List<String> rules;
    
    /**
     * 扩展信息
     */
    private String extend;
    
    /**
     * 决策详情对象（当decision不是"accept"时使用）
     */
    @Data
    public static class DecisionDetail {
        
        /**
         * 规则代码
         */
        private String ruleCode;
        
        /**
         * 规则描述
         */
        private RuleDescription ruleDesc;
    }
    
    /**
     * 规则描述对象
     */
    @Data
    public static class RuleDescription {
        
        /**
         * 货物名称相关的敏感词信息
         */
        private FieldSensitiveInfo cargoName;
        
        /**
         * 描述相关的敏感词信息
         */
        private FieldSensitiveInfo description;
    }
    
    /**
     * 字段敏感词信息
     */
    @Data
    public static class FieldSensitiveInfo {
        
        /**
         * 规则列表
         */
        private List<String> ruleList;
        
        /**
         * 命中详情列表
         */
        private List<HitDetail> hitDetails;
    }
    
    /**
     * 命中详情
     */
    @Data
    public static class HitDetail {
        
        /**
         * 开始索引
         */
        private Integer beginIndex;
        
        /**
         * 结束索引
         */
        private Integer endIndex;
        
        /**
         * 命中的敏感词文本
         */
        private String hitText;
        
        /**
         * 索引
         */
        private Integer index;
    }
}
