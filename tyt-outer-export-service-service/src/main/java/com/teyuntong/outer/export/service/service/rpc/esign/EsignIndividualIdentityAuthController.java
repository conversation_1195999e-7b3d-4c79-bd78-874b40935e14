package com.teyuntong.outer.export.service.service.rpc.esign;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.*;
import com.teyuntong.outer.export.service.client.esign.vo.identity.Telecom3FactorsVO;
import com.teyuntong.outer.export.service.client.esign.vo.identity.Telecom3FactorsVerifyReq;
import com.teyuntong.outer.export.service.service.biz.esign.identity.client.EsignIndividualIdentityAuthClient;
import com.teyuntong.outer.export.service.service.biz.esign.identity.client.IdentityAuthBaseResult;
import com.teyuntong.outer.export.service.service.common.utils.RetrofitUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Response;

import java.io.IOException;

/**
 * 易签宝-个人身份验证
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 *
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/esignIdentityAuthIndividual")
public class EsignIndividualIdentityAuthController   {

    private final EsignIndividualIdentityAuthClient esignIndividualIdentityAuthClient;

    /**
     * 【手机号认证】运营商3要素核身
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/wry7rc">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors")
    public WebResult<Telecom3FactorsVO> telecom3Factors(@RequestBody @Validated Telecom3FactorsApiReq req) throws IOException {
        Telecom3FactorsApiReq identityAuthReq = new Telecom3FactorsApiReq();
        BeanUtils.copyProperties(req, identityAuthReq);
        Response<IdentityAuthBaseResult<Telecom3FactorsApiResp>> response =
                esignIndividualIdentityAuthClient.telecom3Factors(identityAuthReq).execute();
        Telecom3FactorsApiResp body = RetrofitUtils.getDataDetail(response);
        return WebResult.success(Telecom3FactorsVO.builder().flowId(body.getFlowId()).build());
    }

    /**
     * 【手机号认证】短信验证码校验
     * <p>
     * code = 200 就算成功
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ddklrl">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors/verify")
    public WebResult<?> telecom3FactorsVerify(@RequestBody @Validated Telecom3FactorsVerifyReq req) throws IOException {
        Telecom3FactorsVerifyApiReq factorsVerifyReq = new Telecom3FactorsVerifyApiReq();
        factorsVerifyReq.setAuthcode(req.getAuthcode());
        Response<IdentityAuthBaseResult<Object>> response =
                esignIndividualIdentityAuthClient.telecom3FactorsVerify(req.getFlowId(), factorsVerifyReq).execute();

        RetrofitUtils.getDataDetail(response);

        return WebResult.success();
    }

    /**
     * 【手机号认证】重新发送验证码
     * <p>
     * code = 200 就算成功
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/fxr4yh">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors/authCode")
    public WebResult<?> telecom3FactorsAuthCode(@RequestBody @Validated Telecom3FactorsVerifyReq req) throws IOException {
        Response<IdentityAuthBaseResult<Object>> response =
                esignIndividualIdentityAuthClient.telecom3FactorsAuthCode(req.getFlowId()).execute();

        RetrofitUtils.getDataDetail(response);

        return WebResult.success();
    }

    /**
     * 发起个人刷脸实名认证
     * <p>
     * <a href="https://qianxiaoxia.yuque.com/opendoc/fei7gb/io1yrd">官方文档链接</a>
     */
    @PostMapping("/faceVerifyUrl")
    public WebResult<EsignFaceVerifyResp> faceVerifyUrl(@RequestBody @Validated EsignFaceVerifyReq req) throws IOException {

        Response<IdentityAuthBaseResult<EsignFaceVerifyResp>> response =
                esignIndividualIdentityAuthClient.faceVerifyUrl(req).execute();
        EsignFaceVerifyResp body = RetrofitUtils.getDataDetail(response);
        return WebResult.success(body);
    }

    /**
     * 查询个人刷脸状态
     * <p>
     * <a href="https://qianxiaoxia.yuque.com/opendoc/fei7gb/qeig5n">官方文档链接</a>
     */
    @PostMapping("/getFaceResult")
    public WebResult<EsignFaceResultResp> getFaceResult(String flowId) throws IOException {

        Response<IdentityAuthBaseResult<EsignFaceResultResp>> response =
                esignIndividualIdentityAuthClient.getFaceResult(flowId).execute();
        EsignFaceResultResp body = RetrofitUtils.getDataDetail(response);
        return WebResult.success(body);
    }

}
