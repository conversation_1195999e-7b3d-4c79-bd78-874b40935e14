package com.teyuntong.outer.export.service.service.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class HttpClientUtil {

    /**
     * userAgent
     */
    public static final String MOCK_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36";

    /** 同域名同时请求线程数 **/
    private static final Integer MAX_CONN_PER_ROUTE = 10;

    /** httpClient **/
    private static final CloseableHttpClient instance = getHttpClientWithRetry();

    public static CloseableHttpClient getInstance(){
        return instance;
    }

    /**
     * 默认配置
     * @return
     */
    private static RequestConfig getHttpClientConfig(){
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(2000)
                .setSocketTimeout(15000)
                .build();
        return requestConfig;
    }

    /**
     * 生成httpclient
     * @return
     */
    private static CloseableHttpClient getHttpClientWithRetry() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);//例如默认每路由最高50并发，具体依据业务来定

        RequestConfig httpClientConfig = getHttpClientConfig();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(httpClientConfig)
                .setMaxConnPerRoute(MAX_CONN_PER_ROUTE)
                .build();

        return httpClient;
    }

    /**
     * 创建uri 并拼接参数
     * @param apiUrl
     * @param paramMap
     * @return
     */
    public static URI createUri(String apiUrl, Map<String, String> paramMap){
        URI uri = null;
        try {
            URIBuilder builder = new URIBuilder(apiUrl);

            if(MapUtils.isNotEmpty(paramMap)){
                paramMap.forEach(builder::addParameter);
            }
            uri = builder.build();
        } catch (URISyntaxException e) {
            log.error("", e);
        }

        return uri;
    }

    /**
     * 解析json
     * @param httpResponse
     * @return
     */
    public static JSONObject toJsonObject(HttpResponse httpResponse) {

        JSONObject jsonObject = null;

        try {
            HttpEntity entity = httpResponse.getEntity();
            String jsonStr = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if(statusCode != 200) {
                log.warn("toJsonObject_error : httpStatus : [{}], respBody : {}", statusCode, jsonStr);
                throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY);
            }

            jsonObject = JSON.parseObject(jsonStr);
        } catch (IOException e) {
            log.error("", e);
            throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY);
        }

        return jsonObject;
    }

    /**
     * http client 请求
     * @param httpRequest
     * @return
     */
    public static CloseableHttpResponse execute(HttpUriRequest httpRequest) throws IOException{
        CloseableHttpResponse httpResponse = null;

        try {
            httpResponse = instance.execute(httpRequest);
        } catch (Exception e) {
            log.error("http_client_execute_error : ", e);
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }

        return httpResponse;
    }

    /**
     * 下载文件.
     * @param fileUrl fileUrl
     * @param output output
     */
    public static void downloadFile(String fileUrl, OutputStream output){

        HttpGet httpGet = new HttpGet(fileUrl);
        httpGet.addHeader("User-Agent", MOCK_USER_AGENT);

        try(CloseableHttpResponse response = instance.execute(httpGet)) {
            try (InputStream inputStream = response.getEntity().getContent()) {

                IOUtils.copy(inputStream, output);

            } catch (IOException e) {
                log.error("", e);
                throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
            }

        } catch (Exception e) {
            log.error("", e);
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
    }

    /**
     * 下载文件，大文件不允许使用.
     * @param fileUrl fileUrl
     */
    public static FileInfo downloadFile(String fileUrl){

        try(ByteArrayOutputStream output = new ByteArrayOutputStream()) {

            downloadFile(fileUrl, output);

            byte[] dataBytes = output.toByteArray();

            FileInfo fileInfo = TytFileUtil.getFileInfo(fileUrl);

            fileInfo.setDataBytes(dataBytes);
            return fileInfo;

        } catch (Exception e) {
            log.error("", e);
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
    }

    /**
     * 下载文件base64，大文件不允许使用.
     * @param fileUrl fileUrl
     */
    public static String downloadBase64(String fileUrl){
        if(StringUtils.isBlank(fileUrl)){
            return null;
        }

        FileInfo fileInfo = HttpClientUtil.downloadFile(fileUrl);

        byte[] dataBytes = fileInfo.getDataBytes();

        String fileBase64 = PlatCommonUtil.encodeBase64(dataBytes);
        return fileBase64;
    }
}
