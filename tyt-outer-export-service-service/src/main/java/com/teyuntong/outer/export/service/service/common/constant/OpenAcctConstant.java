package com.teyuntong.outer.export.service.service.common.constant;

/**
 * 开户相关常量
 *
 * <AUTHOR>
 */
public class OpenAcctConstant {

    public static final String SUCCESS = "200";

    /**
     * 二次验证返回码
     */
    public static final String DOUBLE_VERIFY = "201";

    /**
     * 商户请求参数校验错误（绑定银行帐号）
     */
    public static final String ERROR_CODE_1005 = "1005_lianlian";
    /**
     * 配合 1005_lianlian 代表身份证有效期错误
     */
    public static final String ERROR_ID_CORD = "证件有效期";

    /**
     * 配合 1005_lianlian 证件号有误
     */
    public static final String ERROR_ID_CORD_01 = "证件号有误";

    /**
     *卡bin 银行卡号不正确
     */
    public static final String ERROR_CODE_4001001 = "4001001_lianlian";


    /**
     * 支付密码错误次数超限，请稍后重试
     */
    public static final String ERROR_CODE_1014 = "1014_lianlian";


    /**
     * 支付密码错误
     */
    public static final String ERROR_CODE_1015 = "1015_lianlian";

    /**
     * 商户请求参数校验错误（绑定银行帐号）
     */
    public static final String ERROR_CODE_3023 = "3023_lianlian";



    public static final String REQ_BIZ_FLOW_ID = "businessFlowId";
    public static final String REQ_BIZ_TYPE = "businessType";
    public static final String REQ_CHANNEL_CODE = "channelCode";
    public static final String REQ_REQUEST_TIME = "requestTime";

    public static final String RET_CODE = "code";
    public static final String RET_MSG = "message";
    public static final String RET_BIZ_FLOW_ID = "businessFlowId";
    public static final String RET_BODY = "body";

    public static final String SIGN_KEY = "pay~tjdcx~b9-5@r";
    public static final String SIGN_TYPE = "MD5";

    /**
     * 业务类型
     */
    public static final String BIZ_TYPE = "开户";
    public static final String BIZ_TYPE_OPEN_ACCT = "开户";

    /**
     * 防止绑卡提交并发 Lock key
     */
    public static final String BIND_CARD_LOCK_KEY = "openacct:bind_card:user_id:";

    /**
     * 新增绑卡缓存token
     */
    public static final String BIND_CARD_TOKEN = "openacct:bind_card:token:";

    /**
     * 解绑银行卡缓存交易流水号
     */
    public static final String UNBIND_CARD_TXNSEQNO = "openacct:unbind:txnSeqno:";

    /**
     * 企业更换绑定账号缓存交易流水号
     */
    public static final String CHANGE_ACC_TXNSEQNO = "openacct:change:txnSeqno:";

    /**
     * 授权令牌过期时间 30分钟
     */
    public static final int ACCT_TOKEN_EXPIRE = 30 * 60;

    /**
     * 卡bin查询api
     */
    public static final String API_QUERY_CARD_BIN = "/v1/open/acc/cardBin";

    /**
     * 发送验证码
     */
    public static final String API_VERIFY_CODE_SEND = "/v1/open/acc/regphone";

    /**
     * 绑定手机号验证码提交验证
     */
    public static final String API_VERIFY_CODE_COMMIT = "/v1/open/acc/verifycode/regphone";

    /**
     * 个人开户
     */
    public static final String API_APPLY_PERSONAL_ACCT = "/v1/open/acc/person/open";

    /**
     * 待激活开户
     */
    public static final String API_APPLY_INACTIVE_ACCT = "/v1/open/acc/unactivated";

    /**
     * 申请企业开户
     */
    public static final String API_APPLY_ENTERPRISE_ACCT = "/v1/open/acc/enterprise/open";


    /**
     * 申请企业开户
     */
    public static final String API_APPLY_ENTERPRISE_ACCT_V2 = "/v1/open/acc/enterprise/apply";

    /**
     * 开户回调api
     */
    public static final String CALLBACK_PERSONAL_ACCT = "/callback/personal";
    public static final String CALLBACK_ENTERPRISE_ACCT = "/callback/enterprise";
    public static final String CALLBACK_INACTIVE_ACCT = "/callback/inactive";

    /**
     * 新增绑卡回调地址
     */
    public static final String CALLBACK_BIND_NEW_CARD = "/callback/card/bind";

    /**
     * 解绑银行卡回调
     */
    public static final String CALLBACK_UNBIND_NEW_CARD = "/callback/card/unbind";

    /**
     * 企业更换绑定账号回调
     */
    public static final String CALLBACK_CHANGE_ENTERPRISE_ACC = "/callback/enterprise/acc/change";

    /**
     * 获取随机因子
     */
    public static final String API_GET_RANDOM = "/v1/open/acc/random/code";

    /**
     * 密码设置
     */
    public static final String API_SET_PASSWORD = "/v1/open/acc/paypassword";

    /**
     * 修改密码
     */
    public static final String API_MODIFY_PASSWORD = "/v1/open/acc/change/paypassword";

    /**
     * 申请重置密码
     */
    public static final String API_APPLY_RESET_PASSWORD = "/v1/open/acc/find/paypassword";

    /**
     * 确认重置密码
     */
    public static final String API_CONFIRM_RESET_PASSWORD = "/v1/open/acc/reset/paypassword";

    /**
     * 密码校验
     */
    public static final String API_VALIDATE_PASSWORD = "/v1/open/acc/check/paypassword";

    /**
     * 上传照片
     */
    public static final String API_UPLOAD_PHOTOS = "/v1/open/acc/upload/photos";

    /**
     * 用户信息查询
     */
    public static final String API_ACCT_INFO = "/v1/open/acc/info";

    /**
     * 新增绑卡申请
     */
    public static final String API_BIND_NEW_CARD_APPLY = "/v1/open/acc/individualBindCard";

    /**
     * 新增绑卡验证
     */
    public static final String API_BIND_NEW_CARD_VERIFY = "/v1/open/acc/individualBindCardVerify";

    /**
     * 解绑银行卡
     */
    public static final String API_UNBIND_BANK_CARD = "/v1/open/acc/individualUnlinkedCard";

    /**
     * 企业用户更换绑定账号
     */
    public static final String API_CHANGE_ENTERPRISE_CARD = "/v1/open/acc/enterpriseChangeCard";
}
