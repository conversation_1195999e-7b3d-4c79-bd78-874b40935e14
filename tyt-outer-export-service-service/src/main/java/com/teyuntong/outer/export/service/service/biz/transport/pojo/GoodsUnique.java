package com.teyuntong.outer.export.service.service.biz.transport.pojo;

import com.huaban.analysis.jieba.JiebaSegmenter;

import java.io.IOException;
import java.util.List;

public class GoodsUnique {

    private GoodsUnique() {}

    private static GoodsUnique singleton;

    public static GoodsUnique getInstance() {
        if (singleton == null) {
            Class var0 = GoodsUnique.class;
            synchronized (GoodsUnique.class) {
                if (singleton == null) {
                    singleton = new GoodsUnique();
                    return singleton;
                }
            }
        }
        return singleton;
    }

    /**
     * 本地缓存数据库内容
     */
    //public static Cache<String,Object> dictCache = CacheBuilder.newBuilder().expireAfterWrite(86400, TimeUnit.SECONDS).build();

    //分词器，源码内加载了WordDictionary.getInstance();词典
    private static JiebaSegmenter segmenter = new JiebaSegmenter();

    /**
     * 把line拆成词组返回
     *
     * @param line
     * @return
     */
    public static List<String> getListkeys(String line) {
        if (null == line || line.isEmpty()) {
            return null;
        }
        // 替换空格，标点符号
        line = line.replaceAll("[ +|\\pP\\p{Punct}]","");
        List<String> segList = segmenter.sentenceProcess(line);
        return segList;

    }


    public static void main(String[] args) throws IOException, InterruptedException {
        GoodsUnique goodsUnique = GoodsUnique.getInstance();
        //开始提取
        String[] sentences = new String[]{
                "一台江淮40叉架急急",
//                "明早触发,50 挖+6 0 铲,急，急",
        };
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            for (String sentence : sentences) {
                //   String info = GoodsUnique.getKeyInfoWithTSLine("333","666", sentence);
                List<String> res = goodsUnique.getListkeys(sentence);
//                for (String key : res) {
//                    System.out.println("源货源内容： " + sentence + " === 提取关键词： " + key);
//                }
//                System.out.println("源货源内容： " + sentence + " === 提取关键词： " + res.toString());
            }
        }

        long endTime = System.currentTimeMillis();

        System.out.println(endTime - startTime);
//        System.in.read();

    }
}


