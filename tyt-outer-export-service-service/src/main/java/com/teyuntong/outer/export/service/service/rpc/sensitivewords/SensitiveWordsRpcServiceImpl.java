package com.teyuntong.outer.export.service.service.rpc.sensitivewords;

import com.teyuntong.outer.export.service.client.sensitivewords.SensitiveWordsRpcService;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.SensitiveWordsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class SensitiveWordsRpcServiceImpl implements SensitiveWordsRpcService {

    private final SensitiveWordsService sensitiveWordsService;

    @Override
    public SensitiveWordsVO newCargoSensitiveCheck(Long userId, String taskContent, String machineRemark) {
        return sensitiveWordsService.newCargoSensitiveCheck(userId, taskContent, machineRemark);
    }

}
