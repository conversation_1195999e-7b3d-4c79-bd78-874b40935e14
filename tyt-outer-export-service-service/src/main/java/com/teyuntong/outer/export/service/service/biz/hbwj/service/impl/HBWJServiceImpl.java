package com.teyuntong.outer.export.service.service.biz.hbwj.service.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJDataResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import com.teyuntong.outer.export.service.client.tripartite.bean.TripartiteTokenBean;
import com.teyuntong.outer.export.service.service.biz.hbwj.client.HBWJClient;
import com.teyuntong.outer.export.service.service.biz.hbwj.client.HBWJInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.*;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJService;
import com.teyuntong.outer.export.service.service.common.property.HbwjProperties;
import com.teyuntong.outer.export.service.service.common.property.MjSignProperty;
import com.teyuntong.outer.export.service.service.common.utils.EccCryptoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 湖北我家相关接口类
 *
 * <AUTHOR>
 * @since 2024/07/11 17:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties({MjSignProperty.class,HbwjProperties.class})
public class HBWJServiceImpl implements HBWJService, InitializingBean {

    private final HbwjProperties hbwjProperties;

    private static final String REDIS_WJ_ACCESS_TOKEN_KEY = "WJ_ACCESS_TOKEN";

    private static final String PASSWORD_ERROR = "密码错误";

    //是否打开签名 true:打开 false:关闭
    private static final Boolean signOnOrOff = true;

    private ObjectMapper mapper;

    private final HBWJClient hBWJClient;

    private HBWJInterfaceClient hBWJInterfaceClient;

    private final MjSignProperty mjSignProperty;

    private final StringRedisTemplate stringRedisTemplate;


    @Override
    public List<PrincipalInfoVO> getPrincipalList() throws IOException {
        PrincipalListDO principalListDO = new PrincipalListDO();
        principalListDO.setScope("234234");
        Response<HBWJDataResult<List<PrincipalInfoVO>>> response = hBWJInterfaceClient.principalList(principalListDO).execute();
        log.info("【我家-查询主体列表】返回结果：{}",JSON.toJSONString(response.body()));
        return getData(response);
    }

    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return CreateWaybillThreeResponse 创建运单返回对象
     */
    @Override
    public WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception {
        log.info("开票-创建运单接口，请求参数：{}", JSON.toJSONString(createWaybillThreeRequest));
        Response<HBWJDataResult<CreateWaybillThreeResponse>> response = hBWJInterfaceClient.addWaybillThree(createWaybillThreeRequest, createWaybillThreeRequest.getUserCode()).execute();
        log.info("开票-创建运单接口，响应参数：{}", JSON.toJSONString(response));

        if (!response.isSuccessful()) {
            return WebResult.error(CommonErrorCode.INTERNAL_ERROR, null);
        }
        HBWJDataResult<CreateWaybillThreeResponse> hbwjDataResult = response.body();
        log.info("开票-创建运单接口，请求参数：{}，响应参数：{}", JSON.toJSONString(createWaybillThreeRequest), JSON.toJSONString(fromHBWJDataResult(hbwjDataResult)));
        return fromHBWJDataResult(hbwjDataResult);
    }

    @Override
    public Object getGaoDeDistrictVoList() throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.getGaoDeDistrictVoList().execute();
        return getData(response);
    }

    @Override
    public WebResult<List<BatchDoPayResponse>> batchDoPay(BatchDoPayRequest batchDoPayRequest, String userCode) throws Exception {
        log.info("开票-批量支付，paymentNos：{}，phone：{} vsgCode:{}", JSON.toJSONString(batchDoPayRequest.getPaymentNos()), batchDoPayRequest.getPhone(), batchDoPayRequest.getVsgCode());
        Response<HBWJDataResult<List<BatchDoPayResponse>>> response = hBWJInterfaceClient.batchDoPay(batchDoPayRequest, userCode).execute();
        log.info("开票-批量支付，paymentNos：{}，响应参数：{} ", JSON.toJSONString(batchDoPayRequest.getPaymentNos()), JSON.toJSONString(response.body()));
        HBWJDataResult<List<BatchDoPayResponse>> hbwjDataResult = response.body();
        WebResult<List<BatchDoPayResponse>> respWebResult = fromHBWJDataResult(hbwjDataResult);
        assert hbwjDataResult != null;
        String info = hbwjDataResult.getInfo();
        if(StringUtils.isNotEmpty(info) && info.contains(PASSWORD_ERROR)){
            respWebResult.setMsg(PASSWORD_ERROR);
        }
        return respWebResult;
    }

    @Override
    public WebResult<Long> invoiceCreate(InvoiceRequest invoiceRequest, String userCode) throws Exception {

        Response<HBWJDataResult<Long>> response = hBWJInterfaceClient.invoiceCreate(invoiceRequest,userCode).execute();
        log.info("开票-新增开票，请求参数：{}，响应参数：{}", JSON.toJSONString(invoiceRequest), JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<Long> invoiceUpdate(InvoiceRequest invoiceRequest, String userCode) throws Exception{
        Response<HBWJDataResult<Long>> response = hBWJInterfaceClient.invoiceUpdate(invoiceRequest,userCode).execute();
        log.info("开票-修改开票，请求参数：{}，响应参数：{}", JSON.toJSONString(invoiceRequest), JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<Object> doInvoiceCreate(InvoiceCreateRequest invoiceCreateRequest, String userCode) throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.doInvoiceCreate(invoiceCreateRequest,userCode).execute();
        log.info("发票创建 请求参数：{}，响应参数：{}", JSON.toJSONString(invoiceCreateRequest), JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<InvoiceResponse> queryInvoiceCallBackVo(InvoiceInfoRequest invoiceInfoRequest, String userCode) throws Exception{
        Response<HBWJDataResult<InvoiceResponse>> response = hBWJInterfaceClient.queryInvoiceCallBackVo(invoiceInfoRequest,userCode).execute();
        log.info("查询发票开票 invoiceNumber：{}，响应参数：{}", JSON.toJSONString(invoiceInfoRequest), JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<String> uploadWeightReceiptsNew(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, String userCode) throws Exception {
        Response<HBWJDataResult<String>> response = hBWJInterfaceClient.uploadWeightReceipts(uploadWeightReceiptsReqDto,userCode).execute();
        log.info("【我家-回单图片上传】请求参数:{} 返回结果：{}", JSON.toJSONString(uploadWeightReceiptsReqDto),JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<PaymentOrderResp> getPaymentDetailByNo(String paymentNo, String userCode) throws Exception {
        log.info("【支付单审核结果查询】开始 paymentNo:{} userCode：{}", paymentNo,userCode);
        PaymentInfoRequest paymentInfoRequest = new PaymentInfoRequest();
        paymentInfoRequest.setPaymentNo(paymentNo);
        Response<HBWJDataResult<PaymentOrderResp>> response = hBWJInterfaceClient.getPaymentDetailByNo(paymentInfoRequest,userCode).execute();
        log.info("【支付单审核结果查询】结束 paymentNo:{} resp：{}", paymentNo,JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }


    @Override
    public WebResult<Object> cancelFreight(CancelFreightRequest cancelFreightRequest, String userCode) throws Exception {
        log.info("【撤销运单的支付单】开始 param:{} userCode：{}", JSON.toJSONString(cancelFreightRequest),userCode);
        Response<HBWJDataResult<Object>> response  = hBWJInterfaceClient.cancelFreight(cancelFreightRequest,userCode).execute();
        log.info("【撤销运单的支付单】结束 param:{} resp：{}", JSON.toJSONString(cancelFreightRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<Object> paymentCancel(PaymentCancelRequest paymentCancelRequest, String userCode) throws Exception {
        log.info("【支付单作废】开始 param:{} userCode：{}", JSON.toJSONString(paymentCancelRequest),userCode);
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.paymentCancel(paymentCancelRequest, userCode).execute();
        log.info("【支付单作废】结束 param:{} resp：{}", JSON.toJSONString(paymentCancelRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<Object> receiptsUpload(ReceiptsUploadRequest receiptsUploadRequest, String userCode) throws Exception {
        log.info("【我家-运单凭证上传】开始 param:{} userCode：{}", JSON.toJSONString(receiptsUploadRequest),userCode);
        Response<HBWJDataResultV3<Object>> response = hBWJInterfaceClient.receiptsUpload(receiptsUploadRequest, userCode).execute();
        log.info("【我家-运单凭证上传】结束 param:{} resp：{}", JSON.toJSONString(receiptsUploadRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResultV3(response.body());
    }

    @Override
    public WebResult<Object> arriveWaybill(ArriveWaybillRequest arriveWaybillRequest, String userCode) throws Exception {
        log.info("【我家-运单运抵】开始 param:{} userCode：{}", JSON.toJSONString(arriveWaybillRequest),userCode);
        Response<HBWJDataResultV3<Object>> response = hBWJInterfaceClient.arriveWaybill(arriveWaybillRequest, userCode).execute();
        log.info("【我家-运单运抵】结束 param:{} resp：{}", JSON.toJSONString(arriveWaybillRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResultV3(response.body());
    }

    @Override
    public WebResult<Object> fundSendMsg(String userCode) throws Exception {
        log.info("【发送短信验证码 】开始 userCode：{}", userCode);
        Response<HBWJDataResultV3<Object>> response = hBWJInterfaceClient.fundSendMsg(userCode).execute();
        log.info("【发送短信验证码 】开始 userCode：{} resp: {}", userCode, JSON.toJSONString(response.body()));
        return fromHBWJDataResultV3(response.body());
    }

    @Override
    public WebResult<Object> forgetpaypwd(ForgetPayPwdRequest forgetPayPwdRequest, String userCode) throws Exception {
        log.info("开始 userCode：{} param: {} ", userCode, JSON.toJSONString(forgetPayPwdRequest));
        Response<HBWJDataResultV3<Object>> response = hBWJInterfaceClient.forgetpaypwd(forgetPayPwdRequest, userCode).execute();
        log.info("开始 userCode：{} param: {} resp: {}", userCode, JSON.toJSONString(forgetPayPwdRequest), JSON.toJSONString(response.body()));
        return fromHBWJDataResultV3(response.body());
    }

    /**
     * 将HBWJDataResult转换为WebResult
     */
    public  <T> WebResult<T> fromHBWJDataResult(HBWJDataResult<T> hbwjDataResult) {
        if (Objects.isNull(hbwjDataResult)) {
            return WebResult.error(CommonErrorCode.INTERNAL_ERROR, null);
        }
        WebResult<T> result = new WebResult<>();
        result.setCode(hbwjDataResult.getCode().toString());
        result.setMsg(StringUtils.isNotEmpty(hbwjDataResult.getInfo()) ? hbwjDataResult.getInfo() : hbwjDataResult.getMsg());
        result.setData(hbwjDataResult.getData());
        return result;
    }


    /**
     * 将HBWJDataResult转换为WebResult
     */
    public <T> WebResult<T> fromHBWJDataResultV3(HBWJDataResultV3<T> hbwjDataResult) {
        if (Objects.isNull(hbwjDataResult)) {
            return WebResult.error(CommonErrorCode.INTERNAL_ERROR, null);
        }
        WebResult<T> result = new WebResult<>();
        result.setCode(hbwjDataResult.getCode().toString());
        result.setMsg(StringUtils.isNotEmpty(hbwjDataResult.getInfo()) ? hbwjDataResult.getInfo() : hbwjDataResult.getMsg());
        result.setData(hbwjDataResult.getData());
        return result;
    }

    @Override
    public  WebResult<List<ApplyFreightResp>> applyFreight(ApplyFreightRequest applyFreightRequest, String userCode) throws Exception {
        Response<HBWJDataResult<List<ApplyFreightResp>>> response = hBWJInterfaceClient.applyFreight(applyFreightRequest, userCode).execute();
        log.info("【我家-运费申请】请求参数:{} 返回结果：{}", JSON.toJSONString(applyFreightRequest),JSON.toJSONString(response.body()));
        HBWJDataResult<List<ApplyFreightResp>> dataResult = response.body();
        return fromHBWJDataResult(dataResult);
    }
    /**
     * 修改运费
     *
     * <AUTHOR>
     * @param amountUpdateRequest 创建运单请求对象
     * @return boolean
     */
    @Override
    public boolean updateAmount(AmountUpdateRequest amountUpdateRequest) throws Exception {
        Response<HBWJDataResult<AmountUpdateResponse>> response = hBWJInterfaceClient.amountUpdate(amountUpdateRequest,amountUpdateRequest.getUserCode()).execute();
        Integer code = response.body().getCode();
        log.info("【我家-修改运费】返回结果：{}",response.body());
        if(code == 200){
            return true;
        }
        return false;
    }

    /**
     *  编辑运单
     * @param editWaybillThreeRequest
     * @return
     * @throws Exception
     */
    @Override
    public WebResult<EditWaybillThreeResponse> editWaybillThree(EditWaybillThreeRequest editWaybillThreeRequest) throws Exception {
        Response<HBWJDataResult<EditWaybillThreeResponse>> response = hBWJInterfaceClient.editWaybillThree(editWaybillThreeRequest,editWaybillThreeRequest.getUserCode()).execute();
        log.info("【我家-编辑运单】请求参数:{} 返回结果：{}", JSON.toJSONString(editWaybillThreeRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    /**
     * 新增司机
     *
     * <AUTHOR>
     * @param report 新增司机对象
     * @return driverDetailResponse 返回司机信息
     */
    @Override
    public DriverDetailResponse createDriver(HbwjDriverReport report) throws Exception{
        log.info("【我家-司机】请求参数:{}", report.toString());
        Response<HBWJDataResult<DriverDetailResponse>> response = hBWJInterfaceClient.createDriver(report,report.getUserCode()).execute();
        log.info("【我家-司机】请求参数:{} 返回结果：{}", JSON.toJSONString(report),JSON.toJSONString(response.body()));
        Integer code = response.body().getCode();
        String info = response.body().getInfo();
//        String msg = response.body().getMsg();
        DriverDetailResponse data = response.body().getData();
        if(null == data){
            data = new DriverDetailResponse();
        }
        data.setInfo(info);
        data.setCode(code);
//        data.setMsg(msg);
        log.info("【我家-司机】返回结果对象：{}", JSON.toJSONString(data));
        return data;
    }

    @Override
    public WebResult<Object> pickGoods(PickGoodsThreeRequest pickGoodsThreeRequest) throws Exception {
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        threeConfig.setIsThreeUserLoadTime(false);
        pickGoodsThreeRequest.setThreeConfig(threeConfig);
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.pickGoods(pickGoodsThreeRequest,pickGoodsThreeRequest.getUserCode()).execute();
        log.info("【我家-起运】请求参数:{} 返回结果：{}", JSON.toJSONString(pickGoodsThreeRequest),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }

    // todo cjg 起运V3
    @Override
    public WebResult<Object> pickGoodsV3(PickGoodsThreeV3Request pickGoodsThreeV3Request) throws Exception {
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        threeConfig.setIsThreeUserLoadTime(false);
        pickGoodsThreeV3Request.setThreeConfig(threeConfig);
        Response<HBWJDataResultV3<Object>> response = hBWJInterfaceClient.pickGoodsV3(pickGoodsThreeV3Request,pickGoodsThreeV3Request.getUserCode()).execute();
        log.info("【我家-起运V3】请求参数:{} 返回结果：{}", JSON.toJSONString(pickGoodsThreeV3Request),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResultV3(response.body());
    }


    @Override
    public  WebResult<Object> arriveGoods(ArriveGoodsThreeRequest arriveGoodsThreeRequest) throws Exception {
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        threeConfig.setIsThreeUserLoadTime(false);
        arriveGoodsThreeRequest.setThreeConfig(threeConfig);
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.arriveGoods(arriveGoodsThreeRequest,arriveGoodsThreeRequest.getUserCode()).execute();
        log.info("【我家-抵运】请求参数:{} 返回结果：{}", JSON.toJSONString(arriveGoodsThreeRequest),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }

    @Override
    public WebResult<WaybillThreeOffsetResp> getWaybillThreeOffset(WaybillThreeOffsetRequest waybillThreeOffsetRequest) throws Exception {
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        threeConfig.setIsThreeUserLoadTime(false);
        waybillThreeOffsetRequest.setThreeConfig(threeConfig);
        Response<HBWJDataResult<WaybillThreeOffsetResp>> response = hBWJInterfaceClient.getWaybillThreeOffset(waybillThreeOffsetRequest, waybillThreeOffsetRequest.getUserCode()).execute();
        log.info("【我家-运单回调详情】请求参数:{} 返回结果：{}", JSON.toJSONString(waybillThreeOffsetRequest), JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }
    public  WebResult<Object> cancelWaybill(CancelOrderThreeRequest cancelOrderThreeRequest) throws Exception {
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        threeConfig.setIsThreeUserLoadTime(false);
        cancelOrderThreeRequest.setThreeConfig(threeConfig);
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.cancelWaybill(cancelOrderThreeRequest,cancelOrderThreeRequest.getUserCode()).execute();
        log.info("【我家-取消运单】请求参数:{} 返回结果：{}", JSON.toJSONString(cancelOrderThreeRequest),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }

    @Override
    public FileUpLoadResp uploadFile(MultipartFile uploadFile) throws Exception {
        Response<HBWJDataResult<FileUpLoadResp>> response = hBWJInterfaceClient.fileUpload(uploadFile).execute();
        log.info("【我家-图片上传】请求参数:{} 返回结果：{}", JSON.toJSONString(uploadFile),JSON.toJSONString(response.body()));
        return getData(response);
    }

    @Override
    public CreateContractResponse createContract(CreateContractRequest request) throws Exception {
        Response<HBWJDataResult<CreateContractResponse>> response = hBWJInterfaceClient.createContract(request, request.getUserCode()).execute();
        log.info("【我家-新增合同】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        checkCode(response.body());
        return getData(response);
    }


    @Override
    public CreateProjectResponse createProject(CreateProjectRequest request, String userCode) throws Exception {
        Response<HBWJDataResult<CreateProjectResponse>> response = hBWJInterfaceClient.createProject(request, userCode).execute();
        log.info("【我家-新增项目】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        checkCode(response.body());
        return getData(response);
    }

    @Override
    public QueryWalletResponse selectCustomerRechargeWalletList(QueryWalletRequest request) throws Exception {
        Response<HBWJDataResult<QueryWalletResponse>> response = hBWJInterfaceClient.selectCustomerRechargeWalletList(request, request.getUserCode()).execute();
        log.info("【我家-查询钱包信息】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        return getData(response);
    }

    @Override
    public WebResult<Object> setPayPwd(SavePayPwdRequest request) throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.setPayPwd(request, request.getUserCode()).execute();
        log.info("【我家-设置支付密码】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }

    @Override
    public  WebResult<Object> changePayPwd(ChangePayPwdRequest request) throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.changePayPwd(request, request.getUserCode()).execute();
        log.info("【我家-修改支付密码】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        return fromHBWJDataResult(response.body());
    }

    @Override
    public void changePhoneCaptchaSendForAccount(ChangePhoneCaptchaSendRequest request) throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.changePhoneCaptchaSendForAccount(request, request.getUserCode()).execute();
        log.info("【我家-发送更换手机验证码】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        checkCode(response.body());
    }

    @Override
    public void changePhone(ChangePhoneRequest request) throws Exception {
        Response<HBWJDataResult<Object>> response = hBWJInterfaceClient.changePhone(request, request.getUserCode()).execute();
        log.info("【我家-修改手机号】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        checkCode(response.body());
    }

    @Override
    public ChangePhoneCaptchaCheckResponse changePhoneCaptchaCheck(ChangePhoneCaptchaCheckRequest request) throws Exception {
        Response<HBWJDataResult<String>> response = hBWJInterfaceClient.changePhoneCaptchaCheck(request, request.getUserCode()).execute();
        log.info("【我家-检查更换手机号验证码】请求参数:{} 返回结果：{}", JSON.toJSONString(request),JSON.toJSONString(response.body()));
        checkCode(response.body());
        return ChangePhoneCaptchaCheckResponse.builder().tick(getData(response)).build();
    }

    @Override
    public CreateCompanyResponse createCompany(CreateCompanyRequest createCompanyRequest) throws IOException {
        Response<HBWJDataResult<CreateCompanyResponse>> response = hBWJInterfaceClient.createCompany(createCompanyRequest).execute();
        HBWJDataResult<CreateCompanyResponse> body = response.body();
        log.info("【我家-创建企业认证信息】请求参数:{} 返回结果：{}", JSON.toJSONString(createCompanyRequest),JSON.toJSONString(body));
        if (body != null && body.getCode() != 10000) {
            String msg = StringUtils.isNotBlank(body.getInfo()) ? body.getInfo() : body.getMsg();
            String code = "该企业已存在".equals(msg) ? "00022038" : CommonErrorCode.ERROR_SYS_BUSY.getCode();
            String message = "00022038".equals(code) ? "您认证的企业已被其他平台认证，暂不支持签约，如有问题请联系客服" : msg;
            throw new BusinessException(new ErrorCodeBase() {
                @Override
                public String getCode() {
                    return code;
                }
                @Override
                public String getMsg() {
                    return message;
                }
                @Override
                public boolean isSuccess() {
                    return false;
                }
            });
        }
        return getData(response);
    }

    @Override
    public CreateDetailVO queryCompanyDetail(CreateDetailDO createDetailDO) throws IOException {
        Response<HBWJDataResult<CreateDetailVO>> response = hBWJInterfaceClient.queryCompanyDetail(createDetailDO).execute();
        log.info("【我家-查询企业信息】请求参数:{} 返回结果：{}", JSON.toJSONString(createDetailDO),JSON.toJSONString(response.body()));
        return getData(response);
    }

    @Override
    public String uploadWeightReceipts(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, String userCode) throws IOException {
        Response<HBWJDataResult<String>> response = hBWJInterfaceClient.uploadWeightReceipts(uploadWeightReceiptsReqDto,userCode).execute();
        log.info("【我家-回单图片上传】请求参数:{} 返回结果：{}", JSON.toJSONString(uploadWeightReceiptsReqDto),JSON.toJSONString(response.body()));
        return getData(response);
    }

    @Override
    public String getFundVerificationCode(String mobile,String userCode) throws IOException {
        FundVerificationCodeReqDto fundVerificationCodeReqDto = new FundVerificationCodeReqDto();
        fundVerificationCodeReqDto.setMobile(mobile);
        Response<HBWJDataResult<String>> response = hBWJInterfaceClient.getFundVerificationCode(fundVerificationCodeReqDto, userCode).execute();
        log.info("【我家-获取支付验证码】请求参数:{} 返回结果：{}", JSON.toJSONString(fundVerificationCodeReqDto),JSON.toJSONString(response.body()));
        return getData(response);
    }

    @Override
    public  DriverDetailResponse createCar(HbwjCarReport report)throws IOException{
        Response<HBWJDataResult<DriverDetailResponse>> response = hBWJInterfaceClient.createCar(report,report.getUserCode()).execute();
        log.info("【我家-新增车辆】请求参数:{} 返回结果：{}", JSON.toJSONString(report),response);
        Integer code = response.body().getCode();
        String info = response.body().getInfo();
//        String msg = response.body().getMsg();
        DriverDetailResponse data = response.body().getData();
        if(null == data){
            data = new DriverDetailResponse();
        }
        data.setInfo(info);
        data.setCode(code);
//        data.setMsg(msg);
        log.info("【我家-新增车辆】返回结果对象：{}", JSON.toJSONString(data));
        return data;
    }

    @Override
    public  WebResult<CreateProtocolResponse> createProtocol(CreateProtocolRequest createProtocolRequest) throws IOException {
        Response<HBWJDataResult<CreateProtocolResponse>> response = hBWJInterfaceClient.createProtocol(createProtocolRequest,createProtocolRequest.getUserCode()).execute();
        log.info("【我家-生成合同】请求参数:{} 返回结果：{}", JSON.toJSONString(createProtocolRequest),JSON.toJSONString(response.body()));
        return fromHBWJDataResult(response.body());
    }

    private <T> void  checkCode(HBWJDataResult<T> body) {
        if (body != null && body.getCode() != 10000 && body.getCode() != 200) {
            String msg = StringUtils.isNotBlank(body.getInfo()) ? body.getInfo() : body.getMsg();
            String code = String.valueOf(body.getCode());
            throw new BusinessException(new ErrorCodeBase() {
                @Override
                public String getCode() {
                    return code;
                }
                @Override
                public String getMsg() {
                    return msg;
                }
                @Override
                public boolean isSuccess() {
                    return false;
                }
            });
        }
    }

    private <T> T getData(Response<HBWJDataResult<T>> response) {
        if (!response.isSuccessful()) {
            return null;
        }
        HBWJDataResult<T> bodyResult = response.body();
        if (HBWJDataResult.checkSuccess(bodyResult)) {
            return bodyResult.getData();
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        //拦截器
        Interceptor merchantApiInterceptor = chain -> {
            Request request = chain.request();
            RequestBody requestBody = request.body();
            Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            String bodyContent = buffer.readUtf8();
            log.info("bodyContent"+bodyContent);
            long nonce = EccCryptoUtil.generateNonce();
            long timestamp = System.currentTimeMillis();

            Pair<String, String> pair = EccCryptoUtil.sign(hbwjProperties.getPrivateKey(), hbwjProperties.getPublicKey(), nonce, timestamp, bodyContent, hbwjProperties.getEncryptedFields());
            RequestBody newBody = RequestBody.create(pair.getValue().getBytes());
            String token = getToken();

            //是否打开签名 true:打开 false:关闭
            if(signOnOrOff){
                Request newRequest = request.newBuilder()
                        .url(request.url())
                        .method(request.method(), newBody)
                        .header("Authorization", token)
                        .header("Content-Type", requestBody.contentType().toString())
                        .header("X-Signature", pair.getKey())
                        .header("X-Timestamp", String.valueOf(timestamp))
                        .header("X-Nonce", String.valueOf(nonce))
                        .header("user-code", StringUtils.isNotBlank(request.header("user-code")) ? request.header("user-code") : "")
                        .build();
                return chain.proceed(newRequest);
            }else{
                Request newRequest = request.newBuilder()
                        .url(request.url())
                        .method(request.method(), requestBody)
                        .header("Authorization", token)
                        .header("Content-Type", requestBody.contentType().toString())
                        .header("X-Timestamp", String.valueOf(timestamp))
                        .header("X-Nonce", String.valueOf(nonce))
                        .header("user-code", StringUtils.isNotBlank(request.header("user-code")) ? request.header("user-code") : "")
                        .build();
                return chain.proceed(newRequest);
            }
        };

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(15))
                .readTimeout(Duration.ofSeconds(15))
                .addInterceptor(merchantApiInterceptor)
                .build();

        Retrofit merchantRetrofit = new Retrofit.Builder()
                .baseUrl(mjSignProperty.getWjApiUrl())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(okHttpClient)
                .build();

        hBWJInterfaceClient = merchantRetrofit.create(HBWJInterfaceClient.class);
    }

    public String getToken() throws IOException {
        String token = stringRedisTemplate.opsForValue().get(REDIS_WJ_ACCESS_TOKEN_KEY);
        if (StringUtils.isNotEmpty(token)) {
            return "Bearer " + token;
        }
        String lockKey = REDIS_WJ_ACCESS_TOKEN_KEY + "_lock";
        String lockValue = String.valueOf(System.currentTimeMillis() + 10000);
        Boolean acquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));
        log.info("getToken 获取token锁【{}】", acquired);
        if (Boolean.TRUE.equals(acquired)) {
            try {
                return doGetToken();
            } finally {
                stringRedisTemplate.delete(lockKey);
            }
        }else{
            log.info("getToken retry 获取token锁【{}】", acquired);
            // 休眠100ms后重试,获取
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return getToken();
        }
    }

    @NotNull
    private String doGetToken() throws IOException {
        String clientCredentials = mjSignProperty.getClientId() + ":" + mjSignProperty.getClientSecret();
        String encodedCredentials = "Basic " + Base64.getEncoder().encodeToString(clientCredentials.getBytes());
        TripartiteTokenBean bean = new TripartiteTokenBean();
        bean.setClient_id(mjSignProperty.getClientId());
        bean.setClient_secret(mjSignProperty.getClientSecret());
        retrofit2.Response<HBWJBaseResult<Object>> response = hBWJClient.getToken(encodedCredentials, "client_credentials",
                mjSignProperty.getClientId(), mjSignProperty.getClientSecret()).execute();
        HBWJBaseResult<Object> result = response.body();
        String token = result.getAccess_token();
        Long expiresIn = Long.valueOf(result.getExpires_in());
        log.info("调用三方token【{}】", token);
        stringRedisTemplate.opsForValue().set(REDIS_WJ_ACCESS_TOKEN_KEY, token, Duration.ofSeconds(expiresIn));
        return "Bearer " + token;
    }
}

