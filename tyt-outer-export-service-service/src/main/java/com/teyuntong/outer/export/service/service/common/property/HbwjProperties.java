package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 湖北我家配置类
 *
 * <AUTHOR>
 * @since 2024-7-26 18:18:47
 */
@Data
@ConfigurationProperties(prefix = "hbwj.sign")
public class HbwjProperties {

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 加密字段
     */
    private List<String> encryptedFields;
}
