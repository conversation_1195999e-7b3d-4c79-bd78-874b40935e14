package com.teyuntong.outer.export.service.service.biz.cticloud.axb.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Maps;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.*;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.biz.cticloud.CticloudClient;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.AXBBindResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.CticloudAxbApi;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper.TytAxbBindInfoMapper;
import com.teyuntong.outer.export.service.service.common.utils.AppConfig;
import com.teyuntong.outer.export.service.service.mq.dingding.DingDingSendService;
import com.teyuntong.outer.export.service.service.mq.dingding.DingdingMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/6/19 上午9:54
 */
@Service
@Slf4j
@DS("common")
public class CtiCloudAxbServiceImpl implements IAxbService {

    private final CticloudAxbApi axbApi;
    private final CticloudAxbApi axbApi2;
    private final ICticloudAxbLogService cticloudAxbLogService;
    @Autowired
    private TytAxbBindInfoMapper tytAxbBindInfoMapper;
    @Autowired
    private DingDingSendService dingdingSendService;

    public CtiCloudAxbServiceImpl(CticloudClient cticloudClient, ICticloudAxbLogService cticloudAxbLogService) {
        this.axbApi = cticloudClient.getCticloudAxBApi();
        this.axbApi2 = cticloudClient.getCticloudAxBApi2();
        this.cticloudAxbLogService = cticloudAxbLogService;
    }

    @Autowired
    private AppConfig appConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TytAxbBindInfo axbBind(AxbBindReq req) {
        Date now = new Date();

        Integer expiration = req.getExpiration();

        if (expiration == null || expiration == 0 || expiration < 70) {
            // 暂时不允许永久有效，永久有效默认过期时间70s
            expiration = (int) TimeUnit.DAYS.toSeconds(70);
        }

        HashMap<String, String> params = Maps.newHashMap();
        params.put("callrecording", "1");
        if (StringUtils.isNotBlank(req.getUserField())) {
            params.put("userField", req.getUserField());
            log.info("userField : {}", req.getUserField());
        }

        Call<CticloudResp<AXBBindResp>> call;
        if (req.getTianrunAccount() != null && req.getTianrunAccount() == 2) {
            call = axbApi2.axbBind(req.getTelA(), req.getTelB(), null, expiration.toString(),
                    params);
        } else {
            call = axbApi.axbBind(req.getTelA(), req.getTelB(), null, expiration.toString(),
                    params);
        }

        Response<CticloudResp<AXBBindResp>> response;
        String subId;
        try {
            response = call.execute();
            subId = Optional.of(response).map(Response::body).map(CticloudResp::getData).map(AXBBindResp::getSubId).orElse(null);
            cticloudAxbLogService.logRequest(1, call, response, now, subId);
        } catch (Exception e) {
            log.error("cticloud 请求失败", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        boolean isPermanent = expiration == null || expiration == 0;

        CticloudResp<AXBBindResp> cticloudResp = response.body();
        if (cticloudResp == null || !Objects.equals(cticloudResp.getResult(), 0)) {
            // 三方请求失败
            log.error("cticloud axb绑定未成功 请求: {}, 响应: {}", req, cticloudResp);
            sendDingTalkNotify(cticloudResp, req.getTianrunAccount());
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_BIND_FAIL_ERROR);
        }

        AXBBindResp axbBindResp = cticloudResp.getData();

        // 过期日期
        Date expirationDate = null;
        if (!isPermanent) {
            expirationDate = DateUtils.addSeconds(now, expiration);
        }

        TytAxbBindInfo tytAxbBindInfo = TytAxbBindInfo.builder().telA(req.getTelA()).thirdPartyId(subId).telB(req.getTelB()).telX(axbBindResp.getTelX()).bizId(req.getBizId()).bizType(req.getBizType()).extraField(req.getExtraField()).delFlag(false).createTime(now).updateTime(now).isPermanent(isPermanent).expirationDate(expirationDate).build();
        if (req.getTianrunAccount() != null && req.getTianrunAccount() == 2) {
            tytAxbBindInfo.setThirdPartyType(2);
        } else {
            tytAxbBindInfo.setThirdPartyType(1);
        }

        tytAxbBindInfoMapper.insertSelective(tytAxbBindInfo);

        return tytAxbBindInfo;
    }

    private void sendDingTalkNotify(CticloudResp<?> cticloudResp, int tianrunAccount) {
        try {
            DingdingMessage message = new DingdingMessage();
            message.setRobotName("axb");
            message.setMessage(String.format("%s 虚拟号绑定异常 账户%s, 错误信息: %s", appConfig.getActiveProfile(), tianrunAccount, cticloudResp.getDescription()));
            dingdingSendService.sendDingdingGroupNotify(message);
        } catch (Exception e) {
            log.error("钉钉通知发送失败 ", e);
        }
    }

    @Override
    public TytAxbBindInfo getAxbInfoByThirdPartyId(String thirdPartyId) {
        TytAxbBindInfo tytAxbBindInfo = new TytAxbBindInfo();
        tytAxbBindInfo.setThirdPartyId(thirdPartyId);
        tytAxbBindInfo.setDelFlag(false);
        return tytAxbBindInfoMapper.selectByThirdPartyId(tytAxbBindInfo);
    }

    @Override
    public List<TytAxbBindInfo> getAxbInfo(Integer bizType, Long bizId, String telA, String telB, String extraField) {
        TytAxbBindInfo tytAxbBindInfo = new TytAxbBindInfo();
        tytAxbBindInfo.setBizType(bizType);
        tytAxbBindInfo.setBizId(bizId);
        tytAxbBindInfo.setDelFlag(false);
        tytAxbBindInfo.setTelA(telA);
        tytAxbBindInfo.setTelB(telB);
        tytAxbBindInfo.setExtraField(extraField);
        tytAxbBindInfo.setTelA(telA);
        tytAxbBindInfo.setTelB(telB);
        tytAxbBindInfo.setExtraField(extraField);

        return tytAxbBindInfoMapper.selectList(tytAxbBindInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void axbUpdate(AxbUpdateReq req) {
        Date now = new Date();

        TytAxbBindInfo tytAxbBindInfo = tytAxbBindInfoMapper.selectById(req.getId());

        if (tytAxbBindInfo == null) {
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_BIND_NOT_EXIST_ERROR);
        }

        Integer expiration = req.getExpiration();

        if (expiration == null || expiration == 0 || expiration < 70) {
            // 暂时不允许永久有效，永久有效默认过期时间70s
            expiration = (int) TimeUnit.DAYS.toSeconds(70);
        }

        HashMap<String, String> params = Maps.newHashMap();
        if (StringUtils.isNotBlank(req.getUserField())) {
            params.put("userField", req.getUserField());
            log.info("userField : {}", req.getUserField());
        }

        Call<CticloudResp<Object>> call;
        if (tytAxbBindInfo.getThirdPartyType() == 1) {
            call = axbApi.axbUpdateBind(tytAxbBindInfo.getThirdPartyId(),
                    req.getTelB(), req.getTelB(), expiration.toString(), params);
        } else {
            call = axbApi2.axbUpdateBind(tytAxbBindInfo.getThirdPartyId(),
                    req.getTelB(), req.getTelB(), expiration.toString(), params);
        }

        Response<CticloudResp<Object>> response;
        try {
            response = call.execute();
            cticloudAxbLogService.logRequest(2, call, response, now, tytAxbBindInfo.getThirdPartyId());
        } catch (Exception e) {
            log.error("cticloud 请求失败", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        CticloudResp<Object> cticloudResp = response.body();

        if (cticloudResp == null || !Objects.equals(cticloudResp.getResult(), 0)) {
            // 三方请求失败
            log.error("cticloud axb修改未成功 请求: {}, 响应: {}", req, cticloudResp);
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_UPDATE_FAIL_ERROR);
        }

        boolean isPermanent = expiration == null || expiration == 0;

        TytAxbBindInfo update = TytAxbBindInfo.builder().id(tytAxbBindInfo.getId()).telA(req.getTelA()).telB(req.getTelB()).updateTime(now).expirationDate(DateUtils.addSeconds(now, expiration)).isPermanent(isPermanent).extraField(req.getExtraField()).build();

        tytAxbBindInfoMapper.updateSelective(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void axbDelete(Long id) {
        Date now = new Date();

        TytAxbBindInfo tytAxbBindInfo = tytAxbBindInfoMapper.selectById(id);

        if (tytAxbBindInfo == null) {
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_BIND_NOT_EXIST_ERROR);
        }

        Call<CticloudResp<Object>> call;
        if (tytAxbBindInfo.getThirdPartyType() == 1) {
            call = axbApi.axbUnbind(tytAxbBindInfo.getThirdPartyId(),
                    null, null);
        } else {
            call = axbApi2.axbUnbind(tytAxbBindInfo.getThirdPartyId(),
                    null, null);
        }

        Response<CticloudResp<Object>> response;
        try {
            response = call.execute();
            cticloudAxbLogService.logRequest(3, call, response, now, tytAxbBindInfo.getThirdPartyId());
        } catch (Exception e) {
            log.error("cticloud 请求失败", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        CticloudResp<Object> cticloudResp = response.body();

        if (cticloudResp == null || !Objects.equals(cticloudResp.getResult(), 0)) {
            // 三方请求失败
            log.error("cticloud axb删除未成功 subId: {}, 响应: {}", id, cticloudResp);
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_DELETE_FAIL_ERROR);
        }

        TytAxbBindInfo update = TytAxbBindInfo.builder().id(id).updateTime(now).delFlag(true).build();

        tytAxbBindInfoMapper.updateSelective(update);
    }

    @Override
    public CDRsResp getCDR(CDRReq req) {
        CDRsResp cdr1 = getCDR1(req);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.info("获取通话记录休眠失败");
        }
        CDRsResp cdr2 = getCDR2(req);

        CDRsResp cdRsResp = new CDRsResp();
        cdRsResp.setCdrs(new ArrayList<>());

        if (cdr1 != null && CollectionUtils.isNotEmpty(cdr1.getCdrs())) {
            cdRsResp.setCdrs(cdr1.getCdrs());
        }

        if (cdr2 != null && CollectionUtils.isNotEmpty(cdr2.getCdrs())) {
            cdRsResp.getCdrs().addAll(cdr2.getCdrs());
        }

        cdRsResp.setPageTabs(new PageTabs("1", "2000", "1", String.valueOf(cdRsResp.getCdrs().size())));
        return cdRsResp;
    }

    public CDRsResp getCDR1(CDRReq req) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("startTime", req.getStartTime());
        param.put("endTime", req.getEndTime());
        param.put("pushType", "finish");
        param.put("pageSize", 1);
        Call<CticloudResp<CDRsResp>> call = axbApi.vncCallQueryCdr(param);
        Response<CticloudResp<CDRsResp>> response;
        try {
            response = call.execute();
        } catch (Exception e) {
            log.error("cticloud 请求失败", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }
        CticloudResp<CDRsResp> cticloudResp = response.body();
        if (cticloudResp == null || !Objects.equals(cticloudResp.getResult(), 0)) {
            // 三方请求失败
            log.error("cticloud 获取话单请求失败 1 请求: {}, 响应: {}", req, cticloudResp);
            sendDingTalkNotify(cticloudResp, 1);
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_GET_FAIL_ERROR);
        }
        if (cticloudResp.getData() != null && CollectionUtils.isNotEmpty(cticloudResp.getData().getCdrs())) {
            List<CDR> cdrs = cticloudResp.getData().getCdrs();
            for (CDR cdr : cdrs) {
                TytAxbBindInfo tytAxbBindInfo = new TytAxbBindInfo();
                tytAxbBindInfo.setThirdPartyId(cdr.getSubId());
                TytAxbBindInfo result = tytAxbBindInfoMapper.selectByThirdPartyId(tytAxbBindInfo);
                if (result != null) {
                    cdr.setSrcMsgId(result.getBizId());
                    cdr.setCarUserId(Long.parseLong(result.getExtraField()));
                }
            }
        }

        return cticloudResp.getData();
    }

    public CDRsResp getCDR2(CDRReq req) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("startTime", req.getStartTime());
        param.put("endTime", req.getEndTime());
        param.put("pushType", "finish");
        param.put("pageSize", 1);
        Call<CticloudResp<CDRsResp>> call = axbApi2.vncCallQueryCdr(param);
        Response<CticloudResp<CDRsResp>> response;
        try {
            response = call.execute();
        } catch (Exception e) {
            log.error("cticloud 请求失败", e);
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }
        CticloudResp<CDRsResp> cticloudResp = response.body();
        if (cticloudResp == null || !Objects.equals(cticloudResp.getResult(), 0)) {
            // 三方请求失败
            log.error("cticloud 获取话单请求失败 2 请求: {}, 响应: {}", req, cticloudResp);
            sendDingTalkNotify(cticloudResp, 2);
            throw new BusinessException(OuterExportErrorCode.COMMON_AXB_GET_FAIL_ERROR);
        }
        if (cticloudResp.getData() != null && CollectionUtils.isNotEmpty(cticloudResp.getData().getCdrs())) {
            List<CDR> cdrs = cticloudResp.getData().getCdrs();
            for (CDR cdr : cdrs) {
                TytAxbBindInfo tytAxbBindInfo = new TytAxbBindInfo();
                tytAxbBindInfo.setThirdPartyId(cdr.getSubId());
                TytAxbBindInfo result = tytAxbBindInfoMapper.selectByThirdPartyId(tytAxbBindInfo);
                if (result != null) {
                    cdr.setSrcMsgId(result.getBizId());
                    cdr.setCarUserId(Long.parseLong(result.getExtraField()));
                }
            }
        }

        return cticloudResp.getData();
    }

}
