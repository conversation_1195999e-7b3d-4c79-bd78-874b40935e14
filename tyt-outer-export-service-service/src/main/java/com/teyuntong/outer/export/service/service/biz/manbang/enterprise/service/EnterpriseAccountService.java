package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service;

import com.teyuntong.outer.export.service.client.enterprise.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
* 【满帮】企业相关接口
* <AUTHOR>
* @since 2024/4/2 19:01
*/
public interface EnterpriseAccountService {

    CreateEnterpriseApiResp createEnterprise(@RequestBody @Validated EnterpriseAccountApiReq req);

    Boolean enterpriseOpenAccount(@RequestBody @Validated EnterpriseAccountApiReq req);

    EnterpriseOpenNetAccountApiResp enterpriseOpenNetAccount(@RequestBody @Validated EnterpriseAccountApiReq req);

    EnterpriseOpenNetAccountApiResp queryEnterpriseNetAccountInfo(@RequestBody @Validated EnterpriseAccountApiReq req);

    QueryEnterpriseBalanceApiResp queryEnterpriseBalance(@RequestBody @Validated QueryEnterpriseBalanceApiReq req);

    ResultMsgBean queryEnterpriseBill(@RequestBody @Validated QueryEnterpriseBillApiReq req);
}
