package com.teyuntong.outer.export.service.service.biz.manbang.location.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/18 10:12
 */
@Data
public class AppLastPositionVO {

    private String userIp;
    private Long userId;
    private Long telephone;
    private String deviceId;
    private Double lon;
    private Double lat;
    private Double gclon;
    private Double gclat;
    private Double bdlng;
    private Double bdlat;
    private Integer provinceId;
    private String provinceName;
    private Integer cityId;
    private String cityName;
    private Integer districtId;
    private String districtName;
    private String address;
    private Integer type;
    private Long positionTime;
    private Double accuracy;
    private Double speed;
    private Double direction;
    private Long uploadTime;
    private Integer platformType;
    private Integer flag;
    private Integer userType;
    private String errorMsg;
    private Long updateTime;
    private Long createTime;
    private Integer resourceId;
    private Double mileage;
    private Double height;
    private Double angleInTheNorthDirection;
    private Object dataResource;
    private Integer apiType;
    private Double firstTempNum;
    private Double secondTempNum;
    private Double thirdTempeNum;
    private Double fourthTempNum;
    private Object channelType;
    private Integer uploadResource;
    private String gdDeviceId;
    private Double mbWGSLon;
    private Double mbWGSLat;
    private Integer phoneType;
    private Integer gsmLac;
    private Integer gsmCid;
    private Integer cdmaNetworkId;
    private Integer cdmaSystemId;
    private Integer cdmaBaseStationId;
    private Double cdmaBaseStationLon;
    private Double cdmaBaseStationLat;
    private Integer locationType;
    private Double positionAccuracy;
    private Object sceneType;
    private String appType;
    private Integer currentAppStatus;
}
