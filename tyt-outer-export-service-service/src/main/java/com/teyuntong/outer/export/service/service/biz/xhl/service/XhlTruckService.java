package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;

/**
 * 翔和翎 车辆相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlTruckService {

    /**
     * 新增车辆
     *
     * @param truckEntity 新增车辆信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveTruck(XhlTruckEntity truckEntity) throws Exception;

    /**
     * 修改车辆
     *
     * @param truckEntity 修改车辆信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> updateTruck(XhlTruckEntity truckEntity) throws Exception;

    /**
     * 查询车辆
     *
     * @param plateNumber 车牌号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryTruck(String plateNumber) throws Exception;


}
