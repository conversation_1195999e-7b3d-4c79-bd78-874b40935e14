package com.teyuntong.outer.export.service.service.biz.corporate.service;

import com.teyuntong.outer.export.service.client.corporate.vo.CorporateBaseInfoBean;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.CorporateBaseInfo;

/**
 * <AUTHOR>
 * @version 10
 * @date 2021/01/20
 */
public interface CorporateBaseInfoService {

    /**
     * 查询企业工商基本信息
     *
     * @param keyword 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
     * @return CorporateBaseInfo
     */
    CorporateBaseInfoBean addAndGetCorporateBaseInfo(String keyword);

    /**
     * 数据库查询企业工商基本信息
     *
     * @param keyword 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
     * @return CorporateBaseInfo 企业基本信息，查询失败返回null
     */
    CorporateBaseInfo getCorporateBaseInfo(String keyword);

    /**
     * 忽略插入企业工商基本信息
     *
     * @param baseInfo CorporateBaseInfo
     * @return 更新条数
     */
    int insertCorporateBaseInfo(CorporateBaseInfo baseInfo);
}
