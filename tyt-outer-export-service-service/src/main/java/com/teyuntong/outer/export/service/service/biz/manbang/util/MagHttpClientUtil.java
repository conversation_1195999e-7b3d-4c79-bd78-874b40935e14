package com.teyuntong.outer.export.service.service.biz.manbang.util;

import com.wlqq.wallet.gateway.client.enums.ErrorCode;
import com.wlqq.wallet.gateway.client.exception.GateWayClientException;
import com.wlqq.wallet.gateway.client.util.FileInfo;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpRequest;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpResponse;
import com.wlqq.wallet.gateway.client.util.httpclient.HttpResultType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

/**
 * 访问Mag 服务的HTTP 工具类
 * Created by ZuoQing Li
 * 2017/4/5.
 */
public class MagHttpClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(MagHttpClientUtil.class);

    /**
     * 建立请求，以模拟远程HTTP的POST请求方式构造并获取钱包的处理结果
     *
     * @param sParaTemp    请求参数数组
     * @param inputCharset 编码类型
     * @param gatewayUrl   访问url
     * @return 钱包处理结果
     * @throws Exception
     */
    public static String sendRequest(Map<String, String> sParaTemp,
                                     String inputCharset, String gatewayUrl, int connectionRequestTimeout, int connectionTimeout, int readTimeout) {
        try {
            // 待请求参数数组
            HttpProtocolHandler httpProtocolHandler = HttpProtocolHandler.getInstance();
            HttpRequest request = new HttpRequest(HttpResultType.STRING);
            // 设置编码集
            request.setCharset(inputCharset);
            request.setMethod(HttpRequest.METHOD_POST);
            request.setConnectionRequestTimeout(connectionRequestTimeout);
            request.setConnectionTimeout(connectionTimeout);
            request.setTimeout(readTimeout);

            request.setParameters(sParaTemp);
            request.setUrl(gatewayUrl);
            HttpResponse response = null;
            response = httpProtocolHandler.execute(request, null, null);
            if (response == null) {
                return null;
            }
            String strResult = null;
            strResult = response.getStringResult();
            return strResult;
        } catch (Exception e) {
            logger.error("请求http异常", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        }
    }
    /**
     * 建立请求，以模拟远程HTTP的POST请求方式构造并获取钱包的处理结果
     * 4个参数的请求
     *
     * @param sParaTemp    请求参数数组
     * @param inputCharset 编码类型
     * @param gatewayUrl   访问url
     * @return 钱包处理结果
     * @throws Exception
     *
     */
    public static String sendRequest(Map<String, String> sParaTemp,
                                     String inputCharset, String gatewayUrl, int readTimeout) {
        try {
            // 待请求参数数组
            HttpProtocolHandler httpProtocolHandler = HttpProtocolHandler.getInstance();
            HttpRequest request = new HttpRequest(HttpResultType.STRING);
            // 设置编码集
            request.setCharset(inputCharset);
            request.setMethod(HttpRequest.METHOD_POST);
//            request.setConnectionRequestTimeout(connectionRequestTimeout);
//            request.setConnectionTimeout(connectionTimeout);
            request.setTimeout(readTimeout);

            request.setParameters(sParaTemp);
            request.setUrl(gatewayUrl);

            HttpResponse response = null;
            response = httpProtocolHandler.execute(request, null, null);
            if (response == null) {
                return null;
            }
            String strResult = null;
            strResult = response.getStringResult();
            return strResult;
        } catch (Exception e) {
            logger.error("请求http异常", e);
            throw new GateWayClientException(ErrorCode.SEND_GATEWAY_ERROR);
        }
    }
    /**
     * 上传文件和参数(类似表单提交)
     *
     * @param address
     * @param textMap
     * @param fileMap
     * @param headers
     * @return 返回response数据
     */
    @SuppressWarnings("rawtypes")
    public static String form(String address, Map<String, String> textMap, Map<String, FileInfo> fileMap, Map<String, String> headers) {
        String res = "";
        HttpURLConnection conn = null;
        // boundary就是request头和上传文件内容的分隔符
        String BOUNDARY = "--------------" + UUID.randomUUID().toString() + "-------------";
        try {
            URL url = new URL(address);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(30000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);

            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                out.write(strBuf.toString().getBytes());
            }
            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    FileInfo file = (FileInfo) entry.getValue();
                    if (file == null) {
                        continue;
                    }

                    // 没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
                    String contentType = file.getContentType();
                    if (contentType == null || "".equals(contentType.trim())) {
                        contentType = "application/octet-stream";
                    }

                    // 文件名不传默认随机数
                    String fileName = file.getFileName();
                    if (fileName == null || "".equals(fileName.trim())) {
                        fileName = UUID.randomUUID().toString();
                    }

                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + fileName + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
                    out.write(strBuf.toString().getBytes());
                    // 写入文件
                    byte[] bytes = file.getFile();
                    out.write(bytes, 0, bytes.length);
                }
            }
            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();
            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.forName("UTF-8")));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
            reader.close();
            reader = null;
        } catch (Exception e) {
            throw new RuntimeException(address + "发送POST请求出错" + e.getMessage());
        } finally {
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return res;
    }
}
