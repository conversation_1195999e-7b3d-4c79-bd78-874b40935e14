//package com.teyuntong.outer.export.service.service.biz.hbwj.config;
//
//import cn.hutool.core.lang.Pair;
//import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
//import com.teyuntong.outer.export.service.client.tripartite.bean.TripartiteTokenBean;
//import com.teyuntong.outer.export.service.service.biz.hbwj.client.HBWJClient;
//import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJBaseResult;
//import com.teyuntong.outer.export.service.service.common.property.MjSignProperty;
//import com.teyuntong.outer.export.service.service.common.utils.EccCryptoUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.*;
//import okio.Buffer;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Component;
//import retrofit2.Retrofit;
//import retrofit2.converter.jackson.JacksonConverterFactory;
//import retrofit2.converter.scalars.ScalarsConverterFactory;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.time.Duration;
//import java.util.*;
//
///**
// * <AUTHOR>
// * @since 2024/02/05 14:01
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//@EnableConfigurationProperties(MjSignProperty.class)
//public class HBWJRetrofitInterfaceSupplier implements RetrofitSupplier {
//
////    @Value("${third.hbwj.sign.private.key}")
//    private String signPrivateKey = "MEECAQAwEwYHKoZIzj0CAQYIKoZIzj0DAQcEJzAlAgEBBCDtKKfJvaM+E0rd32i9H6yWLdqnV6mnYgdGVhFpJsqEfA==";
//
////    @Value("${third.hbwj.crypto.public.key}")
//    private String cryptoPublicKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEGFgCAomtiGdKWmFYB5kR9WLS+AweZNWq9vYhA0ubG2s7uC/T+qLHaxj6DLglexDVxtsj9N2jflZkYUiQ19wA/Q==";
//
//    private final MjSignProperty mjSignProperty;
//
//    private final HBWJClient hBWJClient;
//
//    private static final List<String> encryptedFields = Arrays.asList("idCardNum","phone","password","idCardNo",
//            "idCard","qualificationCardNo","bankIdCardNo","legalPersonIdCard","bankPhone","adminPhone",
//            "financeContactTel","businessContactTel","userPhone","payeePhone","driverPhone","oldPayPwd","newPayPwd","confirmPayPwd");
//
//    @Resource
//    private StringRedisTemplate stringRedisTemplate;
//
//    private static final String REDIS_WJ_ACCESS_TOKEN_KEY = "WJ_ACCESS_TOKEN";
//    private final Interceptor signInterceptor = new Interceptor() {
//        @Override
//        public Response intercept(Chain chain) throws IOException {
//            Request request = chain.request();
//            RequestBody requestBody = request.body();
//            Buffer buffer = new Buffer();
//            requestBody.writeTo(buffer);
//            String bodyContent = buffer.readUtf8();
//            log.info("bodyContent"+bodyContent);
//            long nonce = EccCryptoUtil.generateNonce();
//            long timestamp = System.currentTimeMillis();
//
//            String newBodyStr = EccCryptoUtil.startHandleJsonFields(bodyContent, cryptoPublicKey, EccCryptoUtil.EncryptionTypeEnum.ENCRYPT, encryptedFields);
//            RequestBody newBody = RequestBody.create(newBodyStr.getBytes());
//            Buffer buffer1 = new Buffer();
//            newBody.writeTo(buffer1);
//            String bodyContent1 = buffer1.readUtf8();
//            log.info("bodyContent1"+bodyContent1);
//            Pair<String, String> pair = EccCryptoUtil.sign(signPrivateKey, cryptoPublicKey, nonce, timestamp, bodyContent, encryptedFields);
//
//            String token = getToken();
//            Request newRequest = new Request.Builder()
//                    .url(request.url())
//                    .method(request.method(), newBody)
//                    .header("Authorization", token)
//                    .header("Content-Type", "application/json")
//                    .header("X-Signature", pair.getKey())
//                    .header("X-Timestamp", String.valueOf(timestamp))
//                    .header("X-Nonce", String.valueOf(nonce))
//                    .build();
//
//            return chain.proceed(newRequest);
//        }
//
//        public String getToken() throws IOException {
//            String token = stringRedisTemplate.opsForValue().get(REDIS_WJ_ACCESS_TOKEN_KEY);
//            if (StringUtils.isNotEmpty(token)) {
//                return "Bearer " + token;
//            }
//            String clientCredentials = mjSignProperty.getClientId() + ":" + mjSignProperty.getClientSecret();
//            String encodedCredentials = "Basic " + Base64.getEncoder().encodeToString(clientCredentials.getBytes());
//            TripartiteTokenBean bean = new TripartiteTokenBean();
//            bean.setClient_id(mjSignProperty.getClientId());
//            bean.setClient_secret(mjSignProperty.getClientSecret());
//            retrofit2.Response<HBWJBaseResult<Object>> response = hBWJClient.getToken(encodedCredentials, "client_credentials",
//                    mjSignProperty.getClientId(), mjSignProperty.getClientSecret()).execute();
//            HBWJBaseResult<Object> result = response.body();
//            token = result.getAccess_token();
//            log.info("调用三方token【{}】", token);
//            stringRedisTemplate.opsForValue().set(REDIS_WJ_ACCESS_TOKEN_KEY, token, Duration.ofHours(2));
//            return "Bearer " + token;
//        }
//    };
//
//    @Override
//    public Retrofit getRetrofit() {
//        OkHttpClient okHttpClient = new OkHttpClient.Builder()
//                .connectTimeout(Duration.ofSeconds(5))
//                .readTimeout(Duration.ofSeconds(5))
//                .addInterceptor(signInterceptor)
//                .build();
//
//        return new Retrofit.Builder()
//                .baseUrl(mjSignProperty.getWjApiUrl())
//                .addConverterFactory(ScalarsConverterFactory.create())
//                .addConverterFactory(JacksonConverterFactory.create())
//                .client(okHttpClient)
//                .build();
//    }
//}
