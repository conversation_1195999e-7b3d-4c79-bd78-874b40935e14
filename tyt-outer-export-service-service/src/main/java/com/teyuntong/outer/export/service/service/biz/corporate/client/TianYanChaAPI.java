package com.teyuntong.outer.export.service.service.biz.corporate.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.client.common.old.bean.ResultMsgBean;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.CorporateBaseInfo;
import com.teyuntong.outer.export.service.service.biz.manbang.util.HttpClientFactory;
import com.teyuntong.outer.export.service.service.common.utils.MessageDigestUtil;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天眼查API
 * Created by duanwc on 2021/01/19.
 */
public class TianYanChaAPI {

    public static Logger logger = LoggerFactory.getLogger(TianYanChaAPI.class);

    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    private TianYanChaAPI() {
    }

    // 天眼查密钥
    private final static String APP_SECRET = "b721eaaa-f5b7-4a2b-9b71-3e273c5c9d61";

    /**
     * 获取天眼查企业基本信息（含企业联系方式）
     *
     * @param keyword 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
     * @return CorporateBaseInfo 企业基本信息，查询失败返回null
     * @see CorporateBaseInfo
     * 参考结构示例
     * {
     *      "result":{
     *          "name":"北京邦利德网络科技有限公司",
     *          ......
     *      },
     *      "error_code":200,
     *      "reason":"success"
     * }
     * 样例：
     * {
     *     "result":{
     *         "historyNames":null,
     *         "regStatus":"存续",
     *         "bondNum":null,
     *         "type":1,
     *         "bondName":null,
     *         "revokeReason":null,
     *         "property3":null,
     *         "usedBondName":null,
     *         "approvedTime":1605024000000,
     *         "id":2849640506,
     *         "orgNumber":"MA07P3HL8",
     *         "businessScope":"货物运输、货物专用运输（罐式）（不含危险化学品运输）、大型物件运输（一）；工程机械租赁；工程机械维修及配件销售、汽车维修及配件销售；保险代理*（依法须经批准的项目，经相关部门批准后方可开展经营活动）",
     *         "taxNumber":"91130501MA07P3HL8R",
     *         "regCapitalCurrency":"人民币",
     *         "tags":null,
     *         "phoneNumber":"0319-8852777",
     *         "district":"沙河市",
     *         "name":"邢台皓越货物运输有限公司",
     *         "percentileScore":7332,
     *         "industryAll":{
     *             "categoryMiddle":"道路货物运输",
     *             "categoryBig":"道路运输业",
     *             "category":"交通运输、仓储和邮政业",
     *             "categorySmall":"其他道路货物运输"
     *         },
     *         "isMicroEnt":1,
     *         "cancelDate":null,
     *         "regCapital":"1000万人民币",
     *         "city":"邢台市",
     *         "staffNumRange":"-",
     *         "industry":"道路运输业",
     *         "historyNameList":null,
     *         "updateTimes":1609484940000,
     *         "revokeDate":null,
     *         "legalPersonName":"唐小娟",
     *         "regNumber":"130501000022061",
     *         "creditCode":"91130501MA07P3HL8R",
     *         "fromTime":1458662400000,
     *         "socialStaffNum":0,
     *         "actualCapitalCurrency":"人民币",
     *         "alias":"皓越",
     *         "companyOrgType":"有限责任公司（自然人投资或控股的法人独资）",
     *         "cancelReason":null,
     *         "email":"<EMAIL>",
     *         "toTime":2405260800000,
     *         "actualCapital":"10万人民币",
     *         "estiblishTime":1458662400000,
     *         "regInstitute":"邢台经济开发区市场监督管理局",
     *         "regLocation":"河北省邢台经济开发区沙河城端庄村",
     *         "websiteList":null,
     *         "bondType":null,
     *         "base":"heb"
     *     },
     *     "reason":"ok",
     *     "error_code":0
     * }
     */
    public static ResultMsgBean<CorporateBaseInfo> getCorporateBaseInfo(String keyword) {
        String host = "http://open.api.tianyancha.com";
        // 请求path
        String path = "/services/open/ic/baseinfoV2/2.0";
        // 请求的query
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("keyword", keyword);
        // 参数
        List<NameValuePair> params = new ArrayList<>();
        for (Map.Entry<String, String> e : querys.entrySet()) {
            params.add(new BasicNameValuePair(e.getKey(), e.getValue()));
        }
        String url = host + path + "?" + URLEncodedUtils.format(params, StandardCharsets.UTF_8);
        HttpGet httpGet = new HttpGet(url);

        CloseableHttpResponse response = null;
        try {
            Map<String, String> headers = new HashMap<String, String>();
            //（必填）添加请求的密钥信息
            headers.put("Authorization", APP_SECRET);
            for (Map.Entry<String, String> e : headers.entrySet()) {
                httpGet.addHeader(e.getKey(), MessageDigestUtil.utf8ToIso88591(e.getValue()));
            }
            response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("HttpRequest [{}], HttpResponse [{}]", url, response);
                return null;
            }

            String result = EntityUtils.toString(response.getEntity());
            JSONObject res = JSON.parseObject(result);
            logger.info("关键字 [{}], 响应结果 [{}]", keyword, JSON.toJSONString(res, true));
            Integer ret = res.getInteger("error_code");
            String message = res.getString("reason");
            CorporateBaseInfo baseInfo = null;
            ResultMsgBean<CorporateBaseInfo> resultMsgBean = new ResultMsgBean<>();
            if (ret != 0) { // 失败
                resultMsgBean.setCode(ret);
                resultMsgBean.setMsg(message);
            } else { // 成功
                JSONObject resultJson = res.getJSONObject("result");
                baseInfo = JSON.toJavaObject(res.getJSONObject("result"), CorporateBaseInfo.class);
                JSONObject industryAll = resultJson.getJSONObject("industryAll");
                baseInfo.setCategory(industryAll.getString("category"));
                baseInfo.setCategoryMiddle(industryAll.getString("categoryMiddle"));
                baseInfo.setCategoryBig(industryAll.getString("categoryBig"));
                baseInfo.setCategorySmall(industryAll.getString("categorySmall"));
            }
            resultMsgBean.setData(baseInfo);
            return resultMsgBean;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 状态转换为解释说明，错误代码说明
     * | 代码    | 说明               |
     * | ------ | ------------------ |
     * | 0      | 请求成功            |
     * | 300000 | 无数据              |
     * | 300001 | 请求失败            |
     * | 300002 | 账号失效            |
     * | 300003 | 账号过期            |
     * | 300004 | 访问频率过快         |
     * | 300005 | 无权限访问此api      |
     * | 300006 | 余额不足            |
     * | 300007 | 剩余次数不足         |
     * | 300008 | 缺少必要参数         |
     * | 300009 | 账号信息有误         |
     * | 300010 | URL不存在           |
     * | 300011 | 此IP无权限访问此api  |
     *
     * @param code 错误代码
     * @return 说明
     */
    private static String code2Msg(int code) {
        String msg = "未知错误";
        switch (code) {
            case 0: {
                msg = "请求成功";
                break;
            }
            case 300000: {
                msg = "⽆数据";
                break;
            }
            case 300001: {
                msg = "请求失败";
                break;
            }
            case 300002: {
                msg = "账号失效";
                break;
            }
            case 300003: {
                msg = "账号过期";
                break;
            }
            case 300004: {
                msg = "访问频率过快";
                break;
            }
            case 300005: {
                msg = "⽆权限访问此api";
                break;
            }
            case 300006: {
                msg = "余额不⾜";
                break;
            }
            case 300007: {
                msg = "剩余次数不⾜";
                break;
            }
            case 300008: {
                msg = "缺少必要参数";
                break;
            }
            case 300009: {
                msg = "账号信息有误";
                break;
            }
            case 300010: {
                msg = "URL不存在";
                break;
            }
            case 300011: {
                msg = "此IP⽆权限访问此api";
                break;
            }
        }
        return msg;
    }

    public static void main(String[] args) {
        /**
         * 示例：
         */
        ResultMsgBean<CorporateBaseInfo> baseInfo = getCorporateBaseInfo("邢台皓越货物运输有限公司");
        System.out.println(JSON.toJSONString(baseInfo, true));

    }
}
