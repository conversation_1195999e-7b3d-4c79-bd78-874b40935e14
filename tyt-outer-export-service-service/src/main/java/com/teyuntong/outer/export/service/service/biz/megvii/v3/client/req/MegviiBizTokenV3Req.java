package com.teyuntong.outer.export.service.service.biz.megvii.v3.client.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:31
 */
@NoArgsConstructor
@Data
public class MegviiBizTokenV3Req {

    /**
     * 活体验证流程的选择，目前仅取以下值：
     * meglive：动作活体
     * still：静默活体
     * flash：炫彩活体，通过打光进行活体验证，炫彩活体相较于静默活体安全性更高，但通过率会略有降低
     * raw_image：不进行活体验证，仅使用上传的图片进行后续的比对
     */
    @JsonProperty("liveness_type")
    private String livenessType = "flash";

    /**
     * 确定本次比对为“KYC验证”或“人脸比对”。取值只为“1”或“0”，取其他值返回错误码400(BAD_ARGUMENTS)
     * 0：表示人脸比对，FaceID将使用客户自己提供的照片(参数image_ref[x])作为比对人脸照
     * 1：表示KYC验证，表示最终的用户照片将与参考照片进行比对。此外，如果没有“KYC验证”的权限，也不能设置此参数为“1”，否则返回错误码403
     * (AUTHORIZATION_ERROR:NO_DATA_SOURCE_PERMISSION)
     */
    @JsonProperty("comparison_type")
    private String comparisonType = "1";

    /**
     * 需要核实对象的姓名，使用UTF-8编码
     */
    @JsonProperty("idcard_name")
    @NotBlank(message = "idCardName 不能为空")
    private String idCardName;

    /**
     * 需要核实对象的证件号码，也就是一个18位长度的字符串
     */
    @JsonProperty("idcard_number")
    @NotBlank(message = "idCardNumber 不能为空")
    private String idCardNumber;

    /**
     * “默认为空”。客户业务流水号，建议设置为您的业务相关的流水串号并且唯一，
     * 会在return时原封不动的返回给您的服务器，以帮助您确认对应业务的归属。此字段不超过128字节
     */
    @JsonProperty("biz_no")
    private String bizNo;

    /**
     * 超时时间，示用户进入活体识别流程后的超时时间，若未在规定时间完成操作，则本次活体失败
     * 超时时间设定：单位 秒， ∈ [5, 60]
     * 动作活体时，设置每个动作的超时时间，默认 10
     * 静默活体时，设置照镜子的超时时间，默认 60
     */
    @JsonProperty("liveness_timeout")
    private String livenessTimeout;

    /**
     * 表示静默活体和炫彩活体是否进行闭眼检测，取值如下：
     * 0：默认值，不进行睁闭眼检测
     * 1：开启睁闭眼检测，若用户全程闭眼，则活体检测失败
     * 若设置为1，则整个请求的耗时会增加
     */
    @JsonProperty("eyes_close_detection")
    private String eyesCloseDetection;

    /**
     * 设置炫彩活体的超时时间，默认值为 120秒。表示用户进入活体识别流程后的超时时间，若未在规定时间完成操作，则本次活体失败
     * 超时时间设定：单位 秒， ∈ [60, 180]。若参数值超过限定范围，则返回BAD_ARGUMENTS错误
     */
    @JsonProperty("flash_liveness_timeout")
    private String flashLivenessTimeout;

    /**
     * 表示活体SDK端严格等级，取值如下：
     * 0：严格模式，默认值
     * 1：标准模式
     * 2：极速模式
     */
    @JsonProperty("liveness_level")
    private String livenessLevel;

    /**
     * 表示对比对结果的严格程度限制，请根据您的场景，选择安全规则，越严格，准确性要求越高，通过率也会相应下降
     * 1：宽松
     * 2：常规(默认值)
     * 3：严格
     * 4：非常严格
     */
    @JsonProperty("security_level")
    private String securityLevel;

    public Map<String, String> toMap(ObjectMapper mapper) throws IOException {
        return mapper.readValue(mapper.writeValueAsBytes(this),
                new TypeReference<Map<String, String>>() {
                });
    }
}
