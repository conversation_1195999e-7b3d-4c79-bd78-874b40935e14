package com.teyuntong.outer.export.service.service.biz.cticloud.task.client;

import com.teyuntong.outer.export.service.client.cticloud.task.vo.ImportTaskTelRequest;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.ImportTaskTelResponse;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.TaskQueryResult;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.TaskCreateResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @since 2023/12/18 10:11
 */
public interface TaskApi {

    /**
     * 获取任务列表接口
     *
     * @param name      可选	任务名称	需进行UTF-8格式的URLEncode编码
     * @param type      可选	任务类型	1.转呼叫组 2.转IVR
     * @param status    可选	任务状态	0初始 1运行中 2暂停 3结束
     * @param autoStart 可选	是否自动开始	0.不自动 1.自动
     * @param autoStop  可选	是否自动结束	0.不自动 1.自动
     * @param timeType  可选	时间过滤条件	1.任务启动时间 2.任务结束时间 3.任务创建时间
     * @param startTime 可选	起始时间点	取值说明："2019-10-11 00:00:00"
     * @param endTime   可选	终止时间点	取值说明："2019-10-11 23:59:59"
     * @return result
     */
    @GET("task/query")
    Call<CticloudResp<TaskQueryResult>> queryTask(@Query("name") String name,
                                                  @Query("type") Integer type,
                                                  @Query("status") Integer status,
                                                  @Query("autoStart") Integer autoStart,
                                                  @Query("autoStop") Integer autoStop,
                                                  @Query("timeType") Integer timeType,
                                                  @Query("startTime") String startTime,
                                                  @Query("endTime") String endTime);

    /**
     * 任务号码导入接口
     *
     * @return result
     */
    @POST("task/importTaskTel")
    Call<CticloudResp<ImportTaskTelResponse>> importTaskTel(@Body ImportTaskTelRequest importTaskTelRequest);

    @GET("task/create")
    Call<CticloudResp<TaskCreateResp>> taskCreate(@Query("name") String name, @Query("type") String type, @Query("userFields") String userFields, @Query("templateName") String templateName);

    @GET("task/start")
    Call<CticloudResp<Object>> taskStart(@Query("taskId") String taskId);

    @GET("task/delete")
    Call<CticloudResp<Object>> delete(@Query("taskId") String taskId);

}
