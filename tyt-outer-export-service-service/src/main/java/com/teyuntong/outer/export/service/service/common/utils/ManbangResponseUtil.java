package com.teyuntong.outer.export.service.service.common.utils;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.client.model.CustomErrorCode;
import com.teyuntong.outer.export.service.client.model.ResponseCode;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/5/11 11:18
 */
@Slf4j
public class ManbangResponseUtil {

    /**
     * T (成功) F（失败）
     */
    public static final String SUCCESS_CODE = "T";
    public static final String FAIL_CODE = "F";

    /**
     * 校验返回结果
     * @param resp resp
     * @return boolean
     */
    public static ResponseCode getErrorCode(BaseResponse resp) {

        String errorMessage = null;
        String errorCode = null;

        if (resp != null){
            errorMessage = resp.getErrorMessage();
            errorCode = resp.getErrorCode();
        }

        if(StringUtils.isBlank(errorMessage)){
            errorMessage = OuterExportErrorCode.MANBANG_ERROR.getMsg();
        }
        ResponseCode responseCode = CustomErrorCode.easyInfo(OuterExportErrorCode.MANBANG_ERROR, errorMessage);
        return responseCode;
    }

    /**
     * 校验返回结果
     * @param resp resp
     * @return boolean
     */
    public static boolean check(BaseResponse resp, boolean throwError) {

        String jsonString = JSON.toJSONString(resp);
        log.info("ManbangResponse : {}", jsonString);

        if (resp != null){
            String isSuccess = resp.getIsSuccess();
            if(StringUtils.equals(isSuccess, SUCCESS_CODE)){
                return true;
            }
        }

        if(throwError) {
            ResponseCode errorCode = getErrorCode(resp);
            throw BusinessException.createException(errorCode);
        }

        return false;
    }

    /**
     * 校验返回结果
     * @param resp resp
     * @return boolean
     */
    public static boolean check(BaseResponse resp) {
        return check(resp, true);
    }

}
