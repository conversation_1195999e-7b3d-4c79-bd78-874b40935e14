package com.teyuntong.outer.export.service.service.biz.cticloud.axb.client;

import lombok.Data;

@Data
public class TaskCreateResp {

    private String id;
    private String enterpriseId;
    private String name;
    private String description;
    private String status;
    private String type;
    private String startTime;
    private String overTime;
    private String customerTimeout;
    private String agentTimeout;
    private String cnos;
    private String ivrId;
    private String autoStart;
    private String autoStartDay;
    private String autoStartTime;
    private String autoStop;
    private String autoStopDay;
    private String autoStopTime;
    private String concurrency;
    private String quotiety;
    private String wrapup;
    private String isRandom;
    private String customerClids;
    private String maxWaitTime;
    private String answerRate;
    private String predictAdjust;
    private int autoComplete;
    private String customerMoh;
    private String createTime;
    private String userFields;


}