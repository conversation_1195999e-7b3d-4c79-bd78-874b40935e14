package com.teyuntong.outer.export.service.service.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 临时测试用的 message bean
 *
 * <AUTHOR>
 * @since 2023/11/08 13:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TmpMessageBean {

    /**
     * id
     */
    private Long id;
    /**
     * 内容
     */
    private String content;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 顺序
     */
    private Integer order;

}
