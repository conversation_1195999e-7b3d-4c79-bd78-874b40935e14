package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlTruckService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 车辆相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlTruckServiceImpl implements XhlTruckService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> saveTruck(XhlTruckEntity truckEntity) throws Exception {
        log.info("新增车辆-请求参数，truckEntity={}", truckEntity);
        if (!Objects.isNull(truckEntity)) {
            truckEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveTruck(truckEntity).execute();
        log.info("新增车辆-返回结果，truckEntity={}，响应参数：{} ", truckEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateTruck(XhlTruckEntity truckEntity) throws Exception {
        log.info("修改车辆-请求参数，truckEntity={}", truckEntity);
        if (!Objects.isNull(truckEntity)) {
            truckEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateTruck(truckEntity).execute();
        log.info("修改车辆-返回结果，truckEntity={}，响应参数：{} ", truckEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryTruck(String plateNumber) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询车辆-请求参数，appId={}，plateNumber={}", appId, plateNumber);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryTruck(appId, plateNumber).execute();
        log.info("查询车辆-返回结果，appId={}，plateNumber={}，响应参数：{} ", appId, plateNumber, response.body());
        return response.body();
    }
}
