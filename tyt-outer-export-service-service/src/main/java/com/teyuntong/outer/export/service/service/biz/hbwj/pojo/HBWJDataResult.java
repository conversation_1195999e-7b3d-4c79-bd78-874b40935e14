package com.teyuntong.outer.export.service.service.biz.hbwj.pojo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:39
 */
@NoArgsConstructor
@Data
public class HBWJDataResult<T> {


    private Integer code;
    private String info;
    private T data;
    private Integer biz_code;
    private String biz_msg;
    private String msg;

    private String result_id;
    private String message;
    public static boolean checkSuccess(HBWJDataResult<?> authBaseResult){
        return (authBaseResult != null);
    }

}
