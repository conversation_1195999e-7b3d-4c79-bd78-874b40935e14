package com.teyuntong.outer.export.service.service.biz.zj.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2024/11/05 14:55
 */
@Data
public class ZJVehicleLocation {

    // 车牌号
    @JsonProperty("vno")
    private String vno;

    // 状态码，业务查询状态：详见附录 A
    @JsonProperty("code")
    private int code;

    // 车辆最后定位经度，格式：1/600000.0（WGS84 坐标系）
    @JsonProperty("lon")
    private int lon;

    // 车辆最后定位纬度，格式：1/600000.0（WGS84 坐标系）
    @JsonProperty("lat")
    private int lat;

    // 车辆地理位置名称
    @JsonProperty("adr")
    private String adr;

    // 车辆定位时间，时间戳
    @JsonProperty("utc")
    private long utc;

    // 速度，单位 km/h
    @JsonProperty("spd")
    private double spd;

    // 方向，正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小于 180：东南，等于 180：正南，大于 180 且小于 270：西南，等于 270：正西，大于 270 且小于等于 359：西北，其他：未知
    @JsonProperty("drc")
    private int drc;

    // 省
    @JsonProperty("province")
    private String province;

    // 市
    @JsonProperty("city")
    private String city;

    // 县
    @JsonProperty("country")
    private String country;

    // 里程，单位：km（车机上报点自带里程）
    @JsonProperty("mil")
    private double mil;

    // 离线状态，true:离线，false:在线  首车独有
    @JsonProperty("offlineState")
    private Integer offlineState;

    // 离线时长，单位：分钟  首车独有
    @JsonProperty("offlineTime")
    private int offlineTime;

    // 车辆已行驶距离，单位：km   首车独有
    @JsonProperty("runDistance")
    private double runDistance;

    // 剩余运距，单位：km  首车独有
    @JsonProperty("remainDistance")
    private double remainDistance;

    // 预计到达时间，时间戳   首车独有
    @JsonProperty("estimateArriveTime")
    private long estimateArriveTime;


    private String originalLon;

    private String originalLat;

    private String formattedUtc;


    public String getOriginalLon() {
        return String.format("%.6f", (double) lon / 600000);
    }

    public String getOriginalLat() {
        return String.format("%.6f", (double) lat / 600000);
    }

    public String getFormattedUtc() {
        if (utc <= 0) {
            return null; // 时间戳无效，返回 null
        }

        Instant instant = Instant.ofEpochMilli(utc);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return formatter.format(zonedDateTime);
    }

}
