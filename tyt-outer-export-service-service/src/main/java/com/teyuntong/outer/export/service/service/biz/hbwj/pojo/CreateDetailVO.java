package com.teyuntong.outer.export.service.service.biz.hbwj.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询企业信息VO
 *
 * <AUTHOR>
 * @since 2024-7-18 11:29:40
 */
@Data
public class CreateDetailVO implements Serializable {

    /**
     * id
     */
    private Long id;
    /**
     * 企业管理员ID
     */
    private String adminUserId;
    /**
     * 用户会员代码
     */
    private String userCode;
    /**
     * 用户中心返回的企业客户id
     */
    private String userCenterCompanyId;
    /**
     * 用户中心返回的组织id
     */
    private String orgId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 企业编号
     */
    private String companyCode;
    /**
     * 统一社会信用代码
     */
    private String unifiedCreditcCode;
    /**
     * 营业期限起
     */
    private String tradeTimeStart;
    /**
     * 营业期限止
     */
    private String tradeTimeEnd;
    /**
     * 注册地址
     */
    private String registerAddress;
    /**
     * 办公地址
     */
    private String workAddress;
    /**
     * 办公地址2
     */
    private String otherWorkAddress;
    /**
     * 所属集团
     */
    private String affiliatedGroup;
    /**
     * 行业类型
     */
    private Integer industryType;
    /**
     * 企业性质
     */
    private Integer companyNature;
    /**
     * 企业类型
     */
    private Integer companyType;
    /**
     * 应用配置id
     */
    private String applicationConfigId;
    /**
     * 业务代表id
     */
    private Long businessRepresentativeId;
    /**
     * 业务代表类型
     * 1,"个人"
     * 2,"企业"
     * (See: 承运类型
     * PERSONAL 个人
     * ENTERPRISE 企业)
     */
    private String businessRepresentativeType;
    /**
     * 业务归属(部门Id)
     */
    private Long partnershipId;
    /**
     * 业务规模
     */
    private String businessScale;
    /**
     * 法人姓名
     */
    private String legalPersonName;
    /**
     * 法人身份证号
     */
    private String legalPersonIdCard;
    /**
     * 法人身份证有效期起
     */
    private String idCardValidityStart;
    /**
     * 法人身份证有效期止
     */
    private String idCardValidityEnd;
    /**
     * 财务联系人
     */
    private String financeContact;
    /**
     * 财务手机号
     */
    private String financeContactTel;
    /**
     * 业务联系人
     */
    private String businessContact;
    /**
     * 业务员手机号
     */
    private String businessContactTel;
    /**
     * 认证状态 0未认证 1认证中 2已认证
     */
    private String authStatus;
    /**
     * 启用状态 1启用 0禁用
     * (See: 合作状态
     * ENABLE 启用
     * DISABLE 禁用)
     */
    private String enableStatus;
    /**
     * 身份id ，用逗号分隔
     */
    private String identityIds;

}
