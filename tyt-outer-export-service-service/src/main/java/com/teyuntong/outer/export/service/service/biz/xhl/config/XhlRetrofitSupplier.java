package com.teyuntong.outer.export.service.service.biz.xhl.config;

import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.util.XhlSignUtil;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.RequiredArgsConstructor;
import okhttp3.*;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 翔和翎 Retrofit Supplier
 *
 * <AUTHOR>
 * @since 2025/01/13 14:01
 */
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(XhlProperties.class)
public class XhlRetrofitSupplier implements RetrofitSupplier {

    private final XhlProperties xhlProperties;

    private static final String MEDIA_TYPE_URLENCODED = "application/x-www-form-urlencoded";

    private static final String CONTENT_TYPE_GET = "text/plain;charset=UTF-8";

    private final Interceptor signInterceptor = new Interceptor() {
        @NotNull
        @Override
        public Response intercept(@NotNull Chain chain) throws IOException {
            Request request = chain.request();
            // 获取请求URL
            String url = request.url().toString();
            String path = XhlSignUtil.getRelUrl(url);
            // 获取时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());

            String bodyString = "";
            Request newRequest = null;
            if (request.method().equalsIgnoreCase("POST")) {
                RequestBody requestBody = request.body();
                if (requestBody != null) {
                    Buffer buffer = new Buffer();
                    requestBody.writeTo(buffer);
                    bodyString = buffer.readUtf8();
                }
                // 构建签名参数
                String sign = XhlSignUtil.getSign(xhlProperties.getAppId(), xhlProperties.getApiKey(), path, timestamp, bodyString);
                StringJoiner stringJoiner = new StringJoiner("&");
                stringJoiner.add("appId=" + URLEncoder.encode(xhlProperties.getAppId(), "UTF-8"))
                        .add("timestamp=" + URLEncoder.encode(timestamp, "UTF-8"))
                        .add("sign=" + URLEncoder.encode(sign, "UTF-8"))
                        .add("body=" + URLEncoder.encode(bodyString, "UTF-8"));

                RequestBody newRequestBody = RequestBody.create(stringJoiner.toString(), MediaType.parse(MEDIA_TYPE_URLENCODED));
                newRequest = request.newBuilder()
                        .header("Content-Type", MEDIA_TYPE_URLENCODED)
                        .header("timestamp", timestamp)
                        .method(request.method(), newRequestBody)
                        .build();
            } else if (request.method().equalsIgnoreCase("GET")) {
                Map<String, String> queryParams = request.url().queryParameterNames().stream()
                        .collect(Collectors.toMap(
                                name -> name,
                                request.url()::queryParameter
                        ));
                bodyString = XhlSignUtil.getBodyString(queryParams);
                url = url.substring(0, url.indexOf("?"));
                // 构建签名参数
                String sign = XhlSignUtil.getSign(xhlProperties.getAppId(), xhlProperties.getApiKey(), path, timestamp, bodyString);
                StringJoiner stringJoiner = new StringJoiner("&");
                stringJoiner.add("appId=" + URLEncoder.encode(xhlProperties.getAppId(), "UTF-8"))
                        .add("timestamp=" + URLEncoder.encode(timestamp, "UTF-8"))
                        .add("sign=" + URLEncoder.encode(sign, "UTF-8"))
                        .add("body=" + URLEncoder.encode(bodyString, "UTF-8"));
                String requestUrl = String.format("%s?%s", url, stringJoiner);
                newRequest = request.newBuilder()
                        .url(requestUrl)
                        .header("timestamp", timestamp)
                        .header("Content-Type", CONTENT_TYPE_GET)
                        .build();
            }
            return chain.proceed(newRequest);
        }
    };

    @Override
    public Retrofit getRetrofit() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(signInterceptor)
                .connectTimeout(Duration.ofMillis(150000))
                .readTimeout(Duration.ofMillis(150000))
                .build();

        return new Retrofit.Builder()
                .baseUrl(xhlProperties.getApiUrl())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create())
                .client(okHttpClient)
                .build();
    }
}
