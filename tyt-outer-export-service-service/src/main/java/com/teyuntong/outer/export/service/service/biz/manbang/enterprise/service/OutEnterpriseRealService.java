package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service;

import com.teyuntong.outer.export.service.client.manbang.api.enterprise.bean.CompanyRealNameReq;
import com.teyuntong.outer.export.service.client.manbang.vo.EnterpriseRealNameStatusVo;
import com.teyuntong.outer.export.service.client.model.FileDataInfo;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/26 14:30
 */
public interface OutEnterpriseRealService {

    String uploadFile(FileDataInfo fileDataInfo);

    void companyRealName(CompanyRealNameReq realNameReq);

    EnterpriseRealNameStatusVo getRealNameStatus(String identityNo);

}
