package com.teyuntong.outer.export.service.service.biz.esign.util;

import com.teyuntong.outer.export.service.client.esign.enums.sign.*;
import com.timevale.esign.sdk.tech.bean.seal.OrganizeTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.PersonTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.SealColor;
import com.timevale.esign.sdk.tech.impl.constants.LegalAreaType;
import com.timevale.esign.sdk.tech.impl.constants.OrganRegType;
import com.timevale.esign.sdk.tech.impl.constants.SignType;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/25 13:08
 */
public class EsignEnumUtil {

    public static SealColor getSealColor(SealColorEnum colorEnumReq){

        if(colorEnumReq == null){
            return SealColor.RED;
        }
        String colorName = colorEnumReq.name();

        SealColor sealColor = SealColor.valueOf(colorName);
        return sealColor;
    }

    public static OrganizeTemplateType getOrganizeTemplateType(OrganizeTemplateTypeEnum templateTypeReq){
        if(templateTypeReq == null){
            return OrganizeTemplateType.STAR;
        }
        String typeName = templateTypeReq.name();

        OrganizeTemplateType templateType = OrganizeTemplateType.valueOf(typeName);
        return templateType;
    }

    public static PersonTemplateType getPersonTemplateType(PersonTemplateTypeEnum templateTypeReq){
        if(templateTypeReq == null){
            return PersonTemplateType.SQUARE;
        }
        String typeName = templateTypeReq.name();
        PersonTemplateType templateType = PersonTemplateType.valueOf(typeName);
        return templateType;
    }


    public static OrganRegType getOrganRegType(OrganRegTypeEnum regTypeReq){
        if(regTypeReq == null){
            return OrganRegType.MERGE;
        }
        String regTypeName = regTypeReq.name();
        OrganRegType organRegType = OrganRegType.valueOf(regTypeName);
        return organRegType;
    }

    public static SignType getSignType(SignSealTypeEnum sealTypeReq){
        if(sealTypeReq == null){
            return SignType.Key;
        }

        String sealCode = sealTypeReq.getCode();
        SignType signType = SignType.valueOf(sealCode);
        return signType;
    }

    public static LegalAreaType getLegalAreaType(LegalAreaTypeEnum areaTypeReq){
        if(areaTypeReq == null){
            return LegalAreaType.MAINLAND;
        }

        String areaName = areaTypeReq.name();
        LegalAreaType areaType = LegalAreaType.valueOf(areaName);
        return areaType;
    }

}
