package com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.CorporateBaseInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("common")
public interface CorporateBaseInfoMapper extends BaseMapper<CorporateBaseInfo> {

    /**
     * 忽略插入工商企业基本信息，手机号唯一索引
     *
     * @param corporateBaseInfo CorporateBaseInfo
     * @return 插入的条数
     */
    int addCorporateBaseInfo(CorporateBaseInfo corporateBaseInfo);

    /**
     * 根据企业名称获取企业基本信息
     *
     * @param name
     * @return
     */
    CorporateBaseInfo getCorporateBaseInfo(String name);

}