package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2024/02/05 10:29
 */
@Data
@ConfigurationProperties("wj-sign")
public class MjSignProperty {

    private String clientId;

    private String clientSecret;

    private String wjApiUrl;

    private String wjUrl;

}
