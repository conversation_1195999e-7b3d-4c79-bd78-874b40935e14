package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2024/12/17 14:02
 */
@Data
@ConfigurationProperties(prefix = "mb.location")
public class LocationProperties {



    /**
     * 定位  145123
     */
    private String lastLocationServiceId;

    /**
     * 轨迹 145036
     */
    private String traceServiceId;
}
