//package com.teyuntong.outer.export.service.service.rpc.isp;
//
//import com.teyuntong.infra.common.definition.bean.WebResult;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeRequest;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeResponse;
//import com.teyuntong.outer.export.service.client.invoice.hbwj.service.ThirdInvoiceService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Map;
//
///**
// * 开票-湖北我家开放接口服务实现类
// *
// * <AUTHOR>
// * @since 2024/07/17 13:21
// */
//@Slf4j
//@RestController
//@RequiredArgsConstructor
//public class ThirdInvoiceServiceImpl implements ThirdInvoiceService {
//
//
//    @Autowired
//    private Map<String, AbstractInvoiceServiceProvider> providerMap;
//
//    /**
//     * 创建运单接口
//     *
//     * <AUTHOR>
//     * @param createWaybillThreeRequest 创建运单请求对象
//     * @return CreateWaybillThreeResponse 创建运单返回对象
//     */
//    @Override
//    public WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception {
//        String provider = "wj";
//        return providerMap.get(provider+"InvoiceServiceProviderImpl").addWaybillThree(createWaybillThreeRequest);
//
//    }
//}
