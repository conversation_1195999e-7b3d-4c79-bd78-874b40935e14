package com.teyuntong.outer.export.service.service.biz.xhl.pojo;

import lombok.Data;

/**
 * 翔和翎 发票实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlInvoiceEntity {

    /**
     * appId，必填
     */
    private String appId;

    /**
     * 公司名称，必填
     */
    private String companyName;

    /**
     * 发票申请单号，必填
     */
    private String invoiceApplyNo;

    /**
     * 三方运单号，多个逗号隔开
     */
    private String tpWaybillNos;

    /**
     * 开票方公司名称
     */
    private String kpCustomerName;

    /**
     * 开票方统一社会代码
     */
    private String kpTaxNo;

    /**
     * 开票方公司地址
     */
    private String kpCustomerAddress;

    /**
     * 开票方电话
     */
    private String kpCustomerPhone;

    /**
     * 开票方开户银行
     */
    private String kpBankName;

    /**
     * 开票方银行账户
     */
    private String kpBankAccountNo;

    /**
     * 发票类型，1:数电票，2:纸质发票
     */
    private String type;

    /**
     * 开票清单，1:需要清单，2:不需要清单
     */
    private String hasList;

    /**
     * 开票单位（吨、方、件等）
     */
    private String unit;

    /**
     * 数量
     */
    private String num;

    /**
     * 规格类型
     */
    private String norms;

    /**
     * 发票清单地址
     */
    private String invoiceListUrl;

    /**
     * 发票备注
     */
    private String remark;

    /**
     * 发票申请状态，1:待开票，2:开票中，3:开票成功，4:开票取消
     */
    private String invoiceStatus;

    /**
     * 审核状态，1待审核，2已审核，3审核不过
     */
    private String checkFlag;

    /**
     * 审核失败原因
     */
    private String checkLog;

    /**
     * 创建时间，时间戳格式
     */
    private String createTime;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票费率
     */
    private String invoiceRate;

    /**
     * 价税合计
     */
    private String totalMoney;

    /**
     * 开票时间， yyyy-MM-dd格式
     */
    private String invoiceTime;

    /**
     * 发票地址
     */
    private String invoiceUrl;
}
