package com.teyuntong.outer.export.service.service.biz.manbang.util;

import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpContext;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.UnknownHostException;
import java.util.logging.Logger;

public class HttpClientFactory {


    private static final Logger logger = Logger.getLogger(HttpClientFactory.class.getName());
    private static final Integer DEFAULT_MAX_RETRY_COUNT = 3;
    private static final Integer DEFAULT_HTTP_TIMEOUT_SECONDS = 5;
    private static final Integer DEFAULT_CONN_PER_ROUTE = 2;

    public static CloseableHttpClient getHttpClientWithRetry() {
        return getHttpClientWithRetry(DEFAULT_MAX_RETRY_COUNT);
    }

    public static CloseableHttpClient getHttpClientWithRetry(final Integer maxRetryCount) {
        return getHttpClientWithRetry(DEFAULT_CONN_PER_ROUTE, maxRetryCount);
    }

    public static CloseableHttpClient getHttpClientWithRetry(final Integer MaxConnPerRoute, final Integer maxRetryCount) {
        return HttpClients.custom()
                .setDefaultRequestConfig(getRequestConfig(DEFAULT_HTTP_TIMEOUT_SECONDS))
                .setRetryHandler(getHttpRequestRetryHandler(maxRetryCount)).setMaxConnPerRoute(MaxConnPerRoute)
                .build();
    }

    private static HttpRequestRetryHandler getHttpRequestRetryHandler(final Integer maxRetryCount) {
        HttpRequestRetryHandler myRetryHandler = new HttpRequestRetryHandler() {
            public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
                if (executionCount > maxRetryCount) {
                    // Do not retry if over max retry count
                    logger.info("Over the max retry count. Skip retrying.");
                    return false;
                }
                if (exception instanceof InterruptedIOException) {
                    // Timeout
                    logger.info("No retry." + exception.getMessage());
                    return false;
                }
                if (exception instanceof UnknownHostException) {
                    // Unknown host
                    logger.info("No retry." + exception.getMessage());
                    return false;
                }
                if (exception instanceof ConnectTimeoutException) {
                    // Connection refused
                    logger.info("Need retry." + exception.getMessage());
                    return true;
                }
                if (exception instanceof SSLException) {
                    // SSL handshake exception
                    logger.info("No retry." + exception.getMessage());
                    return false;
                }
                if (exception instanceof NoHttpResponseException) {
                    // Stale connection between client and LB.
                    logger.info("Need retry." + exception.getMessage());
                    return true;
                }

                HttpClientContext clientContext = HttpClientContext.adapt(context);
                HttpRequest request = clientContext.getRequest();
                boolean idempotent = !(request instanceof HttpEntityEnclosingRequest);
                if (idempotent) {
                    // Retry if the request is considered idempotent
                    return true;
                }
                return false;
            }
        };
        return myRetryHandler;
    }

    private static RequestConfig getRequestConfig(Integer timeout) {
        return RequestConfig.custom()
                .setSocketTimeout(timeout * 1000)
                .setConnectTimeout(timeout * 1000)
                .build();
    }
}

