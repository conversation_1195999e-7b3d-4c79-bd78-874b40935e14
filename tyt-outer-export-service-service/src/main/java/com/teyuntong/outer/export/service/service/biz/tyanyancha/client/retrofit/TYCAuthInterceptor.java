package com.teyuntong.outer.export.service.service.biz.tyanyancha.client.retrofit;

import lombok.RequiredArgsConstructor;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/01/11 13:32
 */
@Component
@EnableConfigurationProperties(TYCProperties.class)
@RequiredArgsConstructor
public class TYCAuthInterceptor implements Interceptor {

    private static final String Auth_HEADER_KEY = "Authorization";

    private final TYCProperties tycProperties;

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request request = chain.request();
        request = request.newBuilder()
                .header(Auth_HEADER_KEY, tycProperties.getToken()).build();

        return chain.proceed(request);
    }

}
