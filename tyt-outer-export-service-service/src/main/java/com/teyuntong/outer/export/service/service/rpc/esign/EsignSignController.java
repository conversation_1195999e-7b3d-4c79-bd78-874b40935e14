package com.teyuntong.outer.export.service.service.rpc.esign;


import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.esign.vo.sign.*;
import com.teyuntong.outer.export.service.service.biz.esign.sign.service.EsignSignService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 易签宝-电子签名
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 *
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/esignSign")
public class EsignSignController {

    private final EsignSignService esignSignService;

    /**
     * 本地PDF模板文件流填充
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/sed7zt">官方文档链接</a>
     */
    @PostMapping("/pdf/create")
    public WebResult<CreatePdfVO> createPdf(@RequestBody @Validated CreatePdfReq req) throws IOException {
        CreatePdfVO createPdfVO = esignSignService.createPdf(req);

        return WebResult.success(createPdfVO);
    }

    /**
     * 创建企业签署账户
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/nogqr7">官方文档链接</a>
     */
    @PostMapping("/account/add")
    public WebResult<AddAccountVO> addAccount(@RequestBody @Validated AddAccountReq req) throws IOException {
        AddAccountVO createPdfVO = esignSignService.addAccount(req);

        return WebResult.success(createPdfVO);
    }

    /**
     * 创建个人签署账户
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/ndxlx8">官方文档链接</a>
     */
    @PostMapping("/addPersonAccount")
    public WebResult<AddAccountVO> addPersonAccount(@RequestBody @Validated AddPersonAccountReq req) {
        AddAccountVO createPdfVO = esignSignService.addPersonAccount(req);

        return WebResult.success(createPdfVO);
    }

    /**
     * 创建企业模板印章
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/qg2s0z">官方文档链接</a>
     */
    @PostMapping("/template/seal/add")
    public WebResult<TemplateSealVO> addTemplateSeal(@RequestBody @Validated TemplateSealReq req) throws IOException {
        TemplateSealVO createPdfVO = esignSignService.addTemplateSeal(req);

        return WebResult.success(createPdfVO);
    }

    /**
     * 创建个人模板印章
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/meryvg">官方文档链接</a>
     */
    @PostMapping("/addPersonSeal")
    public WebResult<TemplateSealVO> addPersonSeal(@RequestBody @Validated PersonTemplateSealReq req) {
        TemplateSealVO createPdfVO = esignSignService.addPersonSeal(req);

        return WebResult.success(createPdfVO);
    }

    /**
     * 指定手机发送签署短信验证码
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/uv05t5">官方文档链接</a>
     */
    @PostMapping("/mobileCode/send")
    public WebResult<?> sendSignMobileCode3rd(@RequestBody @Validated SendSignMobileCode3rdReq req) throws IOException {
        esignSignService.sendSignMobileCode3rd(req);

        return WebResult.success();
    }

    /**
     * 平台用户PDF文件流签署（指定手机号短信验证）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/gg9yea">官方文档链接</a>
     */
    @PostMapping("/localPdf/saveSign3rd")
    public WebResult<LocalSafeSignPDF3rdVO> localSafeSignPDF3rd(@RequestBody @Validated LocalSafeSignPDF3rdReq req) throws IOException {
        LocalSafeSignPDF3rdVO localSafeSignPDF3rdVO = esignSignService.localSafeSignPDF3rd(req);
        return WebResult.success(localSafeSignPDF3rdVO);
    }

    /**
     * 平台用户PDF文件多位置文件流签署（指定手机短信验证）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/defebg">官方文档链接</a>
     */
    @PostMapping("/localPdf/multiPositionSign3rd")
    public WebResult<LocalSafeSignPDF3rdVO> multiPositionSign3rd(@RequestBody @Validated MultiPositionSignPDF3rdReq req) {
        LocalSafeSignPDF3rdVO localSafeSignPDF3rdVO = esignSignService.multiPositionSign3rd(req);
        return WebResult.success(localSafeSignPDF3rdVO);
    }

    /**
     * 平台自身PDF文件流签署（印章标识）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/zk13ay">官方文档链接</a>
     */
    @PostMapping("/localPdf/sealIdSign")
    public WebResult<LocalSignPdfVO> sealIdSign(@RequestBody @Validated LocalSignPdfReq req) throws IOException {
        LocalSignPdfVO localSignPdfVO = esignSignService.sealIdSign(req);
        return WebResult.success(localSignPdfVO);
    }

    /**
     * PDF文档验签（文件流）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/ruk0br">官方文档链接</a>
     */
    @PostMapping("/localPdf/verify")
    public WebResult<LocalVerifyPdfVO> localSignPdf(@RequestBody @Validated LocalVerifyPdfReq req) throws IOException {
        LocalVerifyPdfVO localSignPdfVO = esignSignService.localVerifyPdf(req);
        return WebResult.success(localSignPdfVO);
    }

    /**
     * 平台用户/企业 PDF文件签署(无意愿)
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/wq86pt">官方文档链接</a>
     */
    @PostMapping("/silentUserSealSign")
    public WebResult<LocalSafeSignPDF3rdVO> silentUserSealSign(@RequestBody @Validated SilentUserSealSignReq req) {
        LocalSafeSignPDF3rdVO localSafeSignPDF3rdVO = esignSignService.silentUserSealSign(req);
        return WebResult.success(localSafeSignPDF3rdVO);
    }

}
