package com.teyuntong.outer.export.service.service.biz.megvii.v5.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.service.biz.megvii.FaceIdRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.megvii.FaceIdSignInterceptor;
import com.teyuntong.outer.export.service.client.megvii.vo.v5.BizTokenV5Resp;
import com.teyuntong.outer.export.service.client.megvii.vo.v5.LivenessResp;
import com.teyuntong.outer.export.service.client.megvii.vo.v5.VerifyResp;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:16
 */
@RetrofitClient(value = FaceIdRetrofitSupplier.class, path = "faceid/v5/sdk/")
@UseInterceptor(FaceIdSignInterceptor.class)
@UseInterceptor(LogInfoInterceptor.class)
public interface FaceIdV5Api {

    /**
     * 获取 token
     * <p>
     * <a href="https://faceid.com/document/faceid-guide-docs/v5_get_biztoken">接口文档地址</a>
     */
    @POST("get_biz_token")
    @FormUrlEncoded
    Call<BizTokenV5Resp> getBizToken(@Field("liveness_id") String livenessId);

    /**
     * 活体验证（仅活体不比对）
     * <p>
     * <a href="https://faceid.com/document/faceid-guide-docs/adv_liveness">接口文档地址</a>
     */
    @POST("liveness")
    @FormUrlEncoded
    Call<LivenessResp> liveness(@FieldMap Map<String, Object> params);

    /**
     * 比对认证
     * <p>
     * <a href="https://faceid.com/document/faceid-guide-docs/v5_get_result">接口文档地址</a>
     */
    @POST("verify")
    @FormUrlEncoded
    Call<VerifyResp> verify(@FieldMap Map<String, Object> params);
}
