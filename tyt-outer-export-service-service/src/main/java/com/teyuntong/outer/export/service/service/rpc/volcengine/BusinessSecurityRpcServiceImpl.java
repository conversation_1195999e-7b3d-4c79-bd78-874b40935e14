package com.teyuntong.outer.export.service.service.rpc.volcengine;

import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.client.volcengine.service.BusinessSecurityRpcService;
import com.teyuntong.outer.export.service.client.volcengine.vo.IdCardTwoElementVerifyDTO;
import com.teyuntong.outer.export.service.client.volcengine.vo.IdCardTwoElementVerifyVO;
import com.teyuntong.outer.export.service.service.biz.volcengine.client.BusinessSecurityApi;
import com.teyuntong.outer.export.service.service.biz.volcengine.client.req.IdCardTwoElementVerifyReq;
import com.volcengine.model.response.ElementVerifyResponseV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 二要素认证
 *
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class BusinessSecurityRpcServiceImpl implements BusinessSecurityRpcService {
    @Autowired
    private BusinessSecurityApi businessSecurityApi;

    @Override
    public IdCardTwoElementVerifyVO verifyTwoElement(IdCardTwoElementVerifyDTO dto) {
        IdCardTwoElementVerifyReq req = new IdCardTwoElementVerifyReq();
        req.setIdCardNo(dto.getIdCardNo());
        req.setIdCardName(dto.getIdCardName());
        req.setOperateTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        ElementVerifyResponseV2 elementVerifyResponseV2 = businessSecurityApi.idCardTwoElementVerify(req);
        if (elementVerifyResponseV2 == null) {
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        ElementVerifyResponseV2.ElementVerifyResultV2 result = elementVerifyResponseV2.getResult();
        ElementVerifyResponseV2.ElementVerifyDataV2 data = result.getData();

        IdCardTwoElementVerifyVO idCardTwoElementVerifyVO = new IdCardTwoElementVerifyVO();
        idCardTwoElementVerifyVO.setSuccess(Objects.equals(0, result.getCode()) && data != null && Objects.equals(10001, data.getStatus()));
        idCardTwoElementVerifyVO.setOriginResponse(result);
        return idCardTwoElementVerifyVO;
    }
}
