package com.teyuntong.outer.export.service.service.biz.hbwj.service;


import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateCompanyRequest;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.CreateDetailDO;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.CreateDetailVO;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.PrincipalInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 湖北我家相关接口类
 *
 * <AUTHOR>
 * @since 2024/07/11 17:56
 */
public interface HBWJService {

    /**
     * 查询主体列表
     */
    List<PrincipalInfoVO> getPrincipalList()throws IOException;

    /**
     * 创建企业认证信息
     */
    CreateCompanyResponse createCompany(CreateCompanyRequest createCompanyRequest)throws IOException;

    /**
     * 查询企业信息
     */
    CreateDetailVO queryCompanyDetail(CreateDetailDO createDetailDO)throws IOException;

    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return CreateWaybillThreeResponse 创建运单返回对象
     */
    WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception;

    /**
     *  编辑运费
     * @param amountUpdateRequest
     * @return
     * @throws Exception
     */
    boolean updateAmount(AmountUpdateRequest amountUpdateRequest) throws Exception;


    WebResult<EditWaybillThreeResponse> editWaybillThree(EditWaybillThreeRequest editWaybillThreeRequest) throws Exception;

    /**
     * 运费申请
     * @param applyFreightRequest
     * @return
     * @throws Exception
     */
    WebResult<List<ApplyFreightResp>> applyFreight(ApplyFreightRequest applyFreightRequest, String userCode) throws Exception;

    /**
     * 新增司机
     *
     * <AUTHOR>
     * @param report 新增司机对象
     * @return driverDetailResponse 返回司机信息
     */
    DriverDetailResponse createDriver(HbwjDriverReport report)throws Exception;

    /**
     * 起运
     * <AUTHOR>
     * @param pickGoodsThreeRequest 起运请求对象
     * @return PickGoodsResponse   返回起运信息
     */
    WebResult<Object> pickGoods(PickGoodsThreeRequest pickGoodsThreeRequest)throws Exception;


    /**
     * 起运
     * <AUTHOR>
     * @param pickGoodsThreeV3Request 起运请求对象
     * @return PickGoodsResponse   返回起运信息
     */
    WebResult<Object> pickGoodsV3(PickGoodsThreeV3Request pickGoodsThreeV3Request)throws Exception;

    /**
     * 抵运
     * <AUTHOR>
     * @param arriveGoodsThreeRequest 抵运请求对象
     * @return ArriveGoodsResponse    返回抵运信息
     */
    WebResult<Object> arriveGoods(ArriveGoodsThreeRequest arriveGoodsThreeRequest)throws Exception;

    /**
     * 运单回调详情
     * <AUTHOR>
     * @param waybillThreeOffsetRequest 运单回调详情请求对象
     * @return ArriveGoodsResponse    返回运单回调详情信息
     */
    WebResult<WaybillThreeOffsetResp> getWaybillThreeOffset(WaybillThreeOffsetRequest waybillThreeOffsetRequest)throws Exception;
    /**
     * 取消订单
     * <AUTHOR>
     * @param cancelOrderThreeRequest 取消请求对象
     * @return WebResult<Object>  取消返回对象
     */
    WebResult<Object> cancelWaybill(CancelOrderThreeRequest cancelOrderThreeRequest)throws Exception;

    /**
     * 新增合同
     */
    CreateContractResponse createContract(CreateContractRequest request) throws Exception;

    /**
     * 新增项目
     */
    CreateProjectResponse createProject(CreateProjectRequest request, String userCode) throws Exception;

    /**
     * 查询钱包信息
     */
    QueryWalletResponse selectCustomerRechargeWalletList(QueryWalletRequest request) throws Exception;


    /**
     * 设置支付密码
     */
    WebResult<Object> setPayPwd(SavePayPwdRequest request) throws Exception;

    /**
     * 修改支付密码
     */
    WebResult<Object> changePayPwd(ChangePayPwdRequest request) throws Exception;

    /**
     * 发送更换手机验证码
     */
    void changePhoneCaptchaSendForAccount(ChangePhoneCaptchaSendRequest request) throws Exception;

    /**
     * 修改手机号
     */
    void changePhone(ChangePhoneRequest request) throws Exception;

    /**
     * 检查更换手机号验证码
     */
    ChangePhoneCaptchaCheckResponse changePhoneCaptchaCheck(ChangePhoneCaptchaCheckRequest request) throws Exception;
    /**
     * 图片上传
     * <AUTHOR>
     * @param uploadFile
     * @return void
     */
    FileUpLoadResp uploadFile(MultipartFile uploadFile)throws Exception;

    /**
     * 上传回单
     * @param uploadWeightReceiptsReqDto
     * @return
     */
    String uploadWeightReceipts(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto,String userCode) throws IOException ;

    /**
     * 获取验证码
     * @param mobile
     * @return
     */
    String getFundVerificationCode(String mobile, String userCode)  throws IOException;

    DriverDetailResponse createCar(HbwjCarReport report)throws IOException;

    WebResult<CreateProtocolResponse> createProtocol(CreateProtocolRequest createProtocolRequest) throws IOException;

    Object getGaoDeDistrictVoList() throws Exception;

    /**
     * 批量支付
     *
     * @param batchDoPayRequest 批量支付请求
     * @param userCode
     * @return BatchDoPayResponse
     */
    WebResult<List<BatchDoPayResponse>> batchDoPay(BatchDoPayRequest batchDoPayRequest, String userCode) throws Exception;

    /**
     * 新增开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return WebResult<Long>
     */
    WebResult<Long> invoiceCreate(InvoiceRequest invoiceRequest, String userCode) throws Exception;

    /**
     * 修改开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return WebResult<Long>
     */
    WebResult<Long> invoiceUpdate(InvoiceRequest invoiceRequest, String userCode) throws Exception;

    /**
     *
     * 发票创建
     *
     * @param invoiceCreateRequest
     * @param userCode
     * @return WebResult<Object>
     */
    WebResult<Object> doInvoiceCreate(InvoiceCreateRequest invoiceCreateRequest, String userCode) throws Exception;

    /**
     *
     * 查询发票开票状态
     *
     * @param invoiceInfoRequest 发票号码
     * @param userCode
     * @return WebResult<InvoiceResponse>
     */
    WebResult<InvoiceResponse> queryInvoiceCallBackVo(InvoiceInfoRequest invoiceInfoRequest, String userCode) throws Exception;

    /**
     * 上传回单图片
     *
     * @param uploadWeightReceiptsReqDto
     * @param userCode
     * @return WebResult<String>
     */
    WebResult<String> uploadWeightReceiptsNew(UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, String userCode) throws Exception;

    /**
     * 支付单审核结果查询
     * @param paymentNo
     * @param userCode
     * @return
     * @throws Exception
     */
    WebResult<PaymentOrderResp> getPaymentDetailByNo(String paymentNo, String userCode) throws Exception;


    /**
     * 撤销运单的支付单
     * @param cancelFreightRequest
     * @param userCode
     * @return
     * @throws Exception
     */
    WebResult<Object> cancelFreight(CancelFreightRequest cancelFreightRequest, String userCode) throws Exception;

    /**
     * 支付单撤销
     * @param paymentCancelRequest
     * @param userCode
     * @return
     * @throws Exception
     */
    WebResult<Object> paymentCancel(PaymentCancelRequest paymentCancelRequest, String userCode) throws Exception;

    /**
     * 运单凭证上传(V3)
     */
    WebResult<Object> receiptsUpload(ReceiptsUploadRequest receiptsUploadRequest, String userCode) throws Exception;

    /**
     * 运单运抵(V3)
     */
    WebResult<Object> arriveWaybill(ArriveWaybillRequest arriveWaybillRequest, String userCode) throws Exception;

    /**
     * 重置支付密码 - 发送短信验证码
     * @param userCode
     * @return
     */
    WebResult<Object> fundSendMsg(String userCode) throws Exception;

    /**
     * 重置支付密码
     * @param forgetPayPwdRequest
     * @param userCode
     * @return
     */
    WebResult<Object> forgetpaypwd(ForgetPayPwdRequest forgetPayPwdRequest, String userCode) throws Exception;
}
