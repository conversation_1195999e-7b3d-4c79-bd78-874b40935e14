package com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service;

import com.teyuntong.outer.export.service.client.person.VO.*;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.CreatePersonalResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryMemberAccountResponse;


public interface OpenAcctService {

    CreatePersonalResponse applyInactiveAcct(CreatePersonalReqVO vo);

    BaseResponse cancelVerify(CommonVO commonVO);

    UserRealResponse getUserRealInfo(QueryUserVerifyInfoReVo vo);

    QueryMemberAccountResponse queryMemberAccountInfo(QueryMemberAccountReqVO vo);

    BaseResponse depositAccountApply(CommonVO commonVO);

}
