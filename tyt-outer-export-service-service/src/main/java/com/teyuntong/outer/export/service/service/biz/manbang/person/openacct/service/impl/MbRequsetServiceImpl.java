package com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.impl;

import com.teyuntong.outer.export.service.client.person.VO.MbTokenInfoReqVO;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.MbRequsetService;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.VO.MbTokenReqVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MbRequsetUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MbRequsetServiceImpl implements MbRequsetService {
    @Value("${group1.api.base.url}")
    private String baseUrl;
    @Value("${group1.appId}")
    private String mbappId;
    @Value("${group1.privateKey}")
    private String mbprivateKey;
    @Override
    public String getMbToken(MbTokenInfoReqVO mbTokenInfo) {
        log.info("【getToken】入参：{}",mbTokenInfo);
        String type = "";
        // 21、22安卓  73：车主小程序
        if (mbTokenInfo.getClientSign() == 21 || mbTokenInfo.getClientSign() == 22) {
            type = "Android";
        }else if (mbTokenInfo.getClientSign() == 73) {
            type = "applet";
        } else {
            type = "iOS";
        }
        MbTokenReqVO rsa = MbTokenReqVO.builder()
                .appId(mbappId)
                .deviceId(mbTokenInfo.getDeviceId())
                .deviceType(type)
                .timestamp(mbTokenInfo.getTimestamp())
                .mbUserId("46_"+mbTokenInfo.getUserId())
                .build();
        String mbtoken = "";
        try {
            mbtoken = MbRequsetUtils.sendRequest(rsa, baseUrl, mbprivateKey);
        } catch (Exception e) {
           log.error("【getToken】返回失败：{}",e);
        }
        return mbtoken;
    }
}
