package com.teyuntong.outer.export.service.service.rpc.person;

import com.teyuntong.outer.export.service.client.person.PersonOpenAccountRpcService;
import com.teyuntong.outer.export.service.client.person.VO.*;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.MbRequsetService;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.OpenAcctService;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.CreatePersonalResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryMemberAccountResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class PersonOpenAcctRpcServiceImpl implements PersonOpenAccountRpcService {
    private final OpenAcctService openAcctService;

    private final MbRequsetService mbRequsetService;
    @Override
    public CreatePersonalResponse applyInactiveAcct(CreatePersonalReqVO vo) {
        return openAcctService.applyInactiveAcct(vo);
    }

    @Override
    public BaseResponse cancelVerify(CommonVO commonVO) {
        return openAcctService.cancelVerify(commonVO);
    }

    @Override
    public UserRealResponse getUserRealInfo(QueryUserVerifyInfoReVo queryUserVerifyInfoReVo) {
        return openAcctService.getUserRealInfo(queryUserVerifyInfoReVo);
    }

    @Override
    public QueryMemberAccountResponse queryMemberAccountInfo(QueryMemberAccountReqVO vo) {
        return openAcctService.queryMemberAccountInfo(vo);
    }

    @Override
    public BaseResponse depositAccountApply(CommonVO vo) {
        return openAcctService.depositAccountApply(vo);
    }

    @Override
    public String getToken(MbTokenInfoReqVO mbTokenInfo) {
        return mbRequsetService.getMbToken(mbTokenInfo);
    }
}
