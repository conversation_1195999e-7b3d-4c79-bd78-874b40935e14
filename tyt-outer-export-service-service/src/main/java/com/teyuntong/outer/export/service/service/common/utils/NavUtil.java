package com.teyuntong.outer.export.service.service.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 导航工具类
 *
 * <AUTHOR>
 * @since 2025/07/21 19:25
 */
public class NavUtil {

    private final static BigDecimal LENGTH_1 = new BigDecimal("3.5");
    private final static BigDecimal LENGTH_2 = new BigDecimal("6");
    private final static BigDecimal WEIGHT_1 = new BigDecimal("1.8");
    private final static BigDecimal WEIGHT_2 = new BigDecimal("4.5");
    private final static BigDecimal WEIGHT_3 = new BigDecimal("12");
    private final static BigDecimal ONE_HUNDRED = new BigDecimal("100");

    /**
     * 货车车辆大小，分类依据国标。
     * 1：微型车，车长小于等于3.5m，总质量小于等于1800kg。
     * 2：轻型车，车长小于6m，总质量小于4500kg
     * 3：中型车，车长大于等于6m，总质量大于等于4500kg且小于12000kg。
     * 4：重型车，车长大于等于6m，总质量大于等于12000kg。
     */
    public static String getTruckSize(BigDecimal length, BigDecimal weight) {
        if (length == null || weight == null) {
            return "4";
        }
        if (length.compareTo(LENGTH_1) <= 0 && weight.compareTo(WEIGHT_1) <= 0) {
            return "1";
        } else if (length.compareTo(LENGTH_2) < 0 && weight.compareTo(WEIGHT_2) < 0) {
            return "2";
        } else if (length.compareTo(LENGTH_2) >= 0 && weight.compareTo(WEIGHT_2) >= 0 && weight.compareTo(WEIGHT_3) < 0) {
            return "3";
        } else {
            return "4";
        }
    }

    /**
     * 经纬度如果超过6位，截取返回
     */
    public static String format(String coordinate) {
        BigDecimal decimal = new BigDecimal(coordinate);
        if (decimal.scale() > 6) {
            return decimal.setScale(6, RoundingMode.HALF_UP).toString();
        }
        return coordinate;
    }

    /**
     * 如果重量超过100吨，返回合规重量
     */
    public static String exceed100T(BigDecimal weight, String valid) {
        if (weight == null) {
            return "";
        }
        return weight.compareTo(ONE_HUNDRED) >= 0 ? valid : weight.toString();
    }

}
