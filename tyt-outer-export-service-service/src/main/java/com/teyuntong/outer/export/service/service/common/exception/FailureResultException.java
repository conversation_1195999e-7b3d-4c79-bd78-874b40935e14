package com.teyuntong.outer.export.service.service.common.exception;

/**
 * Created by duanwc on 17/9/27.
 */
public class FailureResultException extends RuntimeException {
    private static final long serialVersionUID = -4644075679931047367L;
    private final int statusCode;
    private final String retMsg;
    private final Integer code;


    public FailureResultException(int statusCode, String retMsg, Integer errorCode) {
        this.statusCode = statusCode;
        this.retMsg = retMsg;
        this.code = errorCode;
    }

    /** @deprecated */
    @Deprecated
    public FailureResultException(int statusCode, String retMsg) {
        this.statusCode = statusCode;
        this.retMsg = retMsg;
        this.code = null;
    }

    public int getStatusCode() {
        return this.statusCode;
    }

    public String getRetMsg() {
        return this.retMsg;
    }

    public Integer getErrorCode() {
        return this.code;
    }
}
