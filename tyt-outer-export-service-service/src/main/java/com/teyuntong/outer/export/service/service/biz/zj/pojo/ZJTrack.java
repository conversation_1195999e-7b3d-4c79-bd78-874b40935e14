package com.teyuntong.outer.export.service.service.biz.zj.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2024/11/05 19:20
 */
@Data
public class ZJTrack {

    /**
     * 正北方向夹角 正北，大于 0 且小于 90：东北，等于 90：正东，大于 90且小于 180：东南，等于 180：
     * 正南，大于 180 且小于 270：西南，等于 270：正西，大于 270 且小于等于 359：西北，其他：未知
     */
    @JsonProperty("agl")
    private String agl;

    /**
     * GPS 时间  20210809/104650
     */
    @JsonProperty("gtm")
    private String gtm;

    /**
     * 海拔 米
     */
    @JsonProperty("hgt")
    private String hgt;

    /**
     * 纬度
     */
    @JsonProperty("lat")
    private String lat;

    /**
     * 经度
     */
    @JsonProperty("lon")
    private String lon;

    /**
     * 上报里程 单位：千米
     */
    @JsonProperty("mlg")
    private String mlg;

    /**
     * GPS 速度 千米/小时
     */
    @JsonProperty("spd")
    private String spd;

    private String formattedGtm;

    private String originalLon;

    private String originalLat;


    // 方法：获取原始经度并保留6位小数
    public String getOriginalLon() {
        if (lon == null || lon.isEmpty()) {
            return null;
        }
        return String.format("%.6f", Double.parseDouble(lon) / 600000.0);
    }

    // 方法：获取原始纬度并保留6位小数
    public String getOriginalLat() {
        if (lat == null || lat.isEmpty()) {
            return null;
        }
        return String.format("%.6f", Double.parseDouble(lat) / 600000.0);
    }

    // 方法：将 GPS 时间转换为 yyyy-MM-dd hh:mm:ss 格式的日期字符串
    public String getFormattedGtm() {
        if (gtm == null || gtm.length() != 15) {
            return null; // 或者抛出异常，根据实际情况处理
        }

        String datePart = gtm.substring(0, 8);
        String timePart = gtm.substring(9, 15);

        LocalDate date = LocalDate.parse(datePart, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalTime time = LocalTime.parse(timePart, DateTimeFormatter.ofPattern("HHmmss"));

        LocalDateTime dateTime = LocalDateTime.of(date, time);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return formatter.format(dateTime);
    }

}
