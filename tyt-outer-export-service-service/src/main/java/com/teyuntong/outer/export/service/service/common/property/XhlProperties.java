package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 翔和翎配置类
 *
 * <AUTHOR>
 * @since 2025-1-13 18:18:47
 */
@Data
@ConfigurationProperties(prefix = "xhl")
public class XhlProperties {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * apiKey
     */
    private String apiKey;

    /**
     * apiUrl
     */
    private String apiUrl;

    /**
     * 司机人脸识别回调链接
     */
    private String driverFaceAuthCallBackUrl;
}
