package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlFreightEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlFreightService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 运费相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlFreightServiceImpl implements XhlFreightService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> payMoney(XhlFreightEntity freightEntity) throws Exception {
        log.info("运费打款-请求参数，freightEntity={}", freightEntity);
        if (!Objects.isNull(freightEntity)) {
            freightEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.payMoney(freightEntity).execute();
        log.info("运费打款-返回结果，freightEntity={}，响应参数：{} ", freightEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateCharge(XhlFreightEntity freightEntity) throws Exception {
        log.info("修改运费-请求参数，freightEntity={}", freightEntity);
        if (!Objects.isNull(freightEntity)) {
            freightEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateCharge(freightEntity).execute();
        log.info("修改运费-返回结果，freightEntity={}，响应参数：{} ", freightEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryPay(String companyName, String tpWaybillNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询运费支付结果-请求参数，appId={}，companyName={}，tpWaybillNo={}", appId, companyName, tpWaybillNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryPay(appId, companyName, tpWaybillNo).execute();
        log.info("查询运费支付结果-返回结果，appId={}，companyName={}，companyName={}，响应参数：{} ", appId, companyName, tpWaybillNo, response.body());
        return response.body();
    }

}
