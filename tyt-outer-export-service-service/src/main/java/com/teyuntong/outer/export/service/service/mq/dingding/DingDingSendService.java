package com.teyuntong.outer.export.service.service.mq.dingding;

import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.outer.export.service.service.common.mq.MessageCenterMQConfig;
import com.teyuntong.outer.export.service.service.common.mq.MqProducerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 钉钉通知
 *
 * <AUTHOR>
 * @since 2024/12/11 11:39
 */
@Slf4j
@Service
public class DingDingSendService {

    @Autowired
    private MqProducerService producer;

    @Autowired
    private MqMessageFactory mqMessageFactory;

    @Autowired
    private MessageCenterMQConfig mcMQConfig;


    /**
     * 发送钉钉群通知
     *
     * @param ding
     */
    public void sendDingdingGroupNotify(DingdingMessage ding) {
        if (StringUtils.isAnyBlank(ding.getRobotName(), ding.getMessage())) {
            return;
        }
        log.info("sendDingdingGroupNotify()开始发送钉钉群通知=================>{}", ding);
        MqMessage mqMessage = mqMessageFactory.create(mcMQConfig.getDingding().getMqTopic(), mcMQConfig.getDingding().getGroupTag(), ding);
        producer.sendNormal(mqMessage);
    }

}
