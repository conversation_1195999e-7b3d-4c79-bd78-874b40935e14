package com.teyuntong.outer.export.service.service.biz.ocr.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.teyuntong.outer.export.service.client.common.old.bean.ResponseCode;
import com.teyuntong.outer.export.service.client.common.old.enums.ResponseEnum;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
public class HttpClientUtil {

    /** 重试次数 **/
    private static final Integer max_retry_count = 1;

    /** 同域名同时请求线程数 **/
    private static final Integer max_conn_per_route = 20;

    /** httpClient **/
    private static final CloseableHttpClient instance = getHttpClientWithRetry();

    public static CloseableHttpClient getInstance(){
        return instance;
    }

    /**
     * 默认配置
     * @return
     */
    private static RequestConfig getHttpClientConfig(){
        RequestConfig requestConfig = RequestConfig.custom()
                .setCookieSpec(CookieSpecs.IGNORE_COOKIES)
                .setConnectTimeout(2000)
                .setSocketTimeout(10000)
                .build();
        return requestConfig;
    }

    /**
     * 重试机制
     * @return
     */
    private static HttpRequestRetryHandler getHttpRequestRetryHandler() {
        HttpRequestRetryHandler retryHandler = (exception, executionCount, context) -> {
            if (executionCount > max_retry_count) {
                // Do not retry if over max retry count
                log.info("Over the max retry count. Skip retrying.");
                return false;
            }
            if (exception instanceof InterruptedIOException) {
                // Timeout
                log.info("No retry." + exception.getMessage());
                return false;
            }
            if (exception instanceof UnknownHostException) {
                // Unknown host
                log.info("No retry." + exception.getMessage());
                return false;
            }
            if (exception instanceof ConnectTimeoutException) {
                // Connection refused
                log.info("Need retry." + exception.getMessage());
                return false;
            }
            if (exception instanceof SSLException) {
                // SSL handshake exception
                log.info("No retry." + exception.getMessage());
                return false;
            }
            if (exception instanceof NoHttpResponseException) {
                // Stale connection between client and LB.
                log.info("Need retry." + exception.getMessage());
                return false;
            }

            HttpClientContext clientContext = HttpClientContext.adapt(context);
            HttpRequest request = clientContext.getRequest();
            boolean idempotent = !(request instanceof HttpEntityEnclosingRequest);
            if (idempotent) {
                // Retry if the request is considered idempotent
                return true;
            }
            return false;
        };
        return retryHandler;
    }

    /**
     * 生成httpclient
     * @return
     */
    private static CloseableHttpClient getHttpClientWithRetry() {

        RequestConfig httpClientConfig = getHttpClientConfig();
        HttpRequestRetryHandler retryHandler = getHttpRequestRetryHandler();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(httpClientConfig)
                .setMaxConnPerRoute(max_conn_per_route)
                .setRetryHandler(retryHandler)
                .build();

        return httpClient;
    }

    /**
     * 创建uri 并拼接参数
     * @param apiUrl
     * @param paramMap
     * @return
     */
    public static URI createUri(String apiUrl, Map<String, String> paramMap){
        URI uri = null;
        try {
            URIBuilder builder = new URIBuilder(apiUrl);

            if(MapUtils.isNotEmpty(paramMap)){
                paramMap.forEach(builder::addParameter);
            }
            uri = builder.build();
        } catch (URISyntaxException e) {
            log.error("", e);
        }

        return uri;
    }

    /**
     * 解析json
     * @param httpResponse
     * @return
     */
    public static JSONObject toJsonObject(CloseableHttpResponse httpResponse) {

        JSONObject jsonObject = null;

        try {
            HttpEntity entity = httpResponse.getEntity();
            String jsonStr = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if(statusCode != 200) {
                log.warn("toJsonObject_error : httpStatus : [{}], respBody : {}", statusCode, jsonStr);
                throw TytException.createException(ResponseEnum.sys_busy.info());
            }

            jsonObject = JSON.parseObject(jsonStr);
        } catch (IOException e) {
            throw TytException.createException(e);
        } finally {
            if(httpResponse != null){
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }

        return jsonObject;
    }

    /**
     * http client 请求
     * @param httpRequest
     * @return
     */
    public static CloseableHttpResponse execute(HttpUriRequest httpRequest) {
        CloseableHttpResponse httpResponse = null;

        try {
            httpResponse = instance.execute(httpRequest);
        } catch (Exception e) {
            log.error("http_client_execute_error : ", e);

            TytException te = null;

            if(e instanceof ConnectTimeoutException){
                te = TytException.createException(ResponseEnum.timeout.info());
            } else if(e instanceof SocketTimeoutException){
                te = TytException.createException(ResponseEnum.sys_busy.info());
            } else {
                te = TytException.createException(ResponseEnum.sys_busy.info());
            }

            throw te;
        }

        return httpResponse;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(CloseableHttpResponse httpResponse, TypeReference typeReference) {
        JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }
        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)){

            data = jsonObject.getObject("data", new TypeReference<List<String>>(){});

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code, msg));
        }

        return data;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(CloseableHttpResponse httpResponse, Class<T> clazz) {

        JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }

        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)) {

            data = jsonObject.getObject("data", clazz);

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code, msg));
        }

        return data;
    }


}
