package com.teyuntong.outer.export.service.service.biz.xhl.client;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 翔和翎接口定义
 *
 * <AUTHOR>
 * @since 2025/01/15 14:01
 */
public interface XhlInterfaceClient {

    /**
     * 新增托运人
     *
     * @param body 托运人信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/company/save")
    Call<XhlDataResult<Object>> saveCompany(@Body XhlCarrierEntity body);

    /**
     * 修改托运人
     *
     * @param body 托运人信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/company/update")
    Call<XhlDataResult<Object>> updateCompany(@Body XhlCarrierEntity body);

    /**
     * 查询托运人
     *
     * @param appId       应用ID
     * @param companyName 公司名称
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/company/query")
    Call<XhlDataResult<Object>> queryCompany(@Query("appId") String appId, @Query("companyName") String companyName);

    /**
     * 托运人绑卡
     *
     * @param body 托运人绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/company/bank/save")
    Call<XhlDataResult<Object>> saveCompanyBank(@Body XhlCarrierEntity body);

    /**
     * 托运人解绑银行卡
     *
     * @param body 托运人解绑银行卡信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/company/bank/delete")
    Call<XhlDataResult<Object>> deleteCompanyBank(@Body XhlCarrierEntity body);

    /**
     * 查询托运人余额
     *
     * @param appId       应用ID
     * @param companyName 公司名称
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/company/balance")
    Call<XhlDataResult<Object>> queryCompanyBalance(@Query("appId") String appId, @Query("companyName") String companyName);


    /**
     * 新增驾驶员
     *
     * @param body 司机信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/save")
    Call<XhlDataResult<Object>> saveDriver(@Body XhlDriverEntity body);


    /**
     * 修改驾驶员
     *
     * @param body 司机信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/update")
    Call<XhlDataResult<Object>> updateDriver(@Body XhlDriverEntity body);

    /**
     * 查询驾驶员信息
     *
     * @param appId    应用ID
     * @param idCardNo 身份证号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/driver/query")
    Call<XhlDataResult<Object>> queryDriver(@Query("appId") String appId, @Query("idCardNo") String idCardNo);


    /**
     * 驾驶员绑卡
     *
     * @param body 驾驶员绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/bank/save")
    Call<XhlDataResult<Object>> saveDriverBank(@Body XhlDriverEntity body);

    /**
     * 驾驶员解绑银行卡
     *
     * @param body 驾驶员绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/bank/delete")
    Call<XhlDataResult<Object>> deleteDriverBank(@Body XhlDriverEntity body);


    /**
     * 新增车辆
     *
     * @param body 新增车辆信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/truck/save")
    Call<XhlDataResult<Object>> saveTruck(@Body XhlTruckEntity body);

    /**
     * 修改车辆
     *
     * @param body 修改车辆信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/truck/update")
    Call<XhlDataResult<Object>> updateTruck(@Body XhlTruckEntity body);

    /**
     * 查询车辆信息
     *
     * @param appId       应用ID
     * @param plateNumber 车牌号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/truck/query")
    Call<XhlDataResult<Object>> queryTruck(@Query("appId") String appId, @Query("plateNumber") String plateNumber);


    /**
     * 新增人车合照
     *
     * @param body 新增人车合照信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/rel/driver_truck/save")
    Call<XhlDataResult<Object>> saveDriverTruck(@Body XhlDriverTruckEntity body);

    /**
     * 修改人车合照
     *
     * @param body 修改人车合照信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/rel/driver_truck/update")
    Call<XhlDataResult<Object>> updateDriverTruck(@Body XhlDriverTruckEntity body);

    /**
     * 查询人车合照
     *
     * @param appId       应用ID
     * @param idCardNo    身份证号
     * @param plateNumber 车牌号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/rel/driver_truck/query")
    Call<XhlDataResult<Object>> queryDriverTruck(@Query("appId") String appId, @Query("idCardNo") String idCardNo, @Query("plateNumber") String plateNumber);

    /**
     * 保存司机授权（委托代征）
     *
     * @param body 驾驶员信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/contract/driver/empower")
    Call<XhlDataResult<Object>> saveEmpowerDriver(@Body XhlDriverEntity body);

    /**
     * 查询司机授权（委托代征）
     *
     * @param appId    应用ID
     * @param idCardNo 身份证号
     * @return 返回操作结果的封装对象
     */
    @GET("external/api/tyt/contract/driver/query")
    Call<XhlDataResult<Object>> queryEmpowerDriver(@Query("appId") String appId, @Query("idCardNo") String idCardNo);


    /**
     * 新增订单
     *
     * @param body 订单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/order/save")
    Call<XhlDataResult<Object>> saveOrder(@Body XhlOrderEntity body);

    /**
     * 修改订单
     *
     * @param body 订单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/order/update")
    Call<XhlDataResult<Object>> updateOrder(@Body XhlOrderEntity body);

    /**
     * 查询订单
     *
     * @param appId       应用ID
     * @param companyName 公司名称
     * @param tpOrderNo   第三方订单号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/order/query")
    Call<XhlDataResult<Object>> queryOrder(@Query("appId") String appId, @Query("companyName") String companyName, @Query("tpOrderNo") String tpOrderNo);

    /**
     * 派单
     *
     * @param body 订单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/order/send")
    Call<XhlDataResult<Object>> sendOrder(@Body XhlOrderEntity body);

    /**
     * 运单发车
     *
     * @param body 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/depart")
    Call<XhlDataResult<Object>> departWaybill(@Body XhlOrderEntity body);

    /**
     * 运单签收
     *
     * @param body 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/sign")
    Call<XhlDataResult<Object>> signWaybill(@Body XhlOrderEntity body);

    /**
     * 运单删除
     *
     * @param body 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/delete")
    Call<XhlDataResult<Object>> deleteWaybill(@Body XhlOrderEntity body);

    /**
     * 运单修改
     *
     * @param body 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/update")
    Call<XhlDataResult<Object>> updateWaybill(@Body XhlOrderEntity body);

    /**
     * 运单查询
     *
     * @param appId       应用ID
     * @param companyName 公司名称
     * @param tpWaybillNo 第三方运单号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/waybill/query")
    Call<XhlDataResult<Object>> queryWaybill(@Query("appId") String appId, @Query("companyName") String companyName, @Query("tpWaybillNo") String tpWaybillNo);

    /**
     * 运费打款
     *
     * @param body 运费实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/payMoney")
    Call<XhlDataResult<Object>> payMoney(@Body XhlFreightEntity body);


    /**
     * 修改运费
     *
     * @param body 运费实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/waybill/updateCharge")
    Call<XhlDataResult<Object>> updateCharge(@Body XhlFreightEntity body);

    /**
     * 查询运费支付结果
     *
     * @param appId       应用ID
     * @param companyName 公司名称
     * @param tpWaybillNo 第三方运单号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/waybill/queryPay")
    Call<XhlDataResult<Object>> queryPay(@Query("appId") String appId, @Query("companyName") String companyName, @Query("tpWaybillNo") String tpWaybillNo);

    /**
     * 发票申请
     *
     * @param body 发票实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/invoice/apply")
    Call<XhlDataResult<Object>> applyInvoice(@Body XhlInvoiceEntity body);


    /**
     * 发票取消
     *
     * @param body 发票实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/invoice/delete")
    Call<XhlDataResult<Object>> deleteInvoice(@Body XhlInvoiceEntity body);

    /**
     * 查询发票申请
     *
     * @param appId          应用ID
     * @param invoiceApplyNo 发票申请单号
     * @return 返回查询结果的封装对象
     */
    @GET("external/api/tyt/invoice/query")
    Call<XhlDataResult<Object>> queryInvoice(@Query("appId") String appId, @Query("invoiceApplyNo") String invoiceApplyNo);

    /**
     * 司机人脸认证
     *
     * @param body 发票实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/saveAppFaceAuth")
    Call<XhlDataResult<Object>> saveAppFaceAuth(@Body XhlFaceAuthEntity body);

    /**
     * 司机人脸结果查询
     *
     * @param body 发票实体
     * @return 返回操作结果的封装对象
     */
    @POST("external/api/tyt/driver/queryAppFaceResult")
    Call<XhlDataResult<Object>> queryAppFaceResult(@Body XhlFaceAuthEntity body);
}

