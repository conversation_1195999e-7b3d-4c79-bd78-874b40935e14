package com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.client.enterprise.vo.*;
import com.teyuntong.outer.export.service.service.biz.manbang.GateWayClient;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.EnterpriseAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.vo.EnterpriseBillResult;
import com.teyuntong.outer.export.service.service.biz.manbang.enums.ResponseStatusEnum;
import com.wlqq.wallet.gateway.client.enums.AccountTypeKind;
import com.wlqq.wallet.gateway.client.enums.IdentityType;
import com.wlqq.wallet.gateway.client.enums.ServiceKind;
import com.wlqq.wallet.gateway.client.request.mag.QueryBillRequest;
import com.wlqq.wallet.gateway.client.request.mgs.*;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.AccountData;
import com.wlqq.wallet.gateway.client.response.mgs.BillResult;
import com.wlqq.wallet.gateway.client.response.mgs.CreateEnterpriseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryBalanceResponse;
import io.reactivex.rxjava3.core.Single;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/04/02 17:36
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class EnterpriseAccountServiceImpl implements EnterpriseAccountService {

    private final GateWayClient client;

    /**
     * 定义返回创建企业用户返回信息
     */
    private static final String IDENTITY_EXIST_ERROR="IDENTITY_EXIST_ERROR";

    /**
     * 定义满帮企业网商开户返回信息
     */
    private static final String MEMBER_NOT_EXIST="MEMBER_NOT_EXIST";

    @Override
    public CreateEnterpriseApiResp createEnterprise(EnterpriseAccountApiReq req) {
        CreateEnterpriseApiResp createEnterpriseResp= new CreateEnterpriseApiResp();
        CreateEnterpriseRequest request=new CreateEnterpriseRequest();
        request.setLogin_name(req.getLoginName());
        request.setUid(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        log.info("满帮创建企业用户请求报文为【content:{}】", JSONObject.toJSONString(request));
        CreateEnterpriseResponse response = client.doService(ServiceKind.create_enterprise_member, request, CreateEnterpriseResponse.class);
        log.info("满帮创建企业用户网关响应结果:{}",response);
        if (response!=null){
            if(ResponseStatusEnum.成功.getCode().equals(response.getIsSuccess())){
                String memberId = response.getMember_id();
                if (Strings.isNotEmpty(memberId)){
                    createEnterpriseResp.setMemberId(memberId);
                    return createEnterpriseResp;
                }
            }
            if(ResponseStatusEnum.失败.getCode().equals(response.getIsSuccess())&&IDENTITY_EXIST_ERROR.equals(response.getErrorCode())){
                createEnterpriseResp.setCreateMsg(IDENTITY_EXIST_ERROR);
                return createEnterpriseResp;
            }
        }
        return createEnterpriseResp;
    }

    @Override
    public Boolean enterpriseOpenAccount(EnterpriseAccountApiReq req) {
        OpenAccountRequest request = new OpenAccountRequest();
        request.setIdentityType(IdentityType.UID);
        request.setIdentityNo(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        request.setAccountType(AccountTypeKind.ENTERPRISE_CITIC_16_ACCOUNT_SZMY.getMsg());
        log.info("满帮专户开户请求报文为【content:{}】", JSONObject.toJSONString(request));
        BaseResponse response = client.doService(ServiceKind.open_account,request);
        log.info("满帮专户开户网关响应结果:{}",response);
        if (response!=null){
            if(ResponseStatusEnum.成功.getCode().equals(response.getIsSuccess())){
             return true;
            }
        }
        return false;
    }


    @Override
    public EnterpriseOpenNetAccountApiResp enterpriseOpenNetAccount(EnterpriseAccountApiReq req){
        EnterpriseOpenNetAccountApiResp openNetAccountApiResp= new EnterpriseOpenNetAccountApiResp();
        EnterpriseRequest request = new EnterpriseRequest();
        request.setIdentityType(IdentityType.UID.getCode());
        request.setIdentityNo(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        request.setMainName(Constants.ENTERPRISE_MAIN_NAME);
        request.setEnterpriseName(req.getEnterpriseName());
        log.info("满帮企业网商开户请求报文为【content:{}】", JSONObject.toJSONString(request));
        BaseResponse response = client.doService(ServiceKind.open_enterprise_net_account_v2,request,BaseResponse.class);
        log.info("满帮企业网商开户网关响应结果:{}",response);
        if (response!=null){
            if(ResponseStatusEnum.成功.getCode().equals(response.getIsSuccess())&& ObjectUtils.isNotEmpty(response.getData())){
                GdAccountApiReqWrapper gdAccount = null;
                try {
                    gdAccount = new ObjectMapper().readValue(response.getData().toString(), GdAccountApiReqWrapper.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                openNetAccountApiResp.setParentAccountNo(gdAccount.getGdAccount().getParentAccountNo());
                openNetAccountApiResp.setSubAccountNo(gdAccount.getGdAccount().getSubAccountNo());
                return openNetAccountApiResp;
            }
            if(ResponseStatusEnum.失败.getCode().equals(response.getIsSuccess())&&MEMBER_NOT_EXIST.equals(response.getErrorMessage())){
                openNetAccountApiResp.setCreateMsg(MEMBER_NOT_EXIST);
                return openNetAccountApiResp;
            }
        }
        return openNetAccountApiResp;
    }

    @Override
    public EnterpriseOpenNetAccountApiResp queryEnterpriseNetAccountInfo(EnterpriseAccountApiReq req) {
        NetUserInfoRequest request = new NetUserInfoRequest();
        request.setIdentityType(IdentityType.UID.getCode());
        request.setIdentityNo(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        request.setMainName(Constants.ENTERPRISE_MAIN_NAME);
        log.info("满帮查询网商开户信息请求报文为【content:{}】", JSONObject.toJSONString(request));
        BaseResponse response = client.doService(ServiceKind.query_net_user_info,request,BaseResponse.class);
        log.info("满帮查询网商开户网关响应结果:{}",response);
        return null;
    }


    @Override
    public QueryEnterpriseBalanceApiResp queryEnterpriseBalance(QueryEnterpriseBalanceApiReq req) {
        //声明要返回的实体
        QueryEnterpriseBalanceApiResp balanceApiResp= new QueryEnterpriseBalanceApiResp();
        //组装请求参数
        QueryBalanceRequest balanceRequest = new QueryBalanceRequest();
        balanceRequest.setIdentityType(IdentityType.UID);
        balanceRequest.setIdentityNo(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        balanceRequest.setAccountType(AccountTypeKind.ENTERPRISE_CITIC_16_ACCOUNT_SZMY);
        log.info("满帮查询企业用户账户余额请求报文为【content:{}】", JSONObject.toJSONString(balanceRequest));
        QueryBalanceResponse balanceResponse = client.doService(ServiceKind.query_balance,balanceRequest,QueryBalanceResponse.class);
        if(balanceResponse!=null&&ResponseStatusEnum.成功.getCode().equals(balanceResponse.getIsSuccess())&&balanceResponse.getAccount_list().size()>0){
            AccountData accountData = balanceResponse.getAccount_list().get(0);
            log.info("满帮查询企业用户账户余额网关响应结果:{}",JSON.toJSONString(accountData));
            balanceApiResp.setMemberId(balanceResponse.getMember_id());
            balanceApiResp.setBalance(accountData==null?new BigDecimal("0.00"):new BigDecimal(accountData.getAvailableBalance()));
            balanceApiResp.setFreezeBalance(accountData==null?new BigDecimal("0.00"):new BigDecimal( accountData.getFrozenBalance()));
        }
        return balanceApiResp;
    }

    @Override
    public ResultMsgBean queryEnterpriseBill(QueryEnterpriseBillApiReq req) {
        ResultMsgBean resultMsgBean= new ResultMsgBean();
        QueryBillRequest request = new QueryBillRequest();
        request.setIdentityNo(Constants.GROUP_ENTERPRISE_PREFIX+req.getUid());
        request.setIdentityType(IdentityType.UID.getCode());
        request.setCurrentPage("1");
        request.setPageSize("100");
        if (StringUtils.isNotBlank(req.getStartTime())){
            request.setStartTime(req.getStartTime());
        }
        if (StringUtils.isNotBlank(req.getEndTime())){
            request.setEndTime(req.getEndTime());
        }
        log.info("满帮查询企业用户账单请求报文为【content:{}】", JSONObject.toJSONString(request));
        BillResult response =  client.doService(ServiceKind.query_bill, request, BillResult.class);
        log.info("满帮查询企业用户账单网关响应结果:{}", JSONObject.toJSONString(response));
        if(Objects.nonNull(response)&&ResponseStatusEnum.成功.getCode().equals(response.getIsSuccess())){
            resultMsgBean.setCode(200);
            resultMsgBean.setData(response);
        }
        return resultMsgBean;
    }
}
