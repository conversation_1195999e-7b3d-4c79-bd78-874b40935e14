package com.teyuntong.outer.export.service.service.biz.xhl.pojo;

import lombok.Data;

/**
 * 翔和翎 车辆实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlTruckEntity {

    /**
     * appId
     */
    private String appId;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 车辆类型，行驶证上的车辆类型
     */
    private String truckType;

    /**
     * 车长，单位mm
     */
    private String length;

    /**
     * 车宽，单位mm
     */
    private String width;

    /**
     * 车高，单位mm
     */
    private String height;

    /**
     * 行驶证照片地址
     */
    private String vehicleLicenseUrl;

    /**
     * 行驶证副页
     */
    private String vehicleLicenseBackUrl;

    /**
     * 行驶证年审页
     */
    private String vehicleLicenseCheckUrl;

    /**
     * 行驶证注册日期，格式yyyy-MM-dd
     */
    private String regDate;

    /**
     * 强制报废日期，格式yyyy-MM-dd
     */
    private String scrapDate;

    /**
     * 行驶证发证日期，格式yyyy-MM-dd
     */
    private String issueDate;

    /**
     * 道路运输证号
     */
    private String optCertNo;

    /**
     * 道路运输照片地址
     */
    private String optCertNoUrl;

    /**
     * 道路运输证失效日期，格式yyyy-MM-dd
     */
    private String optCertExpireTime;

    /**
     * 车牌颜色（1蓝色，2黄色，3黑色，4白色，5绿色，9其他，91农黄色，92农绿色，93黄绿色，94渐变绿）
     */
    private String plateColor;

    /**
     * 车辆所有人，车辆行驶证上的所有人
     */
    private String ownerName;

    /**
     * 是否挂靠（1挂靠、2自有）
     */
    private String isPersonal;

    /**
     * 能源类型，A汽油，B柴油，C电，D混石油，E天然气，F液化石油气，L甲醇，M乙醇，N太阳能
     */
    private String energyType;

    /**
     * 总质量，单位kg，若没有传0
     */
    private String totalWeight;

    /**
     * 整备质量，单位kg，若没有传0
     */
    private String curbWeight;

    /**
     * 核定载质量，单位kg，若没有传0
     */
    private String approveWeight;

    /**
     * 牵引总质量，单位kg，若没有传0
     */
    private String tractionWeight;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 行驶证发证机关
     */
    private String issuingOrganizations;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 经营许可证号
     */
    private String businessCertificate;

    /**
     * 许可证截止时间，格式yyyy-MM-dd
     */
    private String businessCertificateExpireTime;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String createTime;

    /**
     * 审核状态
     */
    private String checkFlag;

    /**
     * 审核原因
     */
    private String checkLog;
}
