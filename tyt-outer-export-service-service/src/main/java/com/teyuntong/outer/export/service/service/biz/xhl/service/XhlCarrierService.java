package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlCarrierEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;

/**
 * 翔和翎 托运人相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlCarrierService {

    /**
     * 新增托运人
     *
     * @param carrierEntity 托运人实体，包含托运人的相关信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> saveCompany(XhlCarrierEntity carrierEntity) throws Exception;

    /**
     * 修改托运人信息
     *
     * @param carrierEntity 托运人实体，包含需要更新的托运人信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> updateCompany(XhlCarrierEntity carrierEntity) throws Exception;

    /**
     * 查询托运人信息
     *
     * @param companyName 托运人公司名称，用于查询特定托运人的信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> queryCompany(String companyName) throws Exception;

    /**
     * 托运人绑卡操作
     *
     * @param carrierEntity 托运人实体，包含需要绑定的银行卡信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> saveBank(XhlCarrierEntity carrierEntity) throws Exception;

    /**
     * 托运人解绑银行卡操作
     *
     * @param carrierEntity 托运人实体，包含需要解绑的银行卡信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> deleteBank(XhlCarrierEntity carrierEntity) throws Exception;

    /**
     * 查询托运人余额
     *
     * @param companyName 托运人公司名称，用于查询特定托运人的余额信息
     * @return 返回数据结果
     * @throws Exception 如果操作失败，抛出异常
     */
    XhlDataResult<Object> queryBalance(String companyName) throws Exception;

}
