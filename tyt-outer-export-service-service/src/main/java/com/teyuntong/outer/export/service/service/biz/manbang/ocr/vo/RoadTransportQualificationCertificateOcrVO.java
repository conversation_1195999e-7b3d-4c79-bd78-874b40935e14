package com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 道路运输从业资格证主页OCR结果类
 *
 * <AUTHOR>
 * @since 2024/04/26 14:03
 */
@Data
@Builder
public class RoadTransportQualificationCertificateOcrVO {

    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 考核时间
     */
    private String assessmentDate;
    /**
     * 从业资格证号
     */
    private String certificateNumber;
    /**
     * 档案号
     */
    private String fileNumber;
    /**
     * 福路通号
     */
    private String unionCardNumber;
    /**
     * 继续教育信息
     */
    private String continuingEducationInfo;
    /**
     * 性别
     */
    private String sex;
    /**
     * 联系电话
     */
    private String phoneNumber;
    /**
     * 登记时间
     */
    private String registrationDate;
    /**
     * 单位
     */
    private String workUnit;
    /**
     * 诚信考核信息
     */
    private String integrityAssessmentInfo;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 姓名
     */
    private String name;
    /**
     * 住址
     */
    private String address;
    /**
     * 准驾车型
     */
    private String drivingClass;
    /**
     * 发证机关
     */
    private String issuingAuthority;
    /**
     * 出生日期
     */
    private String birthDate;
    /**
     * 从业资格列表
     */
    private List<QualificationCategoryVO> qualificationCategoryList;
}
