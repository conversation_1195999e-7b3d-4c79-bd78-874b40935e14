package com.teyuntong.outer.export.service.service.rpc.location;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.teyuntong.outer.export.service.client.location.dto.LastLocationRpcDTO;
import com.teyuntong.outer.export.service.client.location.dto.LocationTraceRpcDTO;
import com.teyuntong.outer.export.service.client.location.service.LocationRpcService;
import com.teyuntong.outer.export.service.client.location.vo.*;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LocationTraceDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.service.LocationService;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17 14:41
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class LocationRpcServiceImpl implements LocationRpcService {

    private final LocationService locationService;
    @Override
    public List<VehicleLocationRpcVO> getVehicleLastLocation(List<LastLocationRpcDTO> lastLocationRpcDTOList) {

        List<LastLocationDTO> lastLocationDTOS = BeanUtil.copyToList(lastLocationRpcDTOList, LastLocationDTO.class);
        List<VehicleLocationVO> vehicleLastLocationVOList = locationService.getVehicleLastLocation(lastLocationDTOS);
        return BeanUtil.copyToList(vehicleLastLocationVOList, VehicleLocationRpcVO.class);

    }

    @Override
    public List<SinoiovVehicleLastLocationRpcVO> getVehicleSinoiovLastLocation(List<LastLocationRpcDTO> lastLocationRpcDTOList) {
        List<LastLocationDTO> lastLocationDTOS = BeanUtil.copyToList(lastLocationRpcDTOList, LastLocationDTO.class);
        List<SinoiovVehicleLastLocationVO> sinoiovVehicleLastLocationVOList = locationService.getVehicleSinoiovLastLocation(lastLocationDTOS);
        return BeanUtil.copyToList(sinoiovVehicleLastLocationVOList, SinoiovVehicleLastLocationRpcVO.class);
    }


    @Override
    public List<AppLastPositionRpcVO> getAppLastPosition(List<Long> userIds) {

        List<AppLastPositionVO> appLastPositionList = locationService.getAppLastPosition(userIds);
        return BeanUtil.copyToList(appLastPositionList, AppLastPositionRpcVO.class);
    }

    @Override
    public LocationTraceRpcVO getLocationTrace(LocationTraceRpcDTO locationTraceRpcDTO) {

        LocationTraceDTO locationTraceDTO = BeanUtil.copyProperties(locationTraceRpcDTO, LocationTraceDTO.class);
        LocationTraceVO locationTraceVO = locationService.getLocationTrace(locationTraceDTO);
        return BeanUtil.copyProperties(locationTraceVO, LocationTraceRpcVO.class);
    }

    @Override
    public List<LbsAuthRpcVO> getLbsAuth(String vehicleNo) {
        List<LbsAuthVO> lbsAuthVOList = locationService.getLbsAuth(vehicleNo);
        if(CollUtil.isNotEmpty(lbsAuthVOList)){
            List<LbsAuthRpcVO> lbsAuthRpcVOList = new ArrayList<>();
            lbsAuthVOList.forEach(lbsAuthVO ->{
                LbsAuthRpcVO lbsAuthRpcVO = new LbsAuthRpcVO();
                lbsAuthRpcVO.setDriverId(lbsAuthVO.getDriverId());
                lbsAuthRpcVO.setLbsAuth(lbsAuthVO.getLbsAuth());
                lbsAuthRpcVOList.add(lbsAuthRpcVO);
            });
            return lbsAuthRpcVOList;
        }
        return Collections.emptyList();
    }
}
