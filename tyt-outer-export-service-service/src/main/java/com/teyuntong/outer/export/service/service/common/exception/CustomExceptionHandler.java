package com.teyuntong.outer.export.service.service.common.exception;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.advice.ExceptionHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.circuitbreaker.NoFallbackAvailableException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/19 17:17
 */
@Slf4j
@RestControllerAdvice
public class CustomException<PERSON>andler extends ExceptionHandlerBase {

}
