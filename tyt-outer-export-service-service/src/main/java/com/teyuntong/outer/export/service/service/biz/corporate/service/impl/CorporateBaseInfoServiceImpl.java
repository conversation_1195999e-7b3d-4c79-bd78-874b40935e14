package com.teyuntong.outer.export.service.service.biz.corporate.service.impl;

import com.teyuntong.outer.export.service.client.common.old.bean.ResultMsgBean;
import com.teyuntong.outer.export.service.client.corporate.vo.CorporateBaseInfoBean;
import com.teyuntong.outer.export.service.service.biz.corporate.client.TianYanChaAPI;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.CorporateBaseInfo;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper.CorporateBaseInfoMapper;
import com.teyuntong.outer.export.service.service.biz.corporate.service.CorporateBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 10
 * @date 2021/01/19
 */
@Service
@Slf4j
public class CorporateBaseInfoServiceImpl implements CorporateBaseInfoService {

    @Autowired
    private CorporateBaseInfoMapper mapper;

    @Override
    public CorporateBaseInfoBean addAndGetCorporateBaseInfo(String keyword) {
        CorporateBaseInfoBean bean = null;
        // 查询数据库存在企业信息数据
        CorporateBaseInfo baseInfo = this.getCorporateBaseInfo(keyword);
        if (baseInfo != null) {
            bean = new CorporateBaseInfoBean();
            BeanUtils.copyProperties(baseInfo, bean);
            return bean;
        }
        // 查询远程API接口数据
        ResultMsgBean<CorporateBaseInfo> resultMsgBean = TianYanChaAPI.getCorporateBaseInfo(keyword);
        if (resultMsgBean != null) {
            if (resultMsgBean.getCode() == 200) {
                CorporateBaseInfo remoteBaseInfo = resultMsgBean.getData();
                // 存入数据库
                int i = this.insertCorporateBaseInfo(remoteBaseInfo);
                bean = new CorporateBaseInfoBean();
                BeanUtils.copyProperties(remoteBaseInfo, bean);
                return bean;
            }
        }
        return bean;
    }

    @Override
    public CorporateBaseInfo getCorporateBaseInfo(String keyword) {
        return mapper.getCorporateBaseInfo(keyword);
    }

    @Override
    public int insertCorporateBaseInfo(CorporateBaseInfo baseInfo) {
        return mapper.addCorporateBaseInfo(baseInfo);
    }
}
