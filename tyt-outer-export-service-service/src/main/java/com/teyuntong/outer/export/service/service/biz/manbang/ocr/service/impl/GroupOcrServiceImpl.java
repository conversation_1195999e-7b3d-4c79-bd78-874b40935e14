package com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupOcrService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.BusinessLicenseOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQualificationCertificateOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VehicleLicenseDeputyPageBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import com.teyuntong.outer.export.service.service.common.property.GroupOcrProperties;
import com.teyuntong.outer.export.service.service.common.utils.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants.DECODE_VALUE_KEY;

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(GroupOcrProperties.class)
public class GroupOcrServiceImpl implements GroupOcrService {

    public static final MediaType jsonMediaType = MediaType.parse("application/json; charset=utf-8");

    private final GroupOcrProperties groupOcrProperties;

    private final MBOpenPlatformUtil util;

    @Override
    public RoadTransportBackOcrVO roadTransportBackOcr(String url) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getRoadTransportBackOcrServiceId())
                .add(Constants.IMG_URL_KEY, url)
                .build();
        String responseBodyStr = util.doPost(Constants.ROAD_TRANSPORT_BACK_OCR_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            RoadTransportBackOcrVO roadTransportBackOcrVO = JSON.parseObject(responseBodyStr, RoadTransportBackOcrVO.class);
            if (ObjectUtil.isNotNull(roadTransportBackOcrVO)
                    && StringUtils.isNotBlank(roadTransportBackOcrVO.getIssueDate())) {
                formatTime(roadTransportBackOcrVO);
            }
            if (ObjectUtil.isNotNull(roadTransportBackOcrVO)
                    && StringUtils.isNotBlank(roadTransportBackOcrVO.getPlateNum())) {
                roadTransportBackOcrVO.setPlateNum(this.decode(roadTransportBackOcrVO.getPlateNum(), "PLATE_NUMBER"));
            }
            return roadTransportBackOcrVO;
        }
        return null;
    }

    private String decode(String decodeStr, String type) {
        if (StringUtils.isBlank(decodeStr)) {
            return decodeStr;
        }
        try {
            Map<String, Map<String, List<String>>> bodyMap = new HashMap<>();
            Map<String, List<String>> value = new HashMap<>();
            value.put(type, List.of(decodeStr));
            bodyMap.put(DECODE_VALUE_KEY, value);
            RequestBody body = RequestBody.create(jsonMediaType, JSON.toJSONString(bodyMap));

            String responseBodyStr = util.doPostForDecode(Constants.YMM_OPEN_PLATFORM_DECODE_URL, body);
            if (StringUtils.isNotBlank(responseBodyStr)) {
                JSONObject jsonObject = JSON.parseObject(responseBodyStr);
                JSONArray jsonArray = jsonObject.getJSONArray(type);
                if (!jsonArray.isEmpty()) {
                    return jsonArray.getString(0);
                }
            }
        } catch (Exception e) {
            log.error("ocr decode error:", e);
        }
        return decodeStr;
    }

    private void formatTime(RoadTransportBackOcrVO roadTransportBackOcrVO) {
        String regStr1 = "\\d+-\\d+-\\d+";
        Pattern pattern1 = Pattern.compile(regStr1);
        Matcher matcher1 = pattern1.matcher(roadTransportBackOcrVO.getIssueDate());
        if (matcher1.find()) {
            roadTransportBackOcrVO.setIssueTime(DateUtil.dayToDate(roadTransportBackOcrVO.getIssueDate()));
        }
        String regStr2 = "\\d+年\\d+月\\d+日";
        Pattern pattern2 = Pattern.compile(regStr2);
        Matcher matcher2 = pattern2.matcher(roadTransportBackOcrVO.getIssueDate());
        if (matcher2.find()) {
            roadTransportBackOcrVO.setIssueTime(DateUtil.chinaDayToDate(roadTransportBackOcrVO.getIssueDate()));
        }
    }

    @Override
    @SneakyThrows
    public VehicleLicenseDeputyPageBackOcrVO vehicleLicenseDeputyPageBackOcr(String url) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getVehicleLicenseDeputyPageBackOcrServiceId())
                .add(Constants.IMG_URL_KEY, url)
                .build();
        String responseBodyStr = util.doPost(Constants.VEHICLE_LICENSE_DEPUTY_PAGE_BACK_OCR_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            JSONObject responseJson = JSON.parseObject(responseBodyStr);
            if (ObjectUtil.isNotNull(responseJson)) {
                String latestValidityPeriodStr = responseJson.getString("latestValidityPeriod");
                if (StringUtils.isNotBlank(latestValidityPeriodStr)) {
                    Date latestValidityPeriod = DateUtil.dateStrToDateEndDayOfMonth(latestValidityPeriodStr);
                    return VehicleLicenseDeputyPageBackOcrVO.builder().latestValidityPeriodStr(latestValidityPeriodStr).latestValidityPeriod(latestValidityPeriod).build();
                }
            }
        }
        return null;
    }

    @Override
    public RoadTransportQualificationCertificateOcrVO roadTransportQualificationCertificateOcr(String url) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getRoadTransportQualificationCertificateOcrServiceId())
                .add(Constants.IMG_URL_KEY, url)
                .build();
        String responseBodyStr = util.doPost(Constants.ROAD_TRANSPORT_QUALIFICATION_CERTIFICATE_OCR_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            return JSON.parseObject(responseBodyStr, RoadTransportQualificationCertificateOcrVO.class);
        }
        return null;
    }

    @Override
    public RoadTransportQualificationCertificateOcrVO roadTransportQualificationCertificateBackOcr(String url) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getRoadTransportQualificationCertificateBackOcrServiceId())
                .add(Constants.IMG_URL_KEY, url)
                .build();
        String responseBodyStr = util.doPost(Constants.ROAD_TRANSPORT_QUALIFICATION_CERTIFICATE_BACK_OCR_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            return JSON.parseObject(responseBodyStr, RoadTransportQualificationCertificateOcrVO.class);
        }
        return null;
    }

    @Override
    public BusinessLicenseOcrVO businessLicenseOcr(String url) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getBusinessLicenseOcrServiceId())
                .add(Constants.IMG_URL_KEY, url)
                .build();
        String responseBodyStr = util.doPost(Constants.BUSINESS_LICENSE_OCR_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            return JSON.parseObject(responseBodyStr, BusinessLicenseOcrVO.class);
        }
        return null;
    }
}

