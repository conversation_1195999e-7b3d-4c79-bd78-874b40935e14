package com.teyuntong.outer.export.service.service.biz.transport.service.impl;

import com.teyuntong.outer.export.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.outer.export.service.service.biz.transport.mybatis.mapper.TransportMainMapper;
import com.teyuntong.outer.export.service.service.biz.transport.pojo.TransportConvert;
import com.teyuntong.outer.export.service.service.biz.transport.pojo.TransportMainDTO;
import com.teyuntong.outer.export.service.service.biz.transport.service.TransportMainService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 运输信息表主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportMainServiceImpl implements TransportMainService {

    private final TransportMainMapper transportMainMapper;
    private final TransportConvert transportConvert;

    @Override
    public TransportMainDTO getById(Long id) {
        return transportConvert.convertDO2DTO(transportMainMapper.selectById(id));
    }
}
