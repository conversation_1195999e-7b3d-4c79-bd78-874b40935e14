package com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.entity.AutoCallTaskLog;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("common")
public interface AutoCallTaskLogMapper extends BaseMapper<AutoCallTaskLog> {

    int insert(AutoCallTaskLog config);

}