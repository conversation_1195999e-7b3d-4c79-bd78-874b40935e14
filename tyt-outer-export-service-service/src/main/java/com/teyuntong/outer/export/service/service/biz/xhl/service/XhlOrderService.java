package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlOrderEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 翔和翎 订单、运单相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlOrderService {


    /**
     * 新增订单
     *
     * @param orderEntity 订单实体类
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> saveOrder(XhlOrderEntity orderEntity) throws Exception;


    /**
     * 修改订单
     *
     * @param truckEntity 运单实体类
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> updateOrder(XhlOrderEntity truckEntity) throws Exception;


    /**
     * 查询订单
     *
     * @param companyName 公司名称
     * @param tpOrderNo   订单编号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryOrder(String companyName, String tpWaybillNo) throws Exception;


    /**
     * 派单
     *
     * @param orderEntity 订单信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> sendOrder(XhlOrderEntity orderEntity) throws Exception;

    /**
     * 运单发车
     *
     * @param orderEntity 运单信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> departWaybill(XhlOrderEntity orderEntity) throws Exception;

    /**
     * 运单签收
     *
     * @param orderEntity 运单信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> signWaybill(XhlOrderEntity orderEntity) throws Exception;

    /**
     * 运单删除
     *
     * @param orderEntity 运单信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> deleteWaybill(XhlOrderEntity orderEntity) throws Exception;

    /**
     * 运单修改
     *
     * @param orderEntity 运单信息实体
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> updateWaybill(XhlOrderEntity orderEntity) throws Exception;


    /**
     * 查询运单
     *
     * @param companyName 公司名称
     * @param tpWaybillNo 运单编号
     * @return 返回操作结果的封装对象
     * @throws Exception
     */
    XhlDataResult<Object> queryWaybill(String companyName, String tpWaybillNo) throws Exception;


}
