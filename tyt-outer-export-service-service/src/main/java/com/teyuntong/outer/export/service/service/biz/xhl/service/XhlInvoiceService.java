package com.teyuntong.outer.export.service.service.biz.xhl.service;

import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlInvoiceEntity;

/**
 * 翔和翎 发票相关业务接口
 *
 * <AUTHOR>
 * @since 2025/01/13 14:45
 */
public interface XhlInvoiceService {

    /**
     * 发票申请
     *
     * @param invoiceEntity 发票实体
     * @return 返回操作结果的封装对象
     */
    XhlDataResult<Object> applyInvoice(XhlInvoiceEntity invoiceEntity) throws Exception;

    /**
     * 修改运费
     *
     * @param invoiceEntity 发票实体
     * @return 返回操作结果的封装对象
     */
    XhlDataResult<Object> deleteInvoice(XhlInvoiceEntity invoiceEntity) throws Exception;

    /**
     * 查询发票申请
     *
     * @param invoiceApplyNo   发票申请单号
     * @return 返回查询结果的封装对象
     */
    XhlDataResult<Object> queryInvoice(String invoiceApplyNo) throws Exception;

}
