package com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 行驶证副页背面OCR结果类
 *
 * <AUTHOR>
 * @since 2024/04/26 14:03
 */
@Data
@Builder
public class RoadTransportBackOcrVO {

    /**
     * 道路运输证号
     */
    private String roadTransportCard;
    /**
     * 业户名称
     */
    private String name;
    /**
     * 车牌号码
     */
    private String plateNum;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 座(吨)位
     */
    private String maximumCapacity;
    /**
     * 车辆尺寸
     */
    private String vehicleSize;
    /**
     * 核发机关（非必有，依赖对应运输证板式）
     */
    private String issuingAuthority;
    /**
     * 发证日期
     */
    private String issueDate;
    /**
     * 发证日期
     */
    private Date issueTime;
    /**
     * 经营许可证
     */
    private String businessLicenseNo;
    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 相关字段的置信度信息，置信度越大，表示本次识别的对应字段的可靠性越高，
     * 在统计意义上，置信度越大，准确率越高。 置信度由算法给出，不直接等价于对应字段的准确率
     */
    private String confidence;
}
