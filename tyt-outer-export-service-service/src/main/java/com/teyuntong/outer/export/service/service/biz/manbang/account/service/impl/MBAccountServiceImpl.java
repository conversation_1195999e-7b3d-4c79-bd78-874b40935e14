package com.teyuntong.outer.export.service.service.biz.manbang.account.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBAccountDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBLicenseDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.service.MBAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBAccountInfoVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBLicenseVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.VehicleLicenseVO;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.VehicleLocationVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants.DECODE_VALUE_KEY;

/**
 * <AUTHOR>
 * @since 2025/04/15 11:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MBAccountServiceImpl implements MBAccountService {

    private final MBOpenPlatformUtil util;
    public static final MediaType jsonMediaType = MediaType.parse("application/json; charset=utf-8");
    @Override
    public List<MBAccountInfoVO> queryAccountsByMobile(MBAccountDTO mbAccountDTO) {

        log.info("查询满帮用户，请求参数：{}", JSON.toJSONString(mbAccountDTO));
        Map<String,String> param = new HashMap<>();
        param.put(Constants.MOBILE, mbAccountDTO.getMobile());
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));
        String responseBodyStr = util.doPostAccount(Constants.YMM_OPEN_PLATFORM_QUERY_ACCOUNTS_MOBILE_URL, body);
        log.info("查询满帮用户：{}，返回结果：{}", mbAccountDTO.getMobile(),responseBodyStr);
        List<MBAccountInfoVO> accountInfoVOList = null;
        if (StringUtils.isNotBlank(responseBodyStr)) {
            accountInfoVOList = JSON.parseArray(responseBodyStr, MBAccountInfoVO.class);
        }
        return accountInfoVOList;
    }

    @Override
    public MBLicenseVO queryLicenseByTypes(MBLicenseDTO mbLicenseDTO) {
        log.info("查询满帮车辆信息，请求参数：{}", JSON.toJSONString(mbLicenseDTO));
        Map<String,Object> param = new HashMap<>();
        param.put(Constants.ACCOUNT_ID, mbLicenseDTO.getAccountId());
        param.put(Constants.LICENSE_TYPES, mbLicenseDTO.getLicenseTypes());
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));
        String responseBodyStr = util.doPostAccount(Constants.YMM_OPEN_PLATFORM_QUERY_LICENSE_TYPES_URL, body);
        log.info("查询满帮车辆信息：{}，返回结果：{}", JSON.toJSONString(mbLicenseDTO),responseBodyStr);
        MBLicenseVO mbLicenseVO = null;
        if (StringUtils.isNotBlank(responseBodyStr)) {
            mbLicenseVO = JSON.parseObject(responseBodyStr, MBLicenseVO.class);
            VehicleLicenseVO vehicleLicense = mbLicenseVO.getVehicleLicense();
            if(Objects.nonNull(vehicleLicense)){
                vehicleLicense.setPlateNo(this.decode(vehicleLicense.getPlateNo(),"PLATE_NUMBER"));
            }
        }
        return mbLicenseVO;
    }


    private String decode(String decodeStr, String type) {
        if (StringUtils.isBlank(decodeStr)) {
            return decodeStr;
        }
        try {
            Map<String, Map<String, List<String>>> bodyMap = new HashMap<>();
            Map<String, List<String>> value = new HashMap<>();
            value.put(type, List.of(decodeStr));
            bodyMap.put(DECODE_VALUE_KEY, value);
            RequestBody body = RequestBody.create(jsonMediaType, JSON.toJSONString(bodyMap));

            String responseBodyStr = util.doPostForDecode(Constants.YMM_OPEN_PLATFORM_DECODE_URL, body);
            if (StringUtils.isNotBlank(responseBodyStr)) {
                JSONObject jsonObject = JSON.parseObject(responseBodyStr);
                JSONArray jsonArray = jsonObject.getJSONArray(type);
                if (!jsonArray.isEmpty()) {
                    return jsonArray.getString(0);
                }
            }
        } catch (Exception e) {
            log.error("ocr decode error:", e);
        }
        return decodeStr;
    }
}
