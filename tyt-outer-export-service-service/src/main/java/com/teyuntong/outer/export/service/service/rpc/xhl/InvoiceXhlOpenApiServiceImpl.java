package com.teyuntong.outer.export.service.service.rpc.xhl;

import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.error.XhlErrorCode;
import com.teyuntong.outer.export.service.client.invoice.xhl.dto.*;
import com.teyuntong.outer.export.service.client.invoice.xhl.service.InvoiceXhlOpenApiService;
import com.teyuntong.outer.export.service.client.invoice.xhl.vo.*;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.*;
import com.teyuntong.outer.export.service.service.biz.xhl.service.*;
import com.teyuntong.outer.export.service.service.biz.xhl.util.TytBeanUtil;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 开票-翔和翎开放接口服务实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 13:21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@EnableConfigurationProperties(XhlProperties.class)
public class InvoiceXhlOpenApiServiceImpl extends InvoiceXhlBaseService implements InvoiceXhlOpenApiService {

    @Autowired
    private XhlCarrierService xhlCarrierService;
    @Autowired
    private XhlDriverService xhlDriverService;
    @Autowired
    private XhlFreightService xhlFreightService;
    @Autowired
    private XhlInvoiceService xhlInvoiceService;
    @Autowired
    private XhlOrderService xhlOrderService;
    @Autowired
    private XhlTruckService xhlTruckService;

    private final XhlProperties xhlProperties;


    @Override
    public WebResult<XhlCarrierVO> saveCompany(XhlCarrierDTO xhlCarrierDTO) throws Exception {
        XhlCarrierEntity entity = TytBeanUtil.convertBean(xhlCarrierDTO, XhlCarrierEntity.class);
        XhlDataResult<Object> response = xhlCarrierService.saveCompany(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("新增托运人失败，companyName={}，状态码：{}，原因：{}", xhlCarrierDTO.getCompanyName(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlCarrierVO> updateCompany(XhlCarrierDTO xhlCarrierDTO) throws Exception {
        XhlCarrierEntity entity = TytBeanUtil.convertBean(xhlCarrierDTO, XhlCarrierEntity.class);
        XhlDataResult<Object> response = xhlCarrierService.updateCompany(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("修改托运人失败，companyName={}，状态码：{}，原因：{}", xhlCarrierDTO.getCompanyName(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlCarrierVO> queryCompany(String companyName) throws Exception {
        if (StringUtils.isAnyBlank(companyName)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlCarrierService.queryCompany(companyName);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlCarrierVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询托运人失败，companyName={}，状态码：{}，原因：{}", companyName, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlCarrierBankVO> saveCompanyBank(XhlCarrierBankDTO xhlCarrierBankDTO) throws Exception {
        XhlCarrierEntity entity = TytBeanUtil.convertBean(xhlCarrierBankDTO, XhlCarrierEntity.class);
        XhlDataResult<Object> response = xhlCarrierService.saveBank(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlCarrierBankVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            WebResult<XhlCarrierBankVO> xhlCarrierBankVOWebResult = fromXhlDataResult(result);
            xhlCarrierBankVOWebResult.setCode(CommonErrorCode.SUCCESS.getCode());
            return xhlCarrierBankVOWebResult;
        } else {
            log.error("托运人绑卡失败，companyName={}，状态码：{}，原因：{}", xhlCarrierBankDTO.getCompanyName(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlCarrierBankVO> deleteCompanyBank(XhlCarrierBankDTO xhlCarrierBankDTO) throws Exception {
        XhlCarrierEntity entity = TytBeanUtil.convertBean(xhlCarrierBankDTO, XhlCarrierEntity.class);
        XhlDataResult<Object> response = xhlCarrierService.deleteBank(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlCarrierBankVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("托运人解绑银行卡失败，companyName={}，状态码：{}，原因：{}", xhlCarrierBankDTO.getCompanyName(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlCarrierBankVO> queryCompanyBalance(String companyName) throws Exception {
        if (StringUtils.isAnyBlank(companyName)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlCarrierService.queryBalance(companyName);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlCarrierBankVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            WebResult<XhlCarrierBankVO> xhlCarrierBankVOWebResult = fromXhlDataResult(result);
            xhlCarrierBankVOWebResult.setCode(CommonErrorCode.SUCCESS.getCode());
            return xhlCarrierBankVOWebResult;
        } else {
            log.error("查询托运人余额失败，companyName={}，状态码：{}，原因：{}", companyName, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverVO> saveDriver(XhlDriverDTO xhlDriverDTO) throws Exception {
        XhlDriverEntity entity = TytBeanUtil.convertBean(xhlDriverDTO, XhlDriverEntity.class);
        XhlDataResult<Object> response = xhlDriverService.saveDriver(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("新增驾驶员失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverVO> updateDriver(XhlDriverDTO xhlDriverDTO) throws Exception {
        XhlDriverEntity entity = TytBeanUtil.convertBean(xhlDriverDTO, XhlDriverEntity.class);
        XhlDataResult<Object> response = xhlDriverService.updateDriver(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("修改驾驶员失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverVO> queryDriver(String idCardNo) throws Exception {
        if (StringUtils.isAnyBlank(idCardNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlDriverService.queryDriver(idCardNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询驾驶员信息失败，idCardNo={}，状态码：{}，原因：{}", idCardNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverBankVO> saveDriverBank(XhlDriverBankDTO xhlDriverBankDTO) throws Exception {
        XhlDriverEntity entity = TytBeanUtil.convertBean(xhlDriverBankDTO, XhlDriverEntity.class);
        XhlDataResult<Object> response = xhlDriverService.saveBank(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverBankVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("驾驶员绑卡失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverBankDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverBankVO> deleteDriverBank(XhlDriverBankDTO xhlDriverBankDTO) throws Exception {
        XhlDriverEntity entity = TytBeanUtil.convertBean(xhlDriverBankDTO, XhlDriverEntity.class);
        XhlDataResult<Object> response = xhlDriverService.deleteBank(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverBankVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("驾驶员解绑银行卡失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverBankDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlTruckVO> saveTruck(XhlTruckDTO xhlTruckDTO) throws Exception {
        XhlTruckEntity entity = TytBeanUtil.convertBean(xhlTruckDTO, XhlTruckEntity.class);
        XhlDataResult<Object> response = xhlTruckService.saveTruck(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
//            XhlDataResult<XhlTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
//            return fromXhlDataResult(result);
            return WebResult.success();
        } else {
            log.error("新增车辆失败，plateNumber={}，状态码：{}，原因：{}", xhlTruckDTO.getPlateNumber(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlTruckVO> updateTruck(XhlTruckDTO xhlTruckDTO) throws Exception {
        XhlTruckEntity entity = TytBeanUtil.convertBean(xhlTruckDTO, XhlTruckEntity.class);
        XhlDataResult<Object> response = xhlTruckService.updateTruck(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
//            XhlDataResult<XhlTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
//            return fromXhlDataResult(result);
            return WebResult.success();
        } else {
            log.error("修改车辆失败，plateNumber={}，状态码：{}，原因：{}", xhlTruckDTO.getPlateNumber(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlTruckVO> queryTruck(String plateNumber) throws Exception {
        if (StringUtils.isAnyBlank(plateNumber)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlTruckService.queryTruck(plateNumber);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询车辆信息失败，plateNumber={}，状态码：{}，原因：{}", plateNumber, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverTruckVO> saveDriverTruck(XhlDriverTruckDTO xhlDriverTruckDTO) throws Exception {
        XhlDriverTruckEntity entity = TytBeanUtil.convertBean(xhlDriverTruckDTO, XhlDriverTruckEntity.class);
        XhlDataResult<Object> response = xhlDriverService.saveDriverTruck(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            WebResult<XhlDriverTruckVO> xhlDriverTruckVOWebResult = fromXhlDataResult(result);
            xhlDriverTruckVOWebResult.setData(new XhlDriverTruckVO());
            return xhlDriverTruckVOWebResult;
        } else {
            log.error("新增人车合照信息失败，idCardNo={}，plateNumber={}，状态码：{}，原因：{}", xhlDriverTruckDTO.getIdCardNo(), xhlDriverTruckDTO.getPlateNumber(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverTruckVO> updateDriverTruck(XhlDriverTruckDTO xhlDriverTruckDTO) throws Exception {
        XhlDriverTruckEntity entity = TytBeanUtil.convertBean(xhlDriverTruckDTO, XhlDriverTruckEntity.class);
        XhlDataResult<Object> response = xhlDriverService.updateDriverTruck(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            WebResult<XhlDriverTruckVO> xhlDriverTruckVOWebResult = fromXhlDataResult(result);
            xhlDriverTruckVOWebResult.setData(new XhlDriverTruckVO());
            return xhlDriverTruckVOWebResult;
        } else {
            log.error("修改人车合照信息失败，idCardNo={}，plateNumber={}，状态码：{}，原因：{}", xhlDriverTruckDTO.getIdCardNo(), xhlDriverTruckDTO.getPlateNumber(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverTruckVO> queryDriverTruck(String idCardNo, String plateNumber) throws Exception {
        if (StringUtils.isAnyBlank(idCardNo, plateNumber)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlDriverService.queryDriverTruck(idCardNo, plateNumber);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverTruckVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询人车合照信息失败，idCardNo={}，plateNumber={}，状态码：{}，原因：{}", idCardNo, plateNumber, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverContractEmpowerVO> saveEmpowerDriver(XhlDriverDTO xhlDriverDTO) throws Exception {
        XhlDriverEntity entity = TytBeanUtil.convertBean(xhlDriverDTO, XhlDriverEntity.class);
        XhlDataResult<Object> response = xhlDriverService.saveEmpowerDriver(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverContractEmpowerVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("新增司机授权（委托代征）失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverVO> queryEmpowerDriver(String idCardNo) throws Exception {
        if (StringUtils.isAnyBlank(idCardNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlDriverService.queryEmpowerDriver(idCardNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询司机授权（委托代征）失败，idCardNo={}，状态码：{}，原因：{}", idCardNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlOrderVO> saveOrder(XhlOrderDTO xhlOrderDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlOrderDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.saveOrder(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlOrderVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("新增订单失败，companyName={}，tpOrderNo={}，状态码：{}，原因：{}", xhlOrderDTO.getCompanyName(), xhlOrderDTO.getTpOrderNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlOrderVO> updateOrder(XhlOrderDTO xhlOrderDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlOrderDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.updateOrder(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlOrderVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("修改订单失败，companyName={}，tpOrderNo={}，状态码：{}，原因：{}", xhlOrderDTO.getCompanyName(), xhlOrderDTO.getTpOrderNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlOrderVO> queryOrder(String companyName, String tpOrderNo) throws Exception {
        if (StringUtils.isAnyBlank(companyName, tpOrderNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlOrderService.queryOrder(companyName, tpOrderNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlOrderVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询订单信息失败，companyName={}，tpOrderNo={}，状态码：{}，原因：{}", companyName, tpOrderNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlSendVO> sendOrder(XhlWayBillDTO xhlWayBillDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlWayBillDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.sendOrder(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlSendVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("派单失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlWayBillDTO.getCompanyName(), xhlWayBillDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> departWaybill(XhlWayBillDepartDTO xhlOrderDepartDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlOrderDepartDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.departWaybill(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("运单发车失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlOrderDepartDTO.getCompanyName(), xhlOrderDepartDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> signWaybill(XhlWayBillSignDTO xhlOrderSignDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlOrderSignDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.signWaybill(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("运单签收失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlOrderSignDTO.getCompanyName(), xhlOrderSignDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> deleteWaybill(XhlWayBillDTO xhlWayBillDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlWayBillDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.deleteWaybill(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("删除运单失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlWayBillDTO.getCompanyName(), xhlWayBillDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> updateWaybill(XhlWayBillDTO xhlWayBillDTO) throws Exception {
        XhlOrderEntity entity = TytBeanUtil.convertBean(xhlWayBillDTO, XhlOrderEntity.class);
        XhlDataResult<Object> response = xhlOrderService.updateWaybill(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("修改运单失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlWayBillDTO.getCompanyName(), xhlWayBillDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> queryWaybill(String companyName, String tpWaybillNo) throws Exception {
        if (StringUtils.isAnyBlank(companyName, tpWaybillNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlOrderService.queryWaybill(companyName, tpWaybillNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlWaybillVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询运单信息失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", companyName, tpWaybillNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlWaybillVO> saveWaybill(XhlOrderAndWayBillDTO xhlOrderAndWayBillDTO) throws Exception {
        XhlOrderEntity orderEntity = TytBeanUtil.convertBean(xhlOrderAndWayBillDTO, XhlOrderEntity.class);
        //创建订单
        XhlDataResult<Object> response = xhlOrderService.saveOrder(orderEntity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            //派单
            XhlDataResult<Object> res = xhlOrderService.sendOrder(orderEntity);
            if (XhlErrorCode.XHL_SUCCESS.getCode().equals(res.getCode())) {
                XhlDataResult<XhlWaybillVO> result = TytBeanUtil.convertBean(res, XhlDataResult.class);
                return fromXhlDataResult(result);
            } else {
                log.error("派单失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlOrderAndWayBillDTO.getCompanyName(), xhlOrderAndWayBillDTO.getTpWaybillNo(), res.getCode(), res.getMsg());
                throw BusinessException.createException(res.getCode(), res.getMsg());
            }
        } else {
            log.error("新增订单失败，companyName={}，tpOrderNo={}，状态码：{}，原因：{}", xhlOrderAndWayBillDTO.getCompanyName(), xhlOrderAndWayBillDTO.getTpOrderNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlFreightVO> payMoney(XhlFreightDTO xhlFreightDTO) throws Exception {
        XhlFreightEntity entity = TytBeanUtil.convertBean(xhlFreightDTO, XhlFreightEntity.class);
        XhlDataResult<Object> response = xhlFreightService.payMoney(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlFreightVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("运费打款失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlFreightDTO.getCompanyName(), xhlFreightDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlFreightVO> updateCharge(XhlFreightDTO xhlFreightDTO) throws Exception {
        XhlFreightEntity entity = TytBeanUtil.convertBean(xhlFreightDTO, XhlFreightEntity.class);
        XhlDataResult<Object> response = xhlFreightService.updateCharge(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlFreightVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("修改运费失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", xhlFreightDTO.getCompanyName(), xhlFreightDTO.getTpWaybillNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlFreightVO> queryPay(String companyName, String tpWaybillNo) throws Exception {
        if (StringUtils.isAnyBlank(companyName, tpWaybillNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlFreightService.queryPay(companyName, tpWaybillNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlFreightVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询运费支付结果失败，companyName={}，tpWaybillNo={}，状态码：{}，原因：{}", companyName, tpWaybillNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlInvoiceVO> applyInvoice(XhlInvoiceDTO xhlInvoiceDTO) throws Exception {
        XhlInvoiceEntity entity = TytBeanUtil.convertBean(xhlInvoiceDTO, XhlInvoiceEntity.class);
        XhlDataResult<Object> response = xhlInvoiceService.applyInvoice(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("申请发票失败，companyName={}，invoiceApplyNo={}，状态码：{}，原因：{}", xhlInvoiceDTO.getCompanyName(), xhlInvoiceDTO.getInvoiceApplyNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlInvoiceVO> deleteInvoice(XhlInvoiceDTO xhlInvoiceDTO) throws Exception {
        XhlInvoiceEntity entity = TytBeanUtil.convertBean(xhlInvoiceDTO, XhlInvoiceEntity.class);
        XhlDataResult<Object> response = xhlInvoiceService.deleteInvoice(entity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success();
        } else {
            log.error("发票取消失败，invoiceApplyNo={}，状态码：{}，原因：{}", xhlInvoiceDTO.getInvoiceApplyNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlInvoiceVO> queryInvoice(String invoiceApplyNo) throws Exception {
        if (StringUtils.isAnyBlank(invoiceApplyNo)) {
            throw new BusinessException(XhlErrorCode.XHL_PARAM_LACK_ERROR);
        }
        XhlDataResult<Object> response = xhlInvoiceService.queryInvoice(invoiceApplyNo);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlInvoiceVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("查询发票申请失败，invoiceApplyNo={}，状态码：{}，原因：{}", invoiceApplyNo, response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<XhlDriverVO> saveAppFaceAuth(XhlDriverDTO xhlDriverDTO) {
        XhlFaceAuthEntity xhlFaceAuthEntity = BeanUtil.copyProperties(xhlDriverDTO, XhlFaceAuthEntity.class);
        xhlFaceAuthEntity.setCallBackUrl(xhlProperties.getDriverFaceAuthCallBackUrl());
        XhlDataResult<Object> response = xhlDriverService.saveAppFaceAuth(xhlFaceAuthEntity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            XhlDataResult<XhlDriverVO> result = TytBeanUtil.convertBean(response, XhlDataResult.class);
            return fromXhlDataResult(result);
        } else {
            log.error("司机人脸认证失败，idCardNo={}，状态码：{}，原因：{}", xhlDriverDTO.getIdCardNo(), response.getCode(), response.getMsg());
            throw BusinessException.createException(response.getCode(), response.getMsg());
        }
    }

    @Override
    public WebResult<Boolean> queryAppFaceResult(XhlDriverDTO xhlDriverDTO) {
        XhlFaceAuthEntity xhlFaceAuthEntity = BeanUtil.copyProperties(xhlDriverDTO, XhlFaceAuthEntity.class);
        xhlFaceAuthEntity.setOrderId(xhlDriverDTO.getFaceAuthOrderId());
        XhlDataResult<Object> response = xhlDriverService.queryAppFaceResult(xhlFaceAuthEntity);
        if (XhlErrorCode.XHL_SUCCESS.getCode().equals(response.getCode())) {
            return WebResult.success(Boolean.TRUE);
        } else {
            return WebResult.success(Boolean.FALSE);
        }
    }

}
