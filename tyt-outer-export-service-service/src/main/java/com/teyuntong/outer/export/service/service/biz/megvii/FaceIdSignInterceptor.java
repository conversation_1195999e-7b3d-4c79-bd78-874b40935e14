package com.teyuntong.outer.export.service.service.biz.megvii;

import com.google.common.collect.Maps;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @since 2024/01/11 13:32
 */
@Slf4j
@Component
@EnableConfigurationProperties(MegviiFaceIdProperties.class)
public class FaceIdSignInterceptor implements Interceptor {

    private final MegviiFaceIdProperties megviiFaceIdProperties;

    public FaceIdSignInterceptor(MegviiFaceIdProperties megviiFaceIdProperties) {
        this.megviiFaceIdProperties = megviiFaceIdProperties;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request request = chain.request();
        RequestBody body = request.body();
        if (body != null) {

            String sign = genSign(megviiFaceIdProperties);
            if (body instanceof MultipartBody) {
                MultipartBody multipartBody = (MultipartBody) body;
                MultipartBody.Builder builder = new MultipartBody.Builder();
                builder.setType(multipartBody.type());
                multipartBody.parts().forEach(builder::addPart);
                builder.addFormDataPart("sign", sign);
                builder.addFormDataPart("sign_version", "hmac_sha1");

                Request newRequest = request.newBuilder().method(request.method(), builder.build()).build();
                return chain.proceed(newRequest);
            } else {
                MediaType formMediaType = MediaType.get("application/x-www-form-urlencoded");
                if (Objects.equals(body.contentType(), formMediaType)) {
                    // 表单body
                    Buffer buffer = new Buffer();
                    body.writeTo(buffer);
                    Map<String, String> paramMap = strFormData2Map(buffer.readUtf8());
                    paramMap.put("sign", sign);
                    paramMap.put("sign_version", "hmac_sha1");

                    RequestBody requestBody =
                            RequestBody.create(dataMap2FormStr(paramMap).getBytes(StandardCharsets.UTF_8),
                                    formMediaType);
                    request = request.newBuilder().method(request.method(), requestBody).build();
                    return chain.proceed(request);
                }
            }
        }

        return chain.proceed(request);
    }

    private Map<String, String> strFormData2Map(String formData) {
        Map<String, String> map = Maps.newHashMap();

        for (String data : formData.split("&")) {
            String[] split = data.split("=");
            if (split.length == 2) {
                map.put(split[0], split[1]);
            }
        }
        return map;
    }

    private String dataMap2FormStr(Map<String, String> formData) {
        StringJoiner stringJoiner = new StringJoiner("&");
        formData.forEach((k, v) -> stringJoiner.add(k + "=" + v));
        return stringJoiner.toString();
    }

    /**
     * 生成签名字段
     */
    private String genSign(MegviiFaceIdProperties megviiFaceIdProperties) {
        try {
            return genSign(megviiFaceIdProperties.getAppKey(),
                    megviiFaceIdProperties.getAppSecret(),
                    megviiFaceIdProperties.getSignExpireDuration());
        } catch (Exception e) {
            throw TytException.createException(e);
        }
    }

    /**
     * 生成签名字段
     */
    private String genSign(String apiKey, String secretKey, Duration expired) throws Exception {
        long now = System.currentTimeMillis() / 1000;
        long expire = expired.plusSeconds(now).toMillis() / 1000;
        int rdm = ThreadLocalRandom.current().nextInt(1000000, 999999999);
        String plainText = String.format("a=%s&b=%d&c=%d&d=%d", apiKey, expire, now, rdm);
        byte[] hmacDigest = hmacSha1(plainText, secretKey);
        byte[] signContent = new byte[hmacDigest.length + plainText.getBytes().length];
        System.arraycopy(hmacDigest, 0, signContent, 0, hmacDigest.length);
        System.arraycopy(plainText.getBytes(), 0, signContent, hmacDigest.length,
                plainText.getBytes().length);
        return encodeToBase64(signContent).replaceAll("[\\s*\t\n\r]", "");
    }

    /**
     * 生成 base64 编码
     *
     * @param binaryData binaryData
     * @return string
     */
    private String encodeToBase64(byte[] binaryData) {
        return Base64.getEncoder().encodeToString(binaryData);
    }

    /**
     * 生成 HmacSHA1 签名
     */
    private byte[] hmacSha1(byte[] binaryData, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA1");
        mac.init(secretKey);
        return mac.doFinal(binaryData);
    }

    /**
     * 生成 HmacSHA1 签名
     */
    private byte[] hmacSha1(String plainText, String key) throws Exception {
        return hmacSha1(plainText.getBytes(), key);
    }
}
