package com.teyuntong.outer.export.service.service.biz.megvii;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/01/11 11:18
 */
@Data
@ConfigurationProperties(prefix = "megvii.face-id")
public class MegviiFaceIdProperties {

    /**
     * ak
     */
    private String appKey;
    /**
     * sk
     */
    private String appSecret;

    /**
     * sign过期时间
     */
    private Duration signExpireDuration = Duration.ofMinutes(30);
}
