package com.teyuntong.outer.export.service.service.biz.hbwj.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.FileUpLoadResp;
import com.teyuntong.outer.export.service.client.tripartite.bean.TripartiteTokenBean;
import com.teyuntong.outer.export.service.service.biz.hbwj.client.HBWJClient;
import com.teyuntong.outer.export.service.service.biz.hbwj.client.HBWJUploadFileClient;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJBaseResult;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJDataResult;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJUploadImgService;
import com.teyuntong.outer.export.service.service.common.property.MjSignProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/23 14:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties(MjSignProperty.class)
public class HBWJUploadImgServiceImpl implements HBWJUploadImgService, InitializingBean {


    private static final String REDIS_WJ_ACCESS_TOKEN_KEY = "WJ_ACCESS_TOKEN";

    private static final List<String> encryptedFields = Arrays.asList("idCardNum","phone","password","idCardNo",
            "idCard","qualificationCardNo","bankIdCardNo","legalPersonIdCard","bankPhone","adminPhone",
            "financeContactTel","businessContactTel","userPhone","payeePhone","driverPhone","oldPayPwd","newPayPwd","confirmPayPwd");

    private ObjectMapper mapper;

    private  HBWJUploadFileClient hbwjUploadFileClient;


    private final MjSignProperty mjSignProperty;

    private final StringRedisTemplate stringRedisTemplate;

    private final HBWJClient hBWJClient;


    @Override
    public FileUpLoadResp uploadFile(MultipartBody.Part part) throws Exception {
        Response<HBWJDataResult<FileUpLoadResp>> response = hbwjUploadFileClient.fileUpload(part).execute();
        log.info("【我家-图片上传】返回结果：{}",JSON.toJSONString(response.body()));
        if (!response.isSuccessful()) {
            throw new BusinessException(CommonErrorCode.ERROR_SYS_BUSY);
        }
        FileUpLoadResp data = getData(response);
        log.info("【我家-图片上传】返回结果：{}",data.toString());
        return data;
    }


    @Override
    public void afterPropertiesSet() throws Exception {

        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        //拦截器
        Interceptor merchantApiInterceptor = chain -> {
            Request request = chain.request();
            RequestBody requestBody = request.body();

            String token = getToken();
            Request newRequest = request.newBuilder()
                    .url(request.url())
                    .header("Authorization", token)
                    .header("Content-Type", "multipart/form-data")
                    .build();
            return chain.proceed(newRequest);
        };

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(5))
                .readTimeout(Duration.ofSeconds(5))
                .addInterceptor(merchantApiInterceptor)
                .build();

        Retrofit merchantRetrofit = new Retrofit.Builder()
                .baseUrl(mjSignProperty.getWjApiUrl())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(okHttpClient)
                .build();

        hbwjUploadFileClient = merchantRetrofit.create(HBWJUploadFileClient.class);
    }

    public String getToken() throws IOException {
        String token = stringRedisTemplate.opsForValue().get(REDIS_WJ_ACCESS_TOKEN_KEY);
        if (StringUtils.isNotEmpty(token)) {
            return "Bearer " + token;
        }
        String clientCredentials = mjSignProperty.getClientId() + ":" + mjSignProperty.getClientSecret();
        String encodedCredentials = "Basic " + Base64.getEncoder().encodeToString(clientCredentials.getBytes());
        TripartiteTokenBean bean = new TripartiteTokenBean();
        bean.setClient_id(mjSignProperty.getClientId());
        bean.setClient_secret(mjSignProperty.getClientSecret());
        retrofit2.Response<HBWJBaseResult<Object>> response = hBWJClient.getToken(encodedCredentials, "client_credentials",
                mjSignProperty.getClientId(), mjSignProperty.getClientSecret()).execute();
        HBWJBaseResult<Object> result = response.body();
        token = result.getAccess_token();
        log.info("调用三方token【{}】", token);
        stringRedisTemplate.opsForValue().set(REDIS_WJ_ACCESS_TOKEN_KEY, token, Duration.ofMinutes(30));
        return "Bearer " + token;
    }

    /**
     * 将HBWJDataResult转换为WebResult
     */
    public  <T> WebResult<T> fromHBWJDataResult(HBWJDataResult<T> hbwjDataResult) {
        if (Objects.isNull(hbwjDataResult)) {
            return WebResult.error(CommonErrorCode.INTERNAL_ERROR, null);
        }
        WebResult<T> result = new WebResult<>();
        result.setCode(hbwjDataResult.getCode().toString());
        result.setMsg(StringUtils.isNotEmpty(hbwjDataResult.getInfo()) ? hbwjDataResult.getInfo() : hbwjDataResult.getMsg());
        result.setData(hbwjDataResult.getData());
        return result;
    }
    private <T> T getData(Response<HBWJDataResult<T>> response) {
        if (!response.isSuccessful()) {
            return null;
        }
        HBWJDataResult<T> bodyResult = response.body();
        if (HBWJDataResult.checkSuccess(bodyResult)) {
            return bodyResult.getData();
        }
        return null;
    }
}
