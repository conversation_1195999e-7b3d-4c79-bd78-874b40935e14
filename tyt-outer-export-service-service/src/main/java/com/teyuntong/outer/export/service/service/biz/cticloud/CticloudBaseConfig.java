package com.teyuntong.outer.export.service.service.biz.cticloud;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/9 下午3:43
 */
@Data
public class CticloudBaseConfig {

    /**
     * 基础url format字符串
     */
    private String baseUrlFormat;

    /**
     * cticloud平台区域
     */
    private String region;

    /**
     * 接口版本
     */
    private String version;

    /**
     * 验证类型
     * 取值1，2； 取值说明：
     * validateType=1时使用部门编号(departmentId)进行验证；
     * validateType=2时使用呼叫中心编号(enterpriseId)进行验证；
     */
    private Integer validateType;

    /**
     * 部门编号
     */
    private String departmentId;

    /**
     * 呼叫中心编号
     */
    private String enterpriseId;

    /**
     * 部门授权码
     */
    private String token;

    /**
     * appId
     */
    private String appId;
}
