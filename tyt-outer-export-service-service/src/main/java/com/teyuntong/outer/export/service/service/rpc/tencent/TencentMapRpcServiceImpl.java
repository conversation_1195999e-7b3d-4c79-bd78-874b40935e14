package com.teyuntong.outer.export.service.service.rpc.tencent;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.tencent.TencentMapRpcService;
import com.teyuntong.outer.export.service.service.biz.tencent.service.TencentMapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 腾讯地图RPC服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/25 09:44
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TencentMapRpcServiceImpl implements TencentMapRpcService {
    private final TencentMapService tencentMapService;

    /**
     * 腾讯货车导航
     */
    @Override
    public String navigationTruck(DistanceRpcDTO dto) {
        return tencentMapService.navigateTruck(dto);
    }
}
