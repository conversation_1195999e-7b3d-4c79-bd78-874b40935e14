package com.teyuntong.outer.export.service.service.biz.ocr.enums;

import lombok.Getter;

/**
 * 证件照正反面
 *
 * <AUTHOR>
 * @date 2022/11/16 14:10
 */
public enum ImgSideEnum {

    front("正面"),
    back("反面"),
    ;

    @Getter
    private String zhName;

    ImgSideEnum(String zhName) {
        this.zhName = zhName;
    }

    public boolean equalsCode(String reqCode) {
        if(reqCode == null){
            return false;
        }
        return this.name().equals(reqCode);
    }

}