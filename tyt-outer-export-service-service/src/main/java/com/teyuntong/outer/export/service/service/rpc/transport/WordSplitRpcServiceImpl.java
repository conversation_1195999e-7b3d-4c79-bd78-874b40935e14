package com.teyuntong.outer.export.service.service.rpc.transport;

import com.teyuntong.outer.export.service.client.transport.service.WordSplitRpcService;
import com.teyuntong.outer.export.service.service.biz.transport.service.WordSplitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分词提取标准货物名称
 *
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WordSplitRpcServiceImpl implements WordSplitRpcService {
    @Autowired
    private WordSplitService wordSplitService;


    @Override
    public List<String> splitContent(String content) {
        List<String> nameList = wordSplitService.splitContent(content);
        return nameList;
    }
}
