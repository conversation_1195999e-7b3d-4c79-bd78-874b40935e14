package com.teyuntong.outer.export.service.service.rpc.enterprise;

import com.teyuntong.outer.export.service.client.enterprise.service.EnterpriseAccountOuterRpcService;
import com.teyuntong.outer.export.service.client.enterprise.vo.*;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.EnterpriseAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 企业相关Controller
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@RestController
@RequiredArgsConstructor
public class EnterpriseAccountOuterRpcServiceImpl implements EnterpriseAccountOuterRpcService {

    private final EnterpriseAccountService enterpriseAccountService;


    @Override
    public CreateEnterpriseApiResp createEnterprise(@RequestBody @Validated EnterpriseAccountApiReq req) {
        return enterpriseAccountService.createEnterprise(req);
    }


    @Override
    public Boolean enterpriseOpenAccount(@RequestBody @Validated EnterpriseAccountApiReq req) {
        return enterpriseAccountService.enterpriseOpenAccount(req);
    }

    @Override
    public EnterpriseOpenNetAccountApiResp enterpriseOpenNetAccount(@RequestBody @Validated EnterpriseAccountApiReq req){
        return enterpriseAccountService.enterpriseOpenNetAccount(req);
    }

    @Override
    public EnterpriseOpenNetAccountApiResp queryEnterpriseNetAccountInfo(@RequestBody @Validated EnterpriseAccountApiReq req) {
        return enterpriseAccountService.queryEnterpriseNetAccountInfo(req);
    }


    @Override
    public QueryEnterpriseBalanceApiResp queryEnterpriseBalance(@RequestBody @Validated QueryEnterpriseBalanceApiReq req) {
        return enterpriseAccountService.queryEnterpriseBalance(req);
    }

    @Override
    public ResultMsgBean queryEnterpriseBill(QueryEnterpriseBillApiReq req) {
        return enterpriseAccountService.queryEnterpriseBill(req);
    }
}
