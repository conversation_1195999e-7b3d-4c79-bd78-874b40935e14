package com.teyuntong.outer.export.service.service.common.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 置类
 *
 * <AUTHOR>
 * @since 2024-7-26 18:18:47
 */
@Data
@ConfigurationProperties(prefix = "group.ocr")
public class GroupOcrProperties {

    /**
     * 道运证OCR  serviceId
     */
    private String roadTransportBackOcrServiceId;

    /**
     * 行驶证副页背面OCR  serviceId
     */
    private String vehicleLicenseDeputyPageBackOcrServiceId;

    /**
     * 道路运输从业资格证主页OCR  serviceId
     */
    private String roadTransportQualificationCertificateOcrServiceId;

    /**
     * 道路运输从业资格证副页OCR  serviceId
     */
    private String roadTransportQualificationCertificateBackOcrServiceId;

    /**
     * 道路运输证验真  serviceId
     */
    private String roadTransportVerifyServiceId;

    /**
     * 道路运输道路运输从业资格证验真  serviceId
     */
    private String roadTransportQcVerifyServiceId;

    /**
     * 营业执照OCR  serviceId
     */
    private String businessLicenseOcrServiceId;
}
