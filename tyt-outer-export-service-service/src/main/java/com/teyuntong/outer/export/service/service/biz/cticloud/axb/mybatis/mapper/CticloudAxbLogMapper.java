package com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.CticloudAxbLog;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("common")
public interface CticloudAxbLogMapper extends BaseMapper<CticloudAxbLog> {

    /**
     * 插入数据
     * @param cticloudAxbLog
     * @return
     */
    int insertSelective(CticloudAxbLog cticloudAxbLog);
}