package com.teyuntong.outer.export.service.service.biz.ocr.service;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/4/24 16:09
 */
public interface BaiduOcrService {

    /**
     * 基于json path 获取对应文本值
     * 本方法只支持对象，不支持数组
     * @param jsonObject
     * @param keyArray
     * @return
     */
    String getJsonString(JSONObject jsonObject, String ... keyArray);

    /**
     * 获取百度 accessToken.
     * @return
     */
    String getAccessToken();

    /**
     * 身份证-正面面
     **/
    OcrIdCardFrontVo idCardFrontOcr(String url);

    /**
     * 身份证-国徽面
     **/
    OcrIdCardBackVo idCardBackOcr(String url);

    /**
     * 驾驶证-正页
     **/
    DriverLicenseFrontVo driverLicenseFrontOcr(String url);

    /**
     * 驾驶证-副页
     **/
    DriverLicenseBackVo driverLicenseBackOcr(String url);

    /**
     * 行驶证-主页
     **/
    VehicleLicenseFrontVo vehicleLicenseFrontOcr(String url);

    /**
     * 行驶证-副页
     **/
    VehicleLicenseBackVo vehicleLicenseBackOcr(String url);

    /**
     * 营业执照
     **/
    OcrBusinessLicenseVo businessLicenseOcr(String url);

    /**
     * 道路运输证
     * @param url
     * @return
     */
    RoadTransportVo roadTransportOcr(String url);

}
