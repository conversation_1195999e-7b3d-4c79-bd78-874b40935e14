package com.teyuntong.outer.export.service.service.biz.manbang.location.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LocationTraceDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.service.LocationService;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.*;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import com.teyuntong.outer.export.service.service.common.property.LocationProperties;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

import static com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants.DECODE_VALUE_KEY;

/**
 * <AUTHOR>
 * @since 2024/12/17 13:53
 */

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(LocationProperties.class)
public class LocationServiceImpl implements LocationService {

    private final LocationProperties locationProperties;

    public static final MediaType jsonMediaType = MediaType.parse("application/json; charset=utf-8");

    private final MBOpenPlatformUtil util;

    /**
     * 双边定位
     *
     */
    @Override
    public List<VehicleLocationVO> getVehicleLastLocation(List<LastLocationDTO> lastLocationDTOList) {
        log.info("获取车辆双边定位信息，请求参数：{}", JSON.toJSONString(lastLocationDTOList));
        Map<String,List<LastLocationDTO>> param = new HashMap<>();
        param.put(Constants.VEHICLE_INFO_LIST, lastLocationDTOList);
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));

        String responseBodyStr = util.doPostLocation(Constants.LAST_LOCATION_URL, body);
        log.info("获取车辆双边定位信息，返回结果：{}", responseBodyStr);
        List<VehicleLocationVO> vehicleLocationVOList = null;
        if (StringUtils.isNotBlank(responseBodyStr)) {
            vehicleLocationVOList = JSON.parseArray(responseBodyStr, VehicleLocationVO.class);

            if(CollUtil.isNotEmpty(vehicleLocationVOList)){
                vehicleLocationVOList.forEach(vehicleLocationVO -> {
                    if(StringUtils.isNotBlank(vehicleLocationVO.getVehicleNo())){
                        vehicleLocationVO.setVehicleNo(this.decode(vehicleLocationVO.getVehicleNo(),"PLATE_NUMBER"));
                    }
                });
            }
        }
        return vehicleLocationVOList;
    }

    /**
     * 单边定位
     */
    @Override
    public List<SinoiovVehicleLastLocationVO> getVehicleSinoiovLastLocation(List<LastLocationDTO> lastLocationDTOList) {
        log.info("获取车辆单边定位信息，请求参数：{}", JSON.toJSONString(lastLocationDTOList));

        List<SinoiovLastLocationDTO> sinoiovLastLocationDTOS = new ArrayList<>();
        for (LastLocationDTO lastLocationDTO: lastLocationDTOList) {
            SinoiovLastLocationDTO sinoiovLastLocationDTO = new SinoiovLastLocationDTO();
            sinoiovLastLocationDTO.setVehicle(lastLocationDTO.getVehicleNo());
            sinoiovLastLocationDTO.setColor(lastLocationDTO.getVehicleColor());
            sinoiovLastLocationDTOS.add(sinoiovLastLocationDTO);
        }
        Map<String,Object> param = new HashMap<>();
        param.put(Constants.APP_SERVICE_ID_KEY, locationProperties.getLastLocationServiceId());
        param.put(Constants.VEHICLE_NOS, sinoiovLastLocationDTOS);
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));

        String responseBodyStr = util.doPostLocation(Constants.LIST_SINOIOV_LAST_LOCATION_URL, body);
        log.info("获取车辆单边定位信息，返回结果：{}", responseBodyStr);
        List<SinoiovVehicleLastLocationVO> sinoiovVehicleLastLocationVOList = null;
        if (StringUtils.isNotBlank(responseBodyStr)) {
            sinoiovVehicleLastLocationVOList = JSON.parseArray(responseBodyStr, SinoiovVehicleLastLocationVO.class);


            if(CollUtil.isNotEmpty(sinoiovVehicleLastLocationVOList)){
                sinoiovVehicleLastLocationVOList.forEach(sinoiovVehicleLastLocationVO -> {
                    if(StringUtils.isNotBlank(sinoiovVehicleLastLocationVO.getVehicle())){
                        sinoiovVehicleLastLocationVO.setVehicle(this.decode(sinoiovVehicleLastLocationVO.getVehicle(),"PLATE_NUMBER"));
                    }
                });
            }
        }
        return sinoiovVehicleLastLocationVOList;
    }


    /**
     * 单/双边车辆轨迹
     *
     */
    @Override
    public LocationTraceVO getLocationTrace(LocationTraceDTO locationTraceDTO) {
        log.info("获取车辆轨迹信息，请求参数：{}", JSON.toJSONString(locationTraceDTO));

        Map<String,Object> param = new HashMap<>();

        if(Objects.nonNull(locationTraceDTO.getUserId())){
            // 用户授权,将查询到的用户id传入
            param.put(Constants.USER_ID, locationTraceDTO.getUserId());
        }

        param.put(Constants.VEHICLE_NO, locationTraceDTO.getVehicleNo());
        param.put(Constants.START_TIME, locationTraceDTO.getStartTime());
        param.put(Constants.END_TIME, locationTraceDTO.getEndTime());
        param.put(Constants.CHARGE_CHANNELS, locationTraceDTO.getChargeChannels());
        param.put(Constants.APP_SERVICE_ID_KEY, locationProperties.getTraceServiceId());
        log.info("接口请求参数：{}", JSON.toJSONString(param));
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));

        String responseBodyStr = util.doPostLocation(Constants.LOCATION_TRACE_URL, body);
        log.info("获取车辆轨迹信息，返回结果：{}", responseBodyStr);
        LocationTraceVO locationTraceVO = null;
        if (StringUtils.isNotBlank(responseBodyStr)) {
            locationTraceVO = JSON.parseObject(responseBodyStr, LocationTraceVO.class);
        }
        return locationTraceVO;
    }


    /**
     * app最新位置
     *
     */
    @Override
    public List<AppLastPositionVO> getAppLastPosition(List<Long> userIds) {
        log.info("获取app最新位置，请求参数：{}", JSON.toJSONString(userIds));

        Map<String,Object> param = new HashMap<>();
        param.put(Constants.USER_IDS, userIds);
        RequestBody body = RequestBody.create(jsonMediaType,JSON.toJSONString(param));

        String responseBodyStr = util.doPostAppPosition(Constants.APP_LAST_POSITION_URL, body);
        log.info("获取app最新位置，返回结果：{}", responseBodyStr);
        List<AppLastPositionVO> appLastPositionVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(responseBodyStr)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 解析 JSON 字符串为 LocationInfo 列表
                AppLastPositionVO[] appLastPositionVOS = objectMapper.readValue(responseBodyStr, AppLastPositionVO[].class);
                if(Objects.nonNull(appLastPositionVOS) && appLastPositionVOS.length > 0){
                    appLastPositionVOList = Arrays.asList(appLastPositionVOS);
                }
            }catch (Exception e){
                log.error("解析app最新位置json异常：",e);
            }
        }
        return appLastPositionVOList;
    }




    /**
     * 用户信息，授权信息
     *
     */
    @Override
    public List<LbsAuthVO> getLbsAuth(String vehicleNo) {
        List<LbsAuthVO> lbsAuthVOS = null;
        log.info("获取用户及授权，请求参数：{}", JSON.toJSONString(vehicleNo));
        Map<String, Object> param = new HashMap<>();
        param.put(Constants.TRUCK_NUMBER, vehicleNo);
        RequestBody body = RequestBody.create(jsonMediaType, JSON.toJSONString(param));

        String responseBodyStr = util.doPostLbsAuth(Constants.LBS_AUTH_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            lbsAuthVOS = JSON.parseArray(responseBodyStr, LbsAuthVO.class);
        }
        log.info("获取用户及授权，返回结果：{}", JSON.toJSONString(lbsAuthVOS));
        return lbsAuthVOS;
    }


    @Data
    public static class SinoiovLastLocationDTO {
        private String vehicle;
        private Integer color;
    }


    private String decode(String decodeStr, String type) {
        if (StringUtils.isBlank(decodeStr)) {
            return decodeStr;
        }
        try {
            Map<String, Map<String, List<String>>> bodyMap = new HashMap<>();
            Map<String, List<String>> value = new HashMap<>();
            value.put(type, List.of(decodeStr));
            bodyMap.put(DECODE_VALUE_KEY, value);
            RequestBody body = RequestBody.create(jsonMediaType, JSON.toJSONString(bodyMap));

            String responseBodyStr = util.doPostForDecode(Constants.YMM_OPEN_PLATFORM_DECODE_URL, body);
            if (StringUtils.isNotBlank(responseBodyStr)) {
                JSONObject jsonObject = JSON.parseObject(responseBodyStr);
                JSONArray jsonArray = jsonObject.getJSONArray(type);
                if (!jsonArray.isEmpty()) {
                    return jsonArray.getString(0);
                }
            }
        } catch (Exception e) {
            log.error("ocr decode error:", e);
        }
        return decodeStr;
    }

}
