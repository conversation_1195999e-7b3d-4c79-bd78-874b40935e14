package com.teyuntong.outer.export.service.service.common.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/22 15:32
 */
@Slf4j
public class LogPrintUtil {


    /**
     * 打印日志，内容过长时会截取
     * @param label label
     * @param info info
     */
    public static void printFixLog(String label, Object info){
        String logText = "";

        if(info != null){
            if(info instanceof String){
                logText = (String) info;
            }else {
                logText = JSON.toJSONString(info);
            }

        }

        if (logText != null && logText.length() > 2000) {
            logText = logText.substring(0, 2000 - 1) + "...skipping...";
        }

        log.info(label + " : {}", logText);
    }

}
