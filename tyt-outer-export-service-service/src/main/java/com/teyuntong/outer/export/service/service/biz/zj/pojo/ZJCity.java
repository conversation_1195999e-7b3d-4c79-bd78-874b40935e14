package com.teyuntong.outer.export.service.service.biz.zj.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/05 19:18
 */
@Data
public class ZJCity {


    /**
     * 城市编码
     */
    @JsonProperty("cityCode")
    private String cityCode;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    private String cityName;

    /**
     * 进出城市时间 1628486013000
     */
    @JsonProperty("vInOutTime")
    private String vInOutTime;

    /**
     * 进出城市类型 1-进 2-出
     */
    @JsonProperty("vInOutType")
    private String vInOutType;
}
