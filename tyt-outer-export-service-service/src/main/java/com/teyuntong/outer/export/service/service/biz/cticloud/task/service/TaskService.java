package com.teyuntong.outer.export.service.service.biz.cticloud.task.service;

import com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.entity.AutoCallTaskLog;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.mapper.AutoCallTaskLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 保存自动外呼日志
 *
 * <AUTHOR>
 * @since 2024/12/10 15:02
 */
@Service
public class TaskService {

    @Autowired
    private AutoCallTaskLogMapper autoCallTaskLogMapper;

    public int saveLog(AutoCallTaskLog autoCallTaskLog) {
        return autoCallTaskLogMapper.insert(autoCallTaskLog);
    }
}
