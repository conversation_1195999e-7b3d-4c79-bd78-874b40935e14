package com.teyuntong.outer.export.service.service.rpc.isp;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeResponse;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.EditWaybillThreeRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.EditWaybillThreeResponse;

/**
 * 开票服务商抽象类
 *
 * <AUTHOR>
 * @since 2024/07/17 14:49
 */
public abstract class AbstractInvoiceServiceProvider {

    /**
     * 添加运单
     *
     * @param createWaybillThreeRequest
     * @return
     * @throws Exception
     */
    public abstract WebResult<CreateWaybillThreeResponse> addWaybillThree(CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception;

    /**
     * 修改运单
     *
     * @param editWaybillThreeRequest
     * @return
     * @throws Exception
     */
    public abstract EditWaybillThreeResponse editWaybillThree(EditWaybillThreeRequest editWaybillThreeRequest) throws Exception;


    /**
     * 添加运力
     */

    /**
     * 修改运力
     */
}
