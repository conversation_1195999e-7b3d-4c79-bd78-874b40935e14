package com.teyuntong.outer.export.service.service.biz.cticloud.axb.client;

import com.teyuntong.outer.export.service.service.biz.cticloud.CticloudBaseConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2023/6/9 下午3:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "cticloud2")
public class CticloudConfigTwo extends CticloudBaseConfig {
}
