package com.teyuntong.outer.export.service.service.biz.hbwj.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.HBWJBaseResult;
import com.teyuntong.outer.export.service.service.biz.hbwj.config.HBWJRetrofitSupplier;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:01
 */
@RetrofitClient(value = HBWJRetrofitSupplier.class)
@UseInterceptor(LogInfoInterceptor.class)
public interface HBWJClient {

    @POST("open-oauth/oauth/token")
    @FormUrlEncoded
    Call<HBWJBaseResult<Object>> getToken(@Header("Authorization") String heade, @Field("grant_type")String type,
                                          @Field("client_id") String id, @Field("client_secret")String secret);

}
