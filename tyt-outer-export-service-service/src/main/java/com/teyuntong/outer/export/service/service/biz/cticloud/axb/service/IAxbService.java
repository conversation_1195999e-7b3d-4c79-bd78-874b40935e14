package com.teyuntong.outer.export.service.service.biz.cticloud.axb.service;


import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbBindReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbUpdateReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.CDRReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.CDRsResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/19 上午9:54
 */
public interface IAxbService {

    /**
     * axb绑定
     */
    TytAxbBindInfo axbBind(AxbBindReq req);

    /**
     * axb查询
     */
    TytAxbBindInfo getAxbInfoByThirdPartyId(String thirdPartyId);

    /**
     * axb查询
     */
    List<TytAxbBindInfo> getAxbInfo(Integer bizType, Long bizId, String telA, String telB, String extraField);

    /**
     * axb修改
     */
    void axbUpdate(AxbUpdateReq req);

    /**
     * axb删除
     */
    void axbDelete(Long id);

    /**
     * 获取话单
     */
    CDRsResp getCDR(CDRReq req);
}
