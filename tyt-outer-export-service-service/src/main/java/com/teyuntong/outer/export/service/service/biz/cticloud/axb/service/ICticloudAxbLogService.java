package com.teyuntong.outer.export.service.service.biz.cticloud.axb.service;

import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import retrofit2.Call;
import retrofit2.Response;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/19 上午11:12
 */
public interface ICticloudAxbLogService {

    /**
     * @param requestType 请求类型 1 绑定 2解绑 3更新
     * @param call
     * @param response
     * @param time
     * @param subId
     * @param <C>
     * @param <R>
     */
    <C, R> void logRequest(int requestType,
                           Call<C> call,
                           Response<CticloudResp<R>> response,
                           Date time,
                           String subId);
}