package com.teyuntong.outer.export.service.service.rpc.megvii;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.client.megvii.service.MegaiiFaceIdV5RpcService;
import com.teyuntong.outer.export.service.client.megvii.vo.v5.*;
import com.teyuntong.outer.export.service.service.biz.megvii.v5.client.FaceIdV5Api;
import com.teyuntong.outer.export.service.service.biz.megvii.v5.client.req.LivenessReq;
import com.teyuntong.outer.export.service.service.biz.megvii.v5.client.req.VerifyReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;

/**
 * 旷世人脸识别-v5版本
 *
 * <AUTHOR>
 * @since 2024/12/05 21:29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class MegaiiFaceIdV5RpcServiceImpl implements MegaiiFaceIdV5RpcService {
    private final FaceIdV5Api faceIdV5Api;
    private final ObjectMapper mapper;

    @Override
    public BizTokenV5Resp getBizToken(GetBizTokenV5Req req) throws IOException {
        Response<BizTokenV5Resp> execute = faceIdV5Api.getBizToken(req.getLivenessId()).execute();

        BizTokenV5Resp body = null;
        if (execute.isSuccessful()) {
            body = execute.body();
        } else {
            try (ResponseBody responseBody = execute.errorBody()) {
                if (responseBody != null) {
                    body = mapper.readValue(responseBody.byteStream(), BizTokenV5Resp.class);
                }
            }
        }
        return body;
    }

    @Override
    public LivenessResp liveness(WebLivenessReq req) throws IOException {
        LivenessReq livenessReq = new LivenessReq();
        livenessReq.setBizToken(req.getBizToken());
        livenessReq.setBizNo(req.getBizNo());
        livenessReq.setDataType(req.getDataType());
        livenessReq.setEncryptionType(req.getEncryptionType());

        Map<String, Object> params = mapper.readValue(mapper.writeValueAsBytes(livenessReq), new TypeReference<Map<String, Object>>() {
        });
        Response<LivenessResp> execute = faceIdV5Api.liveness(params).execute();

        LivenessResp body = null;
        if (execute.isSuccessful()) {
            body = execute.body();
        } else {
            try (ResponseBody responseBody = execute.errorBody()) {
                if (responseBody != null) {
                    body = mapper.readValue(responseBody.byteStream(), LivenessResp.class);
                }
            }
        }
        return body;
    }

    @Override
    public VerifyResp verify(WebVerifyReq req) throws IOException {
        VerifyReq verifyReq = new VerifyReq();
        verifyReq.setBizNo(req.getBizNo());
        verifyReq.setDataType(req.getDataType());
        verifyReq.setComparisonType(req.getComparisonType());
        verifyReq.setIdCardName(req.getIdCardName());
        verifyReq.setVerifyId(req.getVerifyId());
        verifyReq.setIdCardNumber(req.getIdCardNumber());
        verifyReq.setEncryptionType(req.getEncryptionType());
        verifyReq.setBizToken(req.getBizToken());

        Map<String, Object> params = mapper.readValue(mapper.writeValueAsBytes(verifyReq), new TypeReference<Map<String, Object>>() {
        });
        Response<VerifyResp> execute = faceIdV5Api.verify(params).execute();

        VerifyResp body = null;
        if (execute.isSuccessful()) {
            body = execute.body();
        } else {
            try (ResponseBody responseBody = execute.errorBody()) {
                if (responseBody != null) {
                    body = mapper.readValue(responseBody.byteStream(), VerifyResp.class);
                }
            }
        }
        return body;
    }
}
