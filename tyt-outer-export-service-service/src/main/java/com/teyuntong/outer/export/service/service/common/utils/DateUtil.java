package com.teyuntong.outer.export.service.service.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {
    public static final String date_time_format = "yyyy-MM-dd HH:mm:ss";
    public static final String day_format = "yyyy-MM-dd";
    public static final String MONTH_FORMAT = "yyyy-MM";

    public static final String time_format_short = "yyyyMMddHHmmss";
    public static final String day_format_short = "yyyyMMdd";
    public static final String MONTH_FORMAT_SHORT = "yyyyMM";
    public static final String DAY_CHINA_FORMAT = "yyyy年MM月dd日";

    private static final ThreadLocal<SimpleDateFormat> YEAR_MONTH_FORMAT_SHORT = new ThreadLocal<SimpleDateFormat>(){
        @Override
        protected SimpleDateFormat initialValue(){
            return new SimpleDateFormat("yyyy年MM月");
        }
    };

    private static final ThreadLocal<SimpleDateFormat> DATE_CHINA_FORMAT_SHORT = new ThreadLocal<SimpleDateFormat>(){
        @Override
        protected SimpleDateFormat initialValue(){
            return new SimpleDateFormat(DAY_CHINA_FORMAT);
        }
    };

    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_SHORT = new ThreadLocal<SimpleDateFormat>(){
        @Override
        protected SimpleDateFormat initialValue(){
            return new SimpleDateFormat(day_format);
        }
    };

    public static final String default_time_zone = "GMT+8";

    /**
     * 转换String 为 date
     *
     * @param dateStr
     * @param formatType
     * @return
     * @throws Exception
     */
    public static Date stringToDate(String dateStr, String formatType) {

        SimpleDateFormat format = new SimpleDateFormat(formatType);

        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            log.error("", e);
        }

        return date;
    }

    /**
     * 年月字符串转date 月份最后一天
     *
     */
    public static Date dateStrToDateEndDayOfMonth(String timeStr) {
        try {
            Date parseTime = YEAR_MONTH_FORMAT_SHORT.get().parse(timeStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parseTime);
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.DATE, -1);
            return calendar.getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 汉字年月日转date
     * @param timeStr 时间字符串 eg:2008年08月08日
     */
    public static Date chinaDayToDate(String timeStr) {
        if (StringUtils.isNotBlank(timeStr)) {
            try {
                return DATE_CHINA_FORMAT_SHORT.get().parse(timeStr);
            } catch (Exception e) {
                log.error("china day string parse to date error:", e);
            }
        }
        return null;
    }

    /**
     * 年月日转date
     * @param timeStr 时间字符串 eg:2008-08-08
     */
    public static Date dayToDate(String timeStr) {
        if (StringUtils.isNotBlank(timeStr)) {
            try {
                return DATE_FORMAT_SHORT.get().parse(timeStr);
            } catch (Exception e) {
                log.error("china day string parse to date error:", e);
            }
        }
        return null;
    }

    /**
     * 日期转字符串
     *
     * @param date
     * @param formatType
     * @return
     */
    public static String dateToString(Date date, String formatType) {
        SimpleDateFormat format = new SimpleDateFormat(formatType);
        String resultDay = format.format(date);
        return resultDay;
    }

    /**
     * 获取天开始时间
     *
     * @param date
     * @param formatType
     * @return
     */
    public static String getDayBegin(Date date, String formatType) {

        String dayStr = dateToString(date, day_format);

        Date dayDate = stringToDate(dayStr, day_format);

        String result = dateToString(dayDate, formatType);

        return result;
    }

    /**
     * 获取当前月的第一天
     *
     * @return
     */
    public static Date getFirstDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取当前月的最后一天
     *
     * @return
     */
    public static Date getEndDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

}
