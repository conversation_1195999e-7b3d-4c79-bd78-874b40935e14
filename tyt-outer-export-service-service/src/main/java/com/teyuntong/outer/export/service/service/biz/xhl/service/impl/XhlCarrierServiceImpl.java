package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlCarrierEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlCarrierService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 托运人相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlCarrierServiceImpl implements XhlCarrierService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> saveCompany(XhlCarrierEntity carrierEntity) throws Exception {
        log.info("新增托运人-请求参数，carrierEntity={}", carrierEntity);
        if (!Objects.isNull(carrierEntity)) {
            carrierEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveCompany(carrierEntity).execute();
        log.info("新增托运人-返回结果，carrierEntity={}，响应参数：{} ", carrierEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateCompany(XhlCarrierEntity carrierEntity) throws Exception {
        log.info("修改托运人-请求参数，carrierEntity={}", carrierEntity);
        if (!Objects.isNull(carrierEntity)) {
            carrierEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateCompany(carrierEntity).execute();
        log.info("修改托运人-返回结果，carrierEntity={}，响应参数：{} ", carrierEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryCompany(String companyName) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询托运人-请求参数，appId={}，companyName={}", appId, companyName);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryCompany(appId, companyName).execute();
        log.info("查询托运人-返回结果，appId={}，companyName={}，响应参数：{} ", appId, companyName, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> saveBank(XhlCarrierEntity carrierEntity) throws Exception {
        log.info("托运人绑卡-请求参数，carrierEntity={}", carrierEntity);
        if (!Objects.isNull(carrierEntity)) {
            carrierEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveCompanyBank(carrierEntity).execute();
        log.info("托运人绑卡-返回结果，carrierEntity={}，响应参数：{} ", carrierEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> deleteBank(XhlCarrierEntity carrierEntity) throws Exception {
        log.info("托运人解绑银行卡-请求参数，carrierEntity={}", carrierEntity);
        if (!Objects.isNull(carrierEntity)) {
            carrierEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.deleteCompanyBank(carrierEntity).execute();
        log.info("托运人解绑银行卡-返回结果，carrierEntity={}，响应参数：{} ", carrierEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryBalance(String companyName) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询托运人余额-请求参数，appId={}，companyName={}", appId, companyName);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryCompanyBalance(appId, companyName).execute();
        log.info("查询托运人余额-返回结果，appId={}，companyName={}，响应参数：{} ", appId, companyName, response.body());
        return response.body();
    }
}
