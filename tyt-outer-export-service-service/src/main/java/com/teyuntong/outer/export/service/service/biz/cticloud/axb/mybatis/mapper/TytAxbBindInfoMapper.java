package com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("common")
public interface TytAxbBindInfoMapper extends BaseMapper<TytAxbBindInfo> {

    int insertSelective(TytAxbBindInfo tytAxbBindInfo);

    TytAxbBindInfo selectByThirdPartyId(TytAxbBindInfo tytAxbBindInfo);

    List<TytAxbBindInfo> selectList(TytAxbBindInfo tytAxbBindInfo);

    int updateSelective(TytAxbBindInfo tytAxbBindInfo);
}