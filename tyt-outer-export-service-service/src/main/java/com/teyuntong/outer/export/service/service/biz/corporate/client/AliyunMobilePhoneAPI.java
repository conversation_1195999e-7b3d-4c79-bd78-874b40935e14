package com.teyuntong.outer.export.service.service.biz.corporate.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.PhoneNumberAttribution;
import com.teyuntong.outer.export.service.service.biz.manbang.util.HttpClientFactory;
import com.teyuntong.outer.export.service.service.common.constant.HttpHeader;
import com.teyuntong.outer.export.service.service.common.constant.HttpMethod;
import com.teyuntong.outer.export.service.service.common.utils.HttpClientUtil;
import com.teyuntong.outer.export.service.service.common.utils.MessageDigestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 百度获取归属地API
 * Created by duanwc on 17/8/1.
 */
@Slf4j
public class AliyunMobilePhoneAPI {


    private static CloseableHttpClient httpClient = HttpClientFactory.getHttpClientWithRetry();

    private AliyunMobilePhoneAPI(){}

    //APP KEY
    private final static String APP_KEY = "203831195";
    // APP密钥
    private final static String APP_SECRET = "zTwvtNJwy2XiFkntQskHCVhi32z667ee";
    //自定义参与签名Header前缀（可选,默认只有"X-Ca-"开头的参与到Header签名）
    private final static List<String> CUSTOM_HEADERS_TO_SIGN_PREFIX = new ArrayList<String>();

    /**
     * 查询手机归属地
     * @param mobilePhone 手机号
     * @return PhoneNumberAttribution 查询失败返回null
     * @see PhoneNumberAttribution
     * 参考示例
     * {
     *     "data":{
     *         "num":1813843,
     *         "isp":"电信",
     *         "prov":"广东",
     *         "city":"深圳",
     *         "types":"中国电信",
     *         "city_code":"0755",
     *         "area_code":"440300",
     *         "zip_code":"518000",
     *         "lng":"114.057868",
     *         "lat":"22.543099"
     *     },
     *     "ret":200,
     *     "msg":"success",
     *     "log_id":"2ccfb06330b848ad87cb7fa2458c34e8"
     * }
     */
    public static PhoneNumberAttribution getMobileLocale(String mobilePhone) {
        String host = "http://api04.aliyun.venuscn.com";
        // 请求path
        String path = "/mobile";

        String apiUrl = host + path;
        try {
            // 请求的query
            Map<String, String> querys = new HashMap<String, String>();
            querys.put("mobile", mobilePhone);

            URI uri = HttpClientUtil.createUri(apiUrl, querys);

            HttpGet httpGet = new HttpGet(uri);

            Map<String, String> headerMap = new HashMap<String, String>();
            //（必填）根据期望的Response内容类型设置
            headerMap.put(HttpHeader.HTTP_HEADER_ACCEPT, ContentType.APPLICATION_JSON.getMimeType());
            CUSTOM_HEADERS_TO_SIGN_PREFIX.clear();
            headerMap = HttpHeader.initialBasicHeader(HttpMethod.GET, path, headerMap, querys, null, CUSTOM_HEADERS_TO_SIGN_PREFIX, APP_KEY, APP_SECRET);

            for (Map.Entry<String, String> oneEntry : headerMap.entrySet()) {
                httpGet.addHeader(oneEntry.getKey(), MessageDigestUtil.utf8ToIso88591(oneEntry.getValue()));
            }

            CloseableHttpResponse httpResponse = HttpClientUtil.execute(httpGet);

            JSONObject jsonObject = HttpClientUtil.toJsonObject(httpResponse);

            Integer ret = jsonObject.getInteger("ret");
            String message = jsonObject.getString("msg");
            PhoneNumberAttribution attribution = null;
            if(ret != 200) { // 失败
                attribution = new PhoneNumberAttribution();
                attribution.setStatus(2);
            } else { // 成功
                attribution = JSON.toJavaObject(jsonObject.getJSONObject("data"), PhoneNumberAttribution.class);
                attribution.setStatus(1);
            }
            attribution.setCode(ret);
            attribution.setMsg(message);
            attribution.setCellPhone(mobilePhone);
            attribution.setVersion("aliyun");
            return attribution;
        } catch (IOException e) {
            log.error("", e);
        }
        return null;
    }

    public static void main(String[] args) {
        /**
         * 示例：
         */
        PhoneNumberAttribution attribution = AliyunMobilePhoneAPI.getMobileLocale("13873829876");
        System.out.println(JSON.toJSONString(attribution));
    }
}
