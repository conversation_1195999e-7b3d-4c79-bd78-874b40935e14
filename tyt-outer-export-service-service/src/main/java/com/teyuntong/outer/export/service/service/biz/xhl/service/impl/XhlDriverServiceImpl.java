package com.teyuntong.outer.export.service.service.biz.xhl.service.impl;

import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.service.biz.xhl.client.XhlInterfaceClient;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.*;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlDriverService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * 翔和翎 司机相关业务接口实现类
 *
 * <AUTHOR>
 * @since 2025/01/13 14:48
 */
@Service
@Slf4j
public class XhlDriverServiceImpl implements XhlDriverService {

    @Autowired
    private XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    private XhlProperties xhlProperties;

    private XhlInterfaceClient xhlInterfaceClient;

    @PostConstruct
    public void init() {
        this.xhlInterfaceClient = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
    }

    @Override
    public XhlDataResult<Object> saveDriver(XhlDriverEntity driverEntity) throws Exception {
        log.info("新增驾驶员-请求参数，driverEntity={}", driverEntity);
        if (!Objects.isNull(driverEntity)) {
            driverEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveDriver(driverEntity).execute();
        log.info("新增驾驶员-返回结果，driverEntity={}，响应参数：{} ", driverEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateDriver(XhlDriverEntity driverEntity) throws Exception {
        log.info("修改驾驶员-请求参数，driverEntity={}", driverEntity);
        if (!Objects.isNull(driverEntity)) {
            driverEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateDriver(driverEntity).execute();
        log.info("修改驾驶员-返回结果，driverEntity={}，响应参数：{} ", driverEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryDriver(String idCardNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询驾驶员-请求参数，appId={}，idCardNo={}", appId, idCardNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryDriver(appId, idCardNo).execute();
        log.info("查询驾驶员-返回结果，appId={}，idCardNo={}，响应参数：{} ", appId, idCardNo, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> saveBank(XhlDriverEntity driverEntity) throws Exception {
        log.info("驾驶员绑卡-请求参数，driverEntity={}", driverEntity);
        if (!Objects.isNull(driverEntity)) {
            driverEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveDriver(driverEntity).execute();
        log.info("驾驶员绑卡-返回结果，driverEntity={}，响应参数：{} ", driverEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> deleteBank(XhlDriverEntity driverEntity) throws Exception {
        log.info("驾驶员解绑银行卡-请求参数，driverEntity={}", driverEntity);
        if (!Objects.isNull(driverEntity)) {
            driverEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.deleteDriverBank(driverEntity).execute();
        log.info("驾驶员解绑银行卡-返回结果，driverEntity={}，响应参数：{} ", driverEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> saveDriverTruck(XhlDriverTruckEntity driverTruckEntity) throws Exception {
        log.info("新增人车合照-请求参数，driverTruckEntity={}", driverTruckEntity);
        if (!Objects.isNull(driverTruckEntity)) {
            driverTruckEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveDriverTruck(driverTruckEntity).execute();
        log.info("新增人车合照-返回结果，driverTruckEntity={}，响应参数：{} ", driverTruckEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> updateDriverTruck(XhlDriverTruckEntity driverTruckEntity) throws Exception {
        log.info("修改人车合照-请求参数，driverTruckEntity={}", driverTruckEntity);
        if (!Objects.isNull(driverTruckEntity)) {
            driverTruckEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.updateDriverTruck(driverTruckEntity).execute();
        log.info("修改人车合照-返回结果，driverTruckEntity={}，响应参数：{} ", driverTruckEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryDriverTruck(String idCardNo, String plateNumber) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询人车合照-请求参数，appId={}，idCardNo={}，plateNumber={}", appId, idCardNo, plateNumber);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryDriverTruck(appId, idCardNo, plateNumber).execute();
        log.info("查询人车合照-返回结果，appId={}，idCardNo={}，plateNumber={}，响应参数：{} ", appId, idCardNo, plateNumber, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> saveEmpowerDriver(XhlDriverEntity driverEntity) throws Exception {
        log.info("新增司机授权（委托代征）-请求参数，driverEntity={}", driverEntity);
        if (!Objects.isNull(driverEntity)) {
            driverEntity.setAppId(xhlProperties.getAppId());
        }
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveEmpowerDriver(driverEntity).execute();
        log.info("新增司机授权（委托代征）-返回结果，driverEntity={}，响应参数：{} ", driverEntity, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> queryEmpowerDriver(String idCardNo) throws Exception {
        String appId = xhlProperties.getAppId();
        log.info("查询司机授权（委托代征）请求参数，appId={}，idCardNo={}", appId, idCardNo);
        Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryEmpowerDriver(appId, idCardNo).execute();
        log.info("查询司机授权（委托代征）-返回结果，appId={}，idCardNo={}，响应参数：{} ", appId, idCardNo, response.body());
        return response.body();
    }

    @Override
    public XhlDataResult<Object> saveAppFaceAuth(XhlFaceAuthEntity xhlFaceAuthEntity) {
        log.info("司机人脸认证-请求参数，xhlFaceAuthEntity={}", xhlFaceAuthEntity);
        if (!Objects.isNull(xhlFaceAuthEntity)) {
            xhlFaceAuthEntity.setAppId(xhlProperties.getAppId());
        }
        try {
            Response<XhlDataResult<Object>> response = xhlInterfaceClient.saveAppFaceAuth(xhlFaceAuthEntity).execute();
            log.info("司机人脸认证-返回结果，driverEntity={}，响应参数：{} ", xhlFaceAuthEntity, response.body());
            return response.body();
        } catch (Exception e) {
            throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY.getCode(), e.getMessage());
        }
    }

    @Override
    public XhlDataResult<Object> queryAppFaceResult(XhlFaceAuthEntity xhlFaceAuthEntity) {
        log.info("司机人脸结果查询-请求参数，xhlFaceAuthEntity={}", xhlFaceAuthEntity);
        if (!Objects.isNull(xhlFaceAuthEntity)) {
            xhlFaceAuthEntity.setAppId(xhlProperties.getAppId());
        }
        try {
            Response<XhlDataResult<Object>> response = xhlInterfaceClient.queryAppFaceResult(xhlFaceAuthEntity).execute();
            log.info("司机人脸结果查询-返回结果，driverEntity={}，响应参数：{} ", xhlFaceAuthEntity, response.body());
            return response.body();
        } catch (Exception e) {
            throw BusinessException.createException(CommonErrorCode.ERROR_SYS_BUSY.getCode(), e.getMessage());
        }
    }
}
