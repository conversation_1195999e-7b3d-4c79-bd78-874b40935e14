package com.teyuntong.outer.export.service.service.biz.manbang.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.VO.MbResult;
import com.teyuntong.outer.export.service.service.biz.manbang.person.openacct.service.VO.MbTokenReqVO;
import com.teyuntong.outer.export.service.service.common.exception.FailureResultException;
import com.wlqq.wallet.gateway.client.util.BuildParamUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName MbRequsetUtils
 * @Description TODO
 * <AUTHOR>
 * @Verdion 1.0
 **/
public class MbRequsetUtils {

    private static Logger LOG = LoggerFactory.getLogger(MbRequsetUtils.class);

    /**
     * 解析响应结果
     *
     * @param builder
     * @return
     */
    public static String sendRequest(MbTokenReqVO builder, String baseUrl , String mbprivateKey) throws Exception {
        String result = send(builder,baseUrl,mbprivateKey);
        MbResult commResult = parseResult(result);
        return commResult == null ? null : commResult.getContent();
    }

    private static String send(MbTokenReqVO builder,String baseUrl ,String mbprivateKey) throws Exception {

        ArrayList<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("device_id",builder.getDeviceId()));
        list.add(new BasicNameValuePair("device_type",builder.getDeviceType()));
        list.add(new BasicNameValuePair("timestamp",builder.getTimestamp()));
        list.add(new BasicNameValuePair("user_id",builder.getMbUserId()));

        Map<String, String> params = new HashMap<>();
        params.put("device_id", builder.getDeviceId());
        params.put("device_type", builder.getDeviceType());
        params.put("timestamp", builder.getTimestamp());
        params.put("user_id", builder.getMbUserId());

//        String json = JSON.toJSONString(new TreeMap<>(params), SerializerFeature.MapSortField);

        Map<String, String> signMap = BuildParamUtil.buildRequestPara(params,"RSA",mbprivateKey,"UTF-8");

        String sign = signMap.get("sign");

        return HttpClientUtils.doMbPostWithSign(baseUrl, list, sign,builder.getAppId());
    }
    /**
     * 转换响应结果
     *
     * @param result
     * @return
     */
    private static MbResult parseResult(String result) {

        if (StringUtils.isBlank(result)) {
            return null;
        }

        MbResult commResult = JSON.parseObject(result,
                new TypeReference<MbResult>() {
                });
        if (!"OK".equals(commResult.getStatus())) {
            throw new FailureResultException(HttpStatus.SC_OK, commResult.getErrorMsg(), Integer.parseInt(commResult.getErrorCode()));
        }


        return commResult;
    }

}
