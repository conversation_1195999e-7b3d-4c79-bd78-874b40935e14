package com.teyuntong.outer.export.service.service.biz.manbang.ocr.service;

import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.BusinessLicenseOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQualificationCertificateOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VehicleLicenseDeputyPageBackOcrVO;

/**
 * 集团OCR服务类
 *
 * <AUTHOR>
 * @since 2024/04/25 15:27
 */
public interface GroupOcrService {

    /**
     * 道运证OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    RoadTransportBackOcrVO roadTransportBackOcr(String url);

    /**
     * 行驶证副页背面OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    VehicleLicenseDeputyPageBackOcrVO vehicleLicenseDeputyPageBackOcr(String url);

    /**
     * 道路运输从业资格证主页OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    RoadTransportQualificationCertificateOcrVO roadTransportQualificationCertificateOcr(String url);

    /**
     * 道路运输从业资格证副页OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    RoadTransportQualificationCertificateOcrVO roadTransportQualificationCertificateBackOcr(String url);

    /**
     * 营业执照OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    BusinessLicenseOcrVO businessLicenseOcr(String url);
}
