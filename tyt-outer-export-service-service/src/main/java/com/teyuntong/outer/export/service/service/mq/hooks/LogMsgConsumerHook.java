package com.teyuntong.outer.export.service.service.mq.hooks;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.teyuntong.infra.common.rocketmq.hooks.MessageConsumeHook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/08 09:44
 */
@Component
public class LogMsgConsumerHook implements MessageConsumeHook {

    /**
     * 需要定义一个 name 为 mq_producer 的 logger 来指定日志的行为
     */
    private static final Logger LOGGER = LoggerFactory.getLogger("mq_consumer");

    @Override
    public void onConsumeSuccess(Message message, ConsumeContext context, Action action) {
        LOGGER.info("RocketMq Message 消费成功, message: {}, action: {}", message, action);
    }

    @Override
    public void onConsumeFailure(Message message, ConsumeContext context, Exception e) {
        LOGGER.error("RocketMq Message 消费失败, message: {}, context: {}", message, context, e);
    }

    @Override
    public void onConsumeOrderSuccess(Message message, ConsumeOrderContext context, OrderAction orderAction) {
        LOGGER.info("RocketMq OrderMessage 消费成功, message: {}, OrderAction: {}", message, orderAction);
    }

    @Override
    public void onConsumeOrderFailure(Message message, ConsumeOrderContext context, Exception e) {
        LOGGER.error("RocketMq OrderMessage 消费失败, message: {}, context: {}", message, context, e);
    }

    @Override
    public void onConsumeBatchSuccess(List<Message> messages, ConsumeContext context, Action orderAction) {
        LOGGER.info("RocketMq BatchMessage 消费成功, messages: {}, Action: {}", messages, orderAction);
    }

    @Override
    public void onConsumeBatchFailure(List<Message> messages, ConsumeContext context, Exception e) {
        LOGGER.error("RocketMq BatchMessage 消费失败, messages: {}, context: {}", messages, context, e);
    }
}
