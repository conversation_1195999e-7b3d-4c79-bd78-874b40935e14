package com.teyuntong.outer.export.service.service.biz.tyanyancha.client;

import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.outer.export.service.client.corporate.vo.IcBasicInfoNormalResp;
import com.teyuntong.outer.export.service.service.biz.tyanyancha.client.retrofit.TYCAuthInterceptor;
import com.teyuntong.outer.export.service.service.biz.tyanyancha.client.retrofit.TYCRetrofitSupplier;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @since 2024/01/11 15:31
 */
@RetrofitClient(value = TYCRetrofitSupplier.class)
@UseInterceptor(TYCAuthInterceptor.class)
@UseInterceptor(LogInfoInterceptor.class)
public interface TYCApi {

    @GET("services/open/ic/baseinfo/normal")
    Call<IcBasicInfoNormalResp> getIcBasicInfoNormal(@Query("keyword") String keyword);
}
