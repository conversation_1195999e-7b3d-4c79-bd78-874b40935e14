package com.teyuntong.outer.export.service.service.biz.cticloud.axb.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.CticloudResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.CticloudAxbLog;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper.CticloudAxbLogMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import retrofit2.Call;
import retrofit2.Response;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/6/19 上午11:12
 */
@Service
@Slf4j
public class CticloudAxbLogServiceImpl implements ICticloudAxbLogService {

    @Autowired
    private CticloudAxbLogMapper cticloudAxbLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public <C, R> void logRequest(int requestType,
                                  Call<C> call, Response<CticloudResp<R>> response,
                                  Date time, String subId) {
        Request request = call.request();
        boolean reqSuccessful = response.isSuccessful();
        boolean ctiCloudSuccessful = false;

        CticloudResp<R> responseBody = response.body();
        if (reqSuccessful && responseBody != null) {
            ctiCloudSuccessful = Objects.equals(responseBody.getResult(), 0);
        }

        CticloudAxbLog cticloudAxbLog = CticloudAxbLog.builder()
                .requestType(requestType)
                .createTime(time)
                .updateTime(time)
                .requestUrl(request.url().toString())
                .subId(StringUtils.isBlank(subId) ? "" : subId)
                .requestBody(request.body() == null ? null : request.body().toString())
                .requestParam(request.url().query())
                .isRequestSuccess(reqSuccessful)
                .isCticloudSuccess(ctiCloudSuccessful)
                .response(JSON.toJSONString(responseBody)).build();

        cticloudAxbLogMapper.insertSelective(cticloudAxbLog);
    }
}
