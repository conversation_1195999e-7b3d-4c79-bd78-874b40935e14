package com.teyuntong.outer.export.service.service.biz.zj.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/05 19:23
 */
@Data
public class ZJHistoryTrackResult {

    /**
     * 途经城市列表
     */
    @JsonProperty("cityArray")
    private List<ZJCity> cityArray;

    /**
     * 路程总里程
     */
    @JsonProperty("mileage")
    private String mileage;

    /**
     * 停车列表
     */

    @JsonProperty("parkArray")
    private List<ZJPark> parkArray;

    /**
     * 停车次数
     */
    @JsonProperty("parkSize")
    private String parkSize;

    /**
     * 轨迹列表
     */
    @JsonProperty("trackArray")
    private List<ZJTrack> trackArray;
}
