package com.teyuntong.outer.export.service.service.biz.manbang.account.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/15 13:38
 */
@Data
public class MBAccountInfoVO {

    /**
     * 账户ID
     */
    private Long accountId;
    /**
     * 账户类型
     */
    private Integer accountType;
    /**
     * 账户状态
     */
    private Integer accountStatus;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 实名状态
     */
    private Integer realNameStatus;
    /**
     * 头像URL
     */
    private String avatarUrl;
    /**
     * 平台标识
     */
    private Integer platform;
    /**
     * 好运宝用户ID
     */
    private String hcbUserId;
    /**
     * 创建时间，时间戳格式
     */
    private Long createTime;
    /**
     * 更新时间，时间戳格式
     */
    private Long updateTime;
    /**
     * 注册标识
     */
    private Integer registerFlag;
    /**
     * 注销时间，时间戳格式，可能为null
     */
    private Long cancelTime;
    /**
     * 标签信息列表
     */
    private List<String> tagsInfo;
    /**
     * 功能特性，可能为null
     */
    private String features;
    /**
     * 注册地点
     */
    private String registerLocation;
    /**
     * 身份证号
     */
    private String identityNo;
    /**
     * 真实姓名
     */
    private String realName;

}
