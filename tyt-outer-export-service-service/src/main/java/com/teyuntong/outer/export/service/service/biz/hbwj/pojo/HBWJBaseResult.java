package com.teyuntong.outer.export.service.service.biz.hbwj.pojo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:39
 */
@NoArgsConstructor
@Data
public class HBWJBaseResult<T> {

    private String token_type;
    private String access_token;
    private String expires_in;
    private String scope;

    private Integer code;
    private String info;
    private T data;

    public static boolean checkSuccess(HBWJBaseResult<?> authBaseResult){
        return (authBaseResult != null && authBaseResult.getCode() == 0);
    }

}
