package com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.dto.SensitiveCheckResponseDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.SensitiveWordsService;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import com.teyuntong.outer.export.service.service.common.property.GroupOcrProperties;
import com.teyuntong.outer.export.service.service.remote.common.TytConfigRemoteService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.RequestBody;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(GroupOcrProperties.class)
public class SensitiveWordsServiceImpl implements SensitiveWordsService {

    private final MBOpenPlatformUtil util;

    private final TytConfigRemoteService tytConfigRemoteService;

    @Override
    public SensitiveWordsVO newCargoSensitiveCheck(Long userId, String taskContent, String machineRemark) {
        log.info("调用集团敏感词接口 请求参数：{} {} {}", userId, taskContent, machineRemark);

        if (StringUtil.isBlank(taskContent) && StringUtil.isBlank(machineRemark)) {
            return SensitiveWordsVO.builder()
                    .taskContentHitSensitiveWords(false)
                    .machineRemarkHitSensitiveWords(false)
                    .sensitiveWords(new HashSet<>())
                    .build();
        }

        RequestBody body = new FormBody.Builder()
                .add("cargoUserId", String.valueOf(userId))
                .add("filterType", "0")
                .add("securedTran", "2")
                .add("cargoName", StringUtil.isNotBlank(taskContent) ? taskContent : "")
                .add("description", StringUtil.isNotBlank(machineRemark) ? machineRemark : "")
                .add("ticketType", "1")
                .add("scene", "tyt")
                .build();

//        String responseBodyStr = util.doPost(Constants.NEW_CARGO_SENSITIVE_CHECK_URL, body);

        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";

        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";


        String responseBodyStr;
        Integer cargoSensitiveCheck = tytConfigRemoteService.getIntValue("cargo_sensitive_check");
        if (cargoSensitiveCheck == null || cargoSensitiveCheck == 0) {
            responseBodyStr = acceptResponse;
        } else {
            responseBodyStr = sensitiveResponse;
        }

        log.info("调用集团敏感词接口 返回信息：{}", responseBodyStr);

        SensitiveWordsVO sensitiveWordsVO = parseSensitiveCheckResponse(responseBodyStr);

        log.info("调用集团敏感词接口 解析处理后得到的信息：{}", JSON.toJSONString(sensitiveWordsVO));

        return sensitiveWordsVO;
    }

    /**
     * 解析敏感词检测接口响应
     *
     * @param responseBodyStr 响应字符串
     * @return 敏感词检测结果
     */
    private SensitiveWordsVO parseSensitiveCheckResponse(String responseBodyStr) {
        try {

            ObjectMapper objectMapper = new ObjectMapper();

            SensitiveCheckResponseDTO response = objectMapper.readValue(responseBodyStr, SensitiveCheckResponseDTO.class);

            // 如果decision是字符串"accept"，表示未命中敏感词
            if (response.getDecision() instanceof String && "accept".equals(response.getDecision())) {
                return SensitiveWordsVO.builder()
                        .taskContentHitSensitiveWords(false)
                        .machineRemarkHitSensitiveWords(false)
                        .sensitiveWords(new HashSet<>())
                        .build();
            }

            // 如果decision是对象，表示命中了敏感词，需要解析详情
            Set<String> sensitiveWords = new HashSet<>();

            Boolean cargoNameHitSensitiveWords = false;
            Boolean descriptionHitSensitiveWords = false;

            // 将decision对象转换为DecisionDetail
            SensitiveCheckResponseDTO.DecisionDetail decisionDetail =
                    objectMapper.convertValue(response.getDecision(), SensitiveCheckResponseDTO.DecisionDetail.class);

            if (decisionDetail != null && decisionDetail.getRuleCode() != null && decisionDetail.getRuleCode().equals("31133")) {
                return SensitiveWordsVO.builder()
                        .taskContentHitSensitiveWords(false)
                        .machineRemarkHitSensitiveWords(false)
                        .sensitiveWords(new HashSet<>())
                        .build();
            }

            if (decisionDetail != null && decisionDetail.getRuleDesc() != null) {
                SensitiveCheckResponseDTO.RuleDescription ruleDesc = decisionDetail.getRuleDesc();

                // 提取cargoName中的敏感词
                cargoNameHitSensitiveWords = extractSensitiveWords(ruleDesc.getCargoName(), sensitiveWords);

                // 提取description中的敏感词
                descriptionHitSensitiveWords = extractSensitiveWords(ruleDesc.getDescription(), sensitiveWords);
            }

            return SensitiveWordsVO.builder()
                    .taskContentHitSensitiveWords(cargoNameHitSensitiveWords)
                    .machineRemarkHitSensitiveWords(descriptionHitSensitiveWords)
                    .sensitiveWords(sensitiveWords)
                    .build();

        } catch (Exception e) {
            log.error("解析敏感词检测接口响应失败", e);
            // 解析失败时，防止对业务流程造成阻断，认为未命中敏感词
            return SensitiveWordsVO.builder()
                    .taskContentHitSensitiveWords(false)
                    .machineRemarkHitSensitiveWords(false)
                    .sensitiveWords(new HashSet<>())
                    .build();
        }
    }

    /**
     * 从字段敏感词信息中提取敏感词
     *
     * @param fieldInfo 字段敏感词信息
     * @param sensitiveWords 敏感词集合
     */
    private Boolean extractSensitiveWords(SensitiveCheckResponseDTO.FieldSensitiveInfo fieldInfo, Set<String> sensitiveWords) {
        Boolean hitSensitiveWords = false;
        if (fieldInfo != null && fieldInfo.getHitDetails() != null) {
            List<SensitiveCheckResponseDTO.HitDetail> hitDetails = fieldInfo.getHitDetails();
            for (SensitiveCheckResponseDTO.HitDetail hitDetail : hitDetails) {
                if (hitDetail.getHitText() != null && !hitDetail.getHitText().trim().isEmpty()) {
                    sensitiveWords.add(hitDetail.getHitText());
                    hitSensitiveWords = true;
                }
            }
        }
        return hitSensitiveWords;
    }

}

