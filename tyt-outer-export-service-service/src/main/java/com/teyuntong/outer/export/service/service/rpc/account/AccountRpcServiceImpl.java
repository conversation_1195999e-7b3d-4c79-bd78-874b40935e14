package com.teyuntong.outer.export.service.service.rpc.account;

import com.teyuntong.outer.export.service.client.account.dto.AccountRpcDTO;
import com.teyuntong.outer.export.service.client.account.dto.LicenseRpcDTO;
import com.teyuntong.outer.export.service.client.account.service.AccountRpcService;
import com.teyuntong.outer.export.service.client.account.vo.AccountInfoRpcVO;
import com.teyuntong.outer.export.service.client.account.vo.VehicleLicenseRpcVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBAccountDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBLicenseDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.service.MBAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBAccountInfoVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBLicenseVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.VehicleLicenseVO;
import com.teyuntong.outer.export.service.service.biz.xhl.util.TytBeanUtil;
import com.timevale.esign.sdk.tech.service.AccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/04/15 14:43
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AccountRpcServiceImpl implements AccountRpcService {

    private final MBAccountService mbAccountService;
    @Override
    public List<AccountInfoRpcVO> queryAccountsByMobile(AccountRpcDTO accountRpcDTO) {

        MBAccountDTO mbAccountDTO = new MBAccountDTO();
        mbAccountDTO.setMobile(accountRpcDTO.getMobile());
        List<MBAccountInfoVO> accountInfoVOList = mbAccountService.queryAccountsByMobile(mbAccountDTO);
        return TytBeanUtil.convertBeanList(accountInfoVOList, AccountInfoRpcVO.class);
    }

    @Override
    public VehicleLicenseRpcVO queryLicenseByTypes(LicenseRpcDTO licenseRpcDTO) {
        MBLicenseDTO mbLicenseDTO = new MBLicenseDTO();
        mbLicenseDTO.setAccountId(licenseRpcDTO.getAccountId());
        mbLicenseDTO.setLicenseTypes(licenseRpcDTO.getLicenseTypes());
        MBLicenseVO mbLicenseVO = mbAccountService.queryLicenseByTypes(mbLicenseDTO);
        if (Objects.nonNull(mbLicenseVO)) {
            VehicleLicenseVO vehicleLicense = mbLicenseVO.getVehicleLicense();
            if(Objects.nonNull(vehicleLicense)){
                return TytBeanUtil.convertBean(vehicleLicense, VehicleLicenseRpcVO.class);
            }
        }
        return null;
    }
}
