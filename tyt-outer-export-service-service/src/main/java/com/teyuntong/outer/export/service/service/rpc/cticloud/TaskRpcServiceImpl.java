package com.teyuntong.outer.export.service.service.rpc.cticloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.cticloud.task.service.TaskRpcService;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.*;
import com.teyuntong.outer.export.service.client.error.OuterExportErrorCode;
import com.teyuntong.outer.export.service.service.biz.cticloud.CticloudClient;
import com.teyuntong.outer.export.service.service.biz.cticloud.axb.client.TaskCreateResp;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.client.TaskApi;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.entity.AutoCallTaskLog;
import com.teyuntong.outer.export.service.service.biz.cticloud.task.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * cticloud task相关
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@Slf4j
@RestController
public class TaskRpcServiceImpl implements TaskRpcService {

    private final TaskApi taskApi;

    private final TaskService taskService;

    public TaskRpcServiceImpl(CticloudClient cticloudClient, TaskService taskService) {
        this.taskApi = cticloudClient.getTaskApi();
        this.taskService = taskService;
    }

    @Override
    public CticloudResp<TaskQueryResult> queryTask(String name, Integer type, Integer status, Integer autoStart, Integer autoStop, Integer timeType, String startTime, String endTime) throws IOException {
        Response<CticloudResp<TaskQueryResult>> response = taskApi.queryTask(name, type, status, autoStart, autoStop,
                timeType, startTime, endTime).execute();
        log.info("TaskController queryTask  [name:{}]  [type:{}]  [status:{}]  [autoStart:{}] [autoStop:{}]  [timeType:{}]  [startTime:{}]  [endTime:{}]  [response:{}]",name,type,status,autoStart,autoStop,timeType,startTime,endTime,response==null?"":response.toString());

        if (!response.isSuccessful()) {
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        return response.body();
    }

    @Override
    public CticloudResp<ImportTaskTelResponse> importTaskTel(ImportTaskTelRequest importTaskTelRequest) throws IOException {
        Response<CticloudResp<ImportTaskTelResponse>> response = taskApi.importTaskTel(importTaskTelRequest).execute();
        if (!response.isSuccessful()) {
            throw new BusinessException(OuterExportErrorCode.COMMON_API_INTERFACE_ERROR);
        }

        return response.body();
    }

    @Override
    public void autoCallTask(AutoCallTaskRequest autoCallTaskRequest) throws IOException, InterruptedException {
        if (autoCallTaskRequest == null
                || StringUtils.isBlank(autoCallTaskRequest.getTaskName())
                || StringUtils.isBlank(autoCallTaskRequest.getTaskCallValue())
                || CollectionUtils.isEmpty(autoCallTaskRequest.getCallTelList())) {
            throw new BusinessException(OuterExportErrorCode.COMMON_LACK_ERROR);
        }

        log.info("自动外呼请求参数：【{}】", JSON.toJSONString(autoCallTaskRequest));
        if (nowisNotBetweenNineAndTwentyAclock()) {
            log.info("非自动外呼时间段，不可创建自动外呼任务 请求参数:{}", JSONObject.toJSONString(autoCallTaskRequest));
            throw new BusinessException(OuterExportErrorCode.COMMON_AUTO_CALL_TIME_ERROR);
        }

        String userFields = "[{\"text\": \"" + autoCallTaskRequest.getTaskCallValue() + "\"}]";

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNow = now.format(formatter);
        String taskName = autoCallTaskRequest.getTaskName() + "-" + formattedNow;


        AutoCallTaskLog autoCallTaskLog = new AutoCallTaskLog();
        autoCallTaskLog.setTaskCallValue(autoCallTaskRequest.getTaskCallValue());
        autoCallTaskLog.setTaskName(taskName);
        autoCallTaskLog.setCallTelList(JSONObject.toJSONString(autoCallTaskRequest.getCallTelList()));
        autoCallTaskLog.setCreateTime(new Date());

        String templateName = "接口对接模板";
        if (taskName.contains("专车")) {
            templateName = "专车接口对接模板";
        }

        Response<CticloudResp<TaskCreateResp>> taskCreateResponse = taskApi.taskCreate(taskName, "2", userFields, templateName).execute();
        autoCallTaskLog.setCreateTaskRespones(JSONObject.toJSONString(taskCreateResponse.body()));
        autoCallTaskLog.setCreateTaskIsSuccess(0);
        if (taskCreateResponse.body() != null && taskCreateResponse.body().getResult() != null && taskCreateResponse.body().getResult() == 0) {
            TaskCreateResp taskCreateResp = taskCreateResponse.body().getData();
            if (taskCreateResp != null && taskCreateResp.getId() != null) {
                autoCallTaskLog.setCreateTaskIsSuccess(1);
                autoCallTaskLog.setTaskId(taskCreateResp.getId());
                ImportTaskTelRequest importTaskTelRequest = new ImportTaskTelRequest();
                importTaskTelRequest.setTaskId(taskCreateResp.getId());
                importTaskTelRequest.setName(autoCallTaskRequest.getTaskName() + formattedNow + "-批次");

                List<ImportTaskTelRequest.TaskTelListDTO> telList = new ArrayList<>();
                for (String callPhone : autoCallTaskRequest.getCallTelList()) {
                    ImportTaskTelRequest.TaskTelListDTO taskTelListDTO = new ImportTaskTelRequest.TaskTelListDTO();
                    taskTelListDTO.setTel(callPhone);
                    telList.add(taskTelListDTO);
                }
                importTaskTelRequest.setTaskTelList(telList);

                Response<CticloudResp<ImportTaskTelResponse>> importTaskTelResponse = taskApi.importTaskTel(importTaskTelRequest).execute();
                autoCallTaskLog.setTaskImportTelRespones(JSONObject.toJSONString(importTaskTelResponse.body()));
                autoCallTaskLog.setTaskImportTelIsSuccess(0);
                if (importTaskTelResponse.body() != null && importTaskTelResponse.body().getResult() != null && importTaskTelResponse.body().getResult() == 0) {
                    autoCallTaskLog.setTaskImportTelIsSuccess(1);
                    Thread.sleep(500);
                    Response<CticloudResp<Object>> taskStartResponse = taskApi.taskStart(taskCreateResp.getId()).execute();
                    autoCallTaskLog.setStartTaskRespones(JSONObject.toJSONString(taskStartResponse.body()));
                    autoCallTaskLog.setStartTaskIsSuccess(0);
                    if (taskStartResponse.body() != null && taskStartResponse.body().getResult() != null && taskStartResponse.body().getResult() == 0) {
                        autoCallTaskLog.setStartTaskIsSuccess(1);
                        taskService.saveLog(autoCallTaskLog);
                        return;
                    } else {
                        log.info("自动外呼任务-启动任务失败" + JSONObject.toJSONString(importTaskTelResponse.body()));
                    }
                } else {
                    log.info("自动外呼任务-导入手机号失败" + JSONObject.toJSONString(importTaskTelResponse.body()));
                }
            }
        } else {
            log.info("自动外呼任务-创建任务失败" + JSONObject.toJSONString(taskCreateResponse.body()));
        }
        taskService.saveLog(autoCallTaskLog);
        throw new BusinessException(OuterExportErrorCode.COMMON_AUTO_CALL_CREATE_ERROR);
    }

    @Override
    public void autoDelAutoCallTask() throws IOException {
        Response<CticloudResp<TaskQueryResult>> response = taskApi.queryTask(null, null, 3, null, null, null, null, null).execute();
        if (!response.isSuccessful()) {
            throw new BusinessException(OuterExportErrorCode.COMMON_AUTO_CALL_STATUS_ERROR);
        }

        if (response.body() != null && response.body().getResult() != null && response.body().getResult() == 0 && response.body().getData() != null && CollectionUtils.isNotEmpty(response.body().getData().getTaskProperties())) {
            List<TaskQueryResult.TaskProperty> taskProperties = response.body().getData().getTaskProperties();
            log.info("清除已结束的专车派单自动外呼任务，所有自动外呼任务数据：{}", JSONObject.toJSONString(taskProperties));
            for (TaskQueryResult.TaskProperty taskProperty : taskProperties) {
                if (taskProperty != null && taskProperty.getStatus() != null && taskProperty.getStatus().equals("3")
                        && StringUtils.isNotBlank(taskProperty.getName()) && (taskProperty.getName().contains("专车派单")
                        || taskProperty.getName().contains("同意报价") || taskProperty.getName().contains("专票货源提醒")
                        || taskProperty.getName().contains("诈骗预警") || taskProperty.getName().contains("继续找车"))) {
                    log.info("清除已结束的专车派单自动外呼任务，任务ID：{}，任务名称：{}", taskProperty.getId(), taskProperty.getName());
                    taskApi.delete(taskProperty.getId()).execute();
                }
            }
        }
    }

    private static boolean nowisNotBetweenNineAndTwentyAclock() {
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.of(9, 0);
        LocalTime end = LocalTime.of(20, 0);
        return now.isBefore(start) || now.isAfter(end);
    }

}
