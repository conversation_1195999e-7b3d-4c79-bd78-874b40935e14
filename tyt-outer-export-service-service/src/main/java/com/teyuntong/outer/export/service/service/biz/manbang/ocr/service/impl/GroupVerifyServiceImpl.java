package com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.impl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.service.biz.manbang.constant.Constants;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportQCVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupVerifyService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQCVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import com.teyuntong.outer.export.service.service.common.property.GroupOcrProperties;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(GroupOcrProperties.class)
public class GroupVerifyServiceImpl implements GroupVerifyService {

    private final GroupOcrProperties groupOcrProperties;

    private final MBOpenPlatformUtil util;

    @Override
    @SneakyThrows
    public RoadTransportVerifyResultVO roadTransportVerify(RoadTransportVerifyDTO dto) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getRoadTransportVerifyServiceId())
                .add(Constants.VEHICLE_NO_KEY, dto.getVehicleNo())
                .add(Constants.VEHICLE_VIN_KEY, dto.getVehicleVIN())
                .build();
        String responseBodyStr = util.doPost(Constants.ROAD_TRANSPORT_VERIFY_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            return JSON.parseObject(responseBodyStr, RoadTransportVerifyResultVO.class);
        }
        return null;
    }

    @Override
    public RoadTransportQCVerifyResultVO roadTransportQCVerify(RoadTransportQCVerifyDTO dto) {
        RequestBody body = new FormBody.Builder()
                .add(Constants.APP_SERVICE_ID_KEY, groupOcrProperties.getRoadTransportQcVerifyServiceId())
                .add(Constants.ID_CARD_KEY, dto.getIdCard())
                .build();
        String responseBodyStr = util.doPost(Constants.ROAD_TRANSPORT_QC_VERIFY_URL, body);
        if (StringUtils.isNotBlank(responseBodyStr)) {
            return JSON.parseObject(responseBodyStr, RoadTransportQCVerifyResultVO.class);
        }
        return null;
    }
}

