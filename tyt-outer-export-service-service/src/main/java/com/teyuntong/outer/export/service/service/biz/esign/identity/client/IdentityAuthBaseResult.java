package com.teyuntong.outer.export.service.service.biz.esign.identity.client;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:39
 */
@NoArgsConstructor
@Data
public class IdentityAuthBaseResult<T> {

    private Integer code;
    private String message;
    private T data;

    public static boolean checkSuccess(IdentityAuthBaseResult<?> authBaseResult){
        return (authBaseResult != null && authBaseResult.getCode() == 0);
    }

}
