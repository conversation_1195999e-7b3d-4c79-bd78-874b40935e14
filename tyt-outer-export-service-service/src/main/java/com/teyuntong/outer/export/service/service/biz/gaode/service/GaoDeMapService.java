package com.teyuntong.outer.export.service.service.biz.gaode.service;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.outer.export.service.client.gaode.vo.GaoDeRegeoVo;

/**
 * 高德地图服务
 *
 * <AUTHOR>
 * @since 2024/01/16 10:04
 */
public interface GaoDeMapService {
    /**
     * 根据经纬度逆地理坐标
     *
     * @param longitude
     * @param latitude
     * @return
     */
    GaoDeRegeoVo regeoAddress(String longitude, String latitude);

    /**
     * 计算距离
     */
    DistanceRpcVO calculateDistance(DistanceRpcDTO dto);

    /**
     * 调用高德货车导航服务，返回导航数据
     */
    String navigateTruck(DistanceRpcDTO dto);
}
