package com.teyuntong.outer.export.service.service.biz.manbang.util;

import com.google.common.collect.Maps;
import com.teyuntong.outer.export.service.service.common.constant.OpenAcctConstant;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * httpclient工具类
 *
 * <AUTHOR> Zhang
 */
public class HttpClientUtils {

    private static Logger LOG = LoggerFactory.getLogger(HttpClientUtils.class);

    private static PoolingHttpClientConnectionManager connMgr;
    private static final int MAX_TOTAL = 300;
    private static RequestConfig requestConfig;
    private static final int CONNECT_TIMEOUT = 20000;
    private static final int SOCKET_TIMEOUT = 20000;
    private static final int REQUEST_TIMEOUT = 20000;
    private static final String CHARSET = "UTF-8";

    private static final ConnectionKeepAliveStrategy connectionKeepAliveStrategy;

    static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(MAX_TOTAL);
        connMgr.setDefaultMaxPerRoute(MAX_TOTAL);

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 请求超时时间
        configBuilder.setConnectTimeout(CONNECT_TIMEOUT);
        // 连接不够时等待超时时间，不设置将阻塞线程
        configBuilder.setConnectionRequestTimeout(REQUEST_TIMEOUT);
        // 等待数据超时时间
        configBuilder.setSocketTimeout(SOCKET_TIMEOUT);

        LOG.debug("Http参数设置,连接超时时间[{}],Socket超时时间[{}],请求超时时间[{}]", CONNECT_TIMEOUT, SOCKET_TIMEOUT, REQUEST_TIMEOUT);

        requestConfig = configBuilder.build();

        connectionKeepAliveStrategy = (httpResponse, httpContext) -> 30 * 1000;
    }

    public static String doGet(String url, Map<String, Object> params) {

        String result = null;

        if (StringUtils.isEmpty(url)) {
            LOG.info("warn:doGet url is null or '' ");
            return result;
        }

        List<NameValuePair> pairList = new ArrayList<>(params.size());
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            pairList.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
        }

        CloseableHttpClient httpclient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connMgr)
                .setKeepAliveStrategy(connectionKeepAliveStrategy)
                .build();
        CloseableHttpResponse response = null;
        InputStream instream = null;
        try {
            URIBuilder URIBuilder = new URIBuilder(url);
            URIBuilder.setCharset(Charset.forName(CHARSET));
            URIBuilder.addParameters(pairList);
            URI uri = URIBuilder.build();
            HttpGet httpGet = new HttpGet(uri);
            response = httpclient.execute(httpGet);

            LOG.info("doGet statusCode:{}", response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                instream = entity.getContent();
                result = IOUtils.toString(instream, CHARSET);
            }


        } catch (Exception e) {
            LOG.info("doGet request exception:", e);
        } finally {
            closeConnection(response, instream);
            LOG.info("close  instream response httpClient  connection succ");
        }
        return result;
    }


    /**
     * 发送post请求，JSON形式
     *
     * @param url
     * @param json
     * @return
     */
    public static String doPost(String url, String json) {

        if (StringUtils.isEmpty(url)) {
            LOG.info("warn:doPost url is null or '' ");
            return null;
        }

        HttpPost httpPost = new HttpPost(url);
        StringEntity stringEntity = new StringEntity(json, CHARSET);
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);

        return send(httpPost, false);
    }

    /**
     * 发送post请求，带签名
     *
     * @param url
     * @param paramsJson
     * @param sign
     * @return
     */
    public static String doPostWithSign(String url, String paramsJson, String sign) {

        HttpPost httpPost = new HttpPost(url);
        StringEntity stringEntity = new StringEntity(paramsJson, CHARSET);
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);

        // 加签
        httpPost.setHeader("sign", sign);
        LOG.info("\n#### url = {}\n" + "#### params ={}\n" + "#### sign ={}", url, paramsJson, sign);

        return send(httpPost, false);
    }

    /**
     * 发送ssl post请求
     *
     * @param url
     * @param json
     * @return
     */
    public static String doPostSSL(String url, String json) {
        // TODO

        return "";
    }

    /**
     * 发送post请求，JSON形式
     */
    public static String doPostWithToken(String url, String json, String token) {

        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            LOG.info("warn:doPostWithToken url is null or '' ");
            return null;
        }

        HttpPost httpPost = new HttpPost(url);
        StringEntity stringEntity = new StringEntity(json, CHARSET);
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(token)){
            httpPost.setHeader("token", token);
        }
        return send(httpPost, false);
    }


    /**
     * 发送Post请求
     *
     * @param httpPost
     * @return
     */
    private static String send(HttpPost httpPost, boolean isSsl) {

        String result = null;
        CloseableHttpClient httpClient = null;
        InputStream instream = null;
        CloseableHttpResponse response = null;

        try {
            if (isSsl) {
                SSLContext sslcontext = SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy())
                        .build();
                SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext);
                httpClient = HttpClients.custom().setConnectionManager(connMgr)
                        .setSSLSocketFactory(sslsf)
                        .setDefaultRequestConfig(requestConfig)
                        .setKeepAliveStrategy(connectionKeepAliveStrategy)
                        .build();
            } else {
                httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).setConnectionManager(connMgr)
                        .build();
            }
            response = httpClient.execute(httpPost);

            LOG.info("doPost statusCode:{}", response.getStatusLine().getStatusCode());
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                instream = entity.getContent();
                result = IOUtils.toString(instream, CHARSET);
                //LOG.info("doPost Result:{}", result);
            }
        } catch (Exception e) {
            LOG.info("doPost request exception:", e);
        } finally {
            closeConnection(response, instream);
            LOG.info("close  instream response httpClient  connection succ");
        }

        return result;
    }



    private static void closeConnection(CloseableHttpResponse response, InputStream inputStream) {
        if (null != inputStream) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (null != response) {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
      * <AUTHOR> Lion
      * @Description 满帮from表单提交
      * @Param [url, json, sign, appId]
      * @return java.lang.String
      * @Date 2022/3/25 11:47
      */
    public static String doMbPostWithSign(String url, List list, String sign, String appId) {
        HttpPost httpPost = new HttpPost(url);
//        StringEntity stringEntity = new StringEntity(list, CHARSET);
//        stringEntity.setContentType("application/json");
        UrlEncodedFormEntity entity = null;
        try {
            entity = new UrlEncodedFormEntity(list, CHARSET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        httpPost.setEntity(entity);

        // 加签
        httpPost.setHeader("x-sign", sign);
        httpPost.addHeader("x-ap-id",appId);
        LOG.info("\n#### url = {}\n" + "#### params ={}\n" + "#### sign ={}", url,list,sign);

        return send(httpPost, false);
    }
}
