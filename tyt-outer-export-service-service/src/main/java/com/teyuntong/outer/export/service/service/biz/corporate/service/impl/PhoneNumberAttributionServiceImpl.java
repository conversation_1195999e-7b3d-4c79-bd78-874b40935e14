package com.teyuntong.outer.export.service.service.biz.corporate.service.impl;

import com.teyuntong.outer.export.service.service.biz.corporate.client.AliyunMobilePhoneAPI;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.PhoneNumberAttribution;
import com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper.PhoneNumberAttributionMapper;
import com.teyuntong.outer.export.service.client.corporate.vo.PhoneLocale;
import com.teyuntong.outer.export.service.service.biz.corporate.service.PhoneNumberAttributionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 10
 * @date 2020/06/28
 */
@Slf4j
@Service
public class PhoneNumberAttributionServiceImpl implements PhoneNumberAttributionService {

    @Autowired
    private PhoneNumberAttributionMapper mapper;

    /**
     * 示例：
     * {
     * "data":{
     * "num":1813843,
     * "isp":"电信",
     * "prov":"广东",
     * "city":"深圳",
     * "types":"中国电信",
     * "city_code":"0755",
     * "area_code":"440300",
     * "zip_code":"518000",
     * "lng":"114.057868",
     * "lat":"22.543099"
     * },
     * "ret":200,
     * "msg":"success",
     * "log_id":"2ccfb06330b848ad87cb7fa2458c34e8"
     * }
     */
    @Override
    public PhoneLocale addAndGetMobileLocale(String mobile) {
        PhoneLocale locale = null;
        // 查询数据库存在手机号数据
        PhoneNumberAttribution attribution = this.getAttribution(mobile);
        if (attribution != null) {
            locale = this.attribution2PhoneLocale(attribution);
            return locale;
        }
        // 查询远程API接口数据
        PhoneNumberAttribution remoteAttribution = AliyunMobilePhoneAPI.getMobileLocale(mobile);
        if (remoteAttribution != null) {
            // 存入数据库
            int i = this.insertAttribution(remoteAttribution);
            locale = this.attribution2PhoneLocale(remoteAttribution);
            return locale;
        }
        return locale;
    }

    /**
     * PhoneNumberAttribution 转换为 PhoneLocale
     *
     * @param attribution PhoneNumberAttribution
     * @return PhoneLocale
     */
    private PhoneLocale attribution2PhoneLocale(PhoneNumberAttribution attribution) {
        PhoneLocale locale = new PhoneLocale();
        locale.setOperation(attribution.getIsp());
        locale.setProvince(attribution.getProv());
        locale.setCity(attribution.getCity());
        locale.setStatus(attribution.getStatus());
        locale.setVersion(attribution.getVersion());
        locale.setCode(attribution.getCode());
        locale.setMessage(attribution.getMsg());
        return locale;
    }

    @Override
    public PhoneNumberAttribution getAttribution(String mobile) {
//        PhoneNumberAttribution attribution = new PhoneNumberAttribution();
//        attribution.setCellPhone(mobile);
////        QueryWrapper<PhoneNumberAttribution> queryWrapper = new QueryWrapper<>();
////        queryWrapper.eq("cell_phone", mobile);
//        return mapper.selectOne(attribution);
        return mapper.getAttribution(mobile);
    }

    @Override
    public int insertAttribution(PhoneNumberAttribution attribution) {
        return mapper.addAttribution(attribution);
    }
}
