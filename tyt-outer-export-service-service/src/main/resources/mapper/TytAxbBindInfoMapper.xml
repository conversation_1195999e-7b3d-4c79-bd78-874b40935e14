<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper.TytAxbBindInfoMapper">
    <resultMap id="BaseResultMap"
               type="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="third_party_id" jdbcType="VARCHAR" property="thirdPartyId"/>
        <result column="third_party_type" jdbcType="SMALLINT" property="thirdPartyType"/>
        <result column="tel_a" jdbcType="VARCHAR" property="telA"/>
        <result column="tel_b" jdbcType="VARCHAR" property="telB"/>
        <result column="tel_x" jdbcType="VARCHAR" property="telX"/>
        <result column="is_permanent" jdbcType="BIT" property="isPermanent"/>
        <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_type" jdbcType="SMALLINT" property="bizType"/>
        <result column="extra_field" jdbcType="VARCHAR" property="extraField"/>
    </resultMap>

    <insert id="insertSelective"
            parameterType="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo">
        INSERT INTO tyt_axb_bind_info (tel_a,
                                       third_party_id,
                                       tel_b,
                                       tel_x,
                                       biz_id,
                                       biz_type,
                                       extra_field,
                                       third_party_type,
                                       del_flag,
                                       create_time,
                                       update_time,
                                       is_permanent,
                                       expiration_date)
        VALUES (#{telA},
                #{thirdPartyId},
                #{telB},
                #{telX},
                #{bizId},
                #{bizType},
                #{extraField},
                #{thirdPartyType},
                #{delFlag},
                #{createTime},
                #{updateTime},
                #{isPermanent},
                #{expirationDate})
    </insert>

    <select id="selectByThirdPartyId" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_axb_bind_info
        WHERE third_party_id = #{thirdPartyId}
        <if test="delFlag != null">
            AND del_flag = #{delFlag}
        </if>
        LIMIT 1
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT *
        FROM
        tyt_axb_bind_info
        WHERE
        biz_type = #{bizType}
        AND biz_id = #{bizId}
        AND del_flag = FALSE
        <if test="telA != null and telA != ''">
            AND tel_a = #{telA}
        </if>
        <if test="telB != null and telB != ''">
            AND tel_b = #{telB}
        </if>
        <if test="extraField != null and extraField != ''">
            AND extra_field = #{extraField}
        </if>
    </select>

    <update id="updateSelective" parameterType="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.TytAxbBindInfo">
        UPDATE tyt_axb_bind_info
        <set>
            <if test="telA != null">
                tel_a = #{telA},
            </if>
            <if test="telB != null">
                tel_b = #{telB},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="expirationDate != null">
                expiration_date = #{expirationDate},
            </if>
            <if test="isPermanent != null">
                is_permanent = #{isPermanent},
            </if>
            <if test="extraField != null">
                extra_field = #{extraField},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>