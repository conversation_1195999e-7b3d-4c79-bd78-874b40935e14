<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.mapper.CticloudAxbLogMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.CticloudAxbLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sub_id" jdbcType="VARCHAR" property="subId" />
    <result column="request_body" jdbcType="VARCHAR" property="requestBody" />
    <result column="request_param" jdbcType="VARCHAR" property="requestParam" />
    <result column="response" jdbcType="VARCHAR" property="response" />
    <result column="is_request_success" jdbcType="TINYINT" property="isRequestSuccess" />
    <result column="is_cticloud_success" jdbcType="BIT" property="isCticloudSuccess" />
    <result column="request_type" jdbcType="INTEGER" property="requestType" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.teyuntong.outer.export.service.service.biz.cticloud.axb.mybatis.entity.CticloudAxbLog">
      INSERT INTO cticloud_axb_log (
      request_type,
      create_time,
      update_time,
      request_url,
      sub_id,
      <if test="requestBody != null and requestBody != ''">
          request_body,
      </if>
      request_param,
      is_request_success,
      is_cticloud_success,
      response
      )
      VALUES (
      #{requestType},
      #{createTime},
      #{updateTime},
      #{requestUrl},
      #{subId},
      <if test="requestBody != null and requestBody != ''">
          #{requestBody},
      </if>
      #{requestParam},
      #{isRequestSuccess},
      #{isCticloudSuccess},
      #{response}
      )
  </insert>
</mapper>