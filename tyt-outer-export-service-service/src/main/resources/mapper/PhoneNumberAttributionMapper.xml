<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper.PhoneNumberAttributionMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.PhoneNumberAttribution">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="prov" jdbcType="VARCHAR" property="prov" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="isp" jdbcType="VARCHAR" property="isp" />
    <result column="types" jdbcType="VARCHAR" property="types" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="code" jdbcType="INTEGER" property="code" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <!-- 根据手机号查询一条记录-->
  <select id="getAttribution" resultMap="BaseResultMap">
    select * from phone_number_attribution where cell_phone = #{cellPhone} limit 1
  </select>


  <insert id="addAttribution">
    insert ignore into phone_number_attribution(
      cell_phone, num, prov, city, isp, types, lng, lat, area_code, city_code, zip_code,
      code, msg, status, version
    )
    values(
    #{cellPhone}, #{num}, #{prov}, #{city}, #{isp}, #{types}, #{lng}, #{lat}, #{areaCode}, #{cityCode}, #{zipCode},
    #{code}, #{msg}, #{status}, #{version}
    )
  </insert>


</mapper>