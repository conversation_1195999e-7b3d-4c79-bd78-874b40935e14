<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.outer.export.service.service.biz.corporate.mybatis.mapper.CorporateBaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.outer.export.service.service.biz.corporate.mybatis.entity.CorporateBaseInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="property3" jdbcType="VARCHAR" property="property3" />
    <result column="history_names" jdbcType="VARCHAR" property="historyNames" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="org_number" jdbcType="VARCHAR" property="orgNumber" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="reg_number" jdbcType="VARCHAR" property="regNumber" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="website_list" jdbcType="VARCHAR" property="websiteList" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="company_org_type" jdbcType="VARCHAR" property="companyOrgType" />
    <result column="reg_status" jdbcType="VARCHAR" property="regStatus" />
    <result column="cancel_date" jdbcType="TIMESTAMP" property="cancelDate" />
    <result column="reg_capital" jdbcType="VARCHAR" property="regCapital" />
    <result column="staff_num_range" jdbcType="VARCHAR" property="staffNumRange" />
    <result column="industry" jdbcType="VARCHAR" property="industry" />
    <result column="bond_num" jdbcType="VARCHAR" property="bondNum" />
    <result column="bond_name" jdbcType="VARCHAR" property="bondName" />
    <result column="revoke_date" jdbcType="TIMESTAMP" property="revokeDate" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="used_bond_name" jdbcType="VARCHAR" property="usedBondName" />
    <result column="from_time" jdbcType="TIMESTAMP" property="fromTime" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="social_staff_num" jdbcType="INTEGER" property="socialStaffNum" />
    <result column="actual_capital_currency" jdbcType="VARCHAR" property="actualCapitalCurrency" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="to_time" jdbcType="TIMESTAMP" property="toTime" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="actual_capital" jdbcType="VARCHAR" property="actualCapital" />
    <result column="estiblish_time" jdbcType="TIMESTAMP" property="estiblishTime" />
    <result column="reg_institute" jdbcType="VARCHAR" property="regInstitute" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="reg_location" jdbcType="VARCHAR" property="regLocation" />
    <result column="reg_capital_currency" jdbcType="VARCHAR" property="regCapitalCurrency" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="bond_type" jdbcType="VARCHAR" property="bondType" />
    <result column="percentile_score" jdbcType="INTEGER" property="percentileScore" />
    <result column="category_middle" jdbcType="VARCHAR" property="categoryMiddle" />
    <result column="category_big" jdbcType="VARCHAR" property="categoryBig" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_small" jdbcType="VARCHAR" property="categorySmall" />
    <result column="is_micro_ent" jdbcType="INTEGER" property="isMicroEnt" />
    <result column="base" jdbcType="VARCHAR" property="base" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <insert id="addCorporateBaseInfo">
      insert
      ignore into corporate_base_info(
      id, name, legal_person_name, property3, history_names, alias, credit_code, org_number, tax_number, reg_number, type, website_list, phone_number, company_org_type, reg_status, cancel_date, reg_capital, staff_num_range, industry, bond_num, bond_name, revoke_date, revoke_reason, used_bond_name, from_time, approved_time, social_staff_num, actual_capital_currency, cancel_reason, to_time, email, actual_capital, estiblish_time, reg_institute, business_scope, reg_location, reg_capital_currency, tags, bond_type, percentile_score, category_middle, category_big, category, category_small, is_micro_ent, base, city, district
    )
    values(
      #{id},
      #{name},
      #{legalPersonName},
      #{property3},
      #{historyNames},
      #{alias},
      #{creditCode},
      #{orgNumber},
      #{taxNumber},
      #{regNumber},
      #{type},
      #{websiteList},
      #{phoneNumber},
      #{companyOrgType},
      #{regStatus},
      #{cancelDate},
      #{regCapital},
      #{staffNumRange},
      #{industry},
      #{bondNum},
      #{bondName},
      #{revokeDate},
      #{revokeReason},
      #{usedBondName},
      #{fromTime},
      #{approvedTime},
      #{socialStaffNum},
      #{actualCapitalCurrency},
      #{cancelReason},
      #{toTime},
      #{email},
      #{actualCapital},
      #{estiblishTime},
      #{regInstitute},
      #{businessScope},
      #{regLocation},
      #{regCapitalCurrency},
      #{tags},
      #{bondType},
      #{percentileScore},
      #{categoryMiddle},
      #{categoryBig},
      #{category},
      #{categorySmall},
      #{isMicroEnt},
      #{base},
      #{city},
      #{district}
      )
  </insert>

  <!-- 根据name在corporate_base_info表里查询一条数据 -->
  <select id="getCorporateBaseInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
      select *
      from corporate_base_info
      where name = #{name} limit 1;
  </select>

</mapper>