<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.mapper.AutoCallTaskLogMapper">

  <insert id="insert" parameterType="com.teyuntong.outer.export.service.service.biz.cticloud.task.mybatis.entity.AutoCallTaskLog">
    INSERT INTO auto_call_task_log (
        task_id, task_name, task_call_value, call_Tel_List, create_task_is_success, create_task_respones,
      task_import_tel_is_success, task_import_tel_respones, start_task_is_success, start_task_respones, create_time
    ) VALUE (
        #{taskId}, #{taskName}, #{taskCallValue}, #{callTelList}, #{createTaskIsSuccess}, #{createTaskRespones},
               #{taskImportTelIsSuccess}, #{taskImportTelRespones}, #{startTaskIsSuccess}, #{startTaskRespones}, #{createTime}
             )
  </insert>

</mapper>