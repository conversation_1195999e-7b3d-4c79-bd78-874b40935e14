package com.teyuntong.outer.export.service.service.biz.manbang.sensitivewords.service.impl;

import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.outer.export.service.service.biz.manbang.util.MBOpenPlatformUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class SensitiveWordsServiceImplTest {

    @Mock
    private MBOpenPlatformUtil util;

    @InjectMocks
    private SensitiveWordsServiceImpl sensitiveWordsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testNewCargoSensitiveCheck_Accept() throws Exception {
        // 模拟成功返回（未命中敏感词）
        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";
        when(util.doPost(anyString(), any())).thenReturn(acceptResponse);

        SensitiveWordsVO result = sensitiveWordsService.newCargoSensitiveCheck(123L, "正常货物", "正常描述");

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testNewCargoSensitiveCheck_HitSensitive() throws Exception {
        // 模拟命中敏感词的返回
        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";
        when(util.doPost(anyString(), any())).thenReturn(sensitiveResponse);

        SensitiveWordsVO result = sensitiveWordsService.newCargoSensitiveCheck(123L, "含有火药和枪的货物", "傻逼描述");

        assertNotNull(result);
        assertTrue(result.getTaskContentHitSensitiveWords());
        assertTrue(result.getMachineRemarkHitSensitiveWords());
        assertEquals(3, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("枪"));
        assertTrue(result.getSensitiveWords().contains("火药"));
        assertTrue(result.getSensitiveWords().contains("傻逼"));
    }

    @Test
    void testParseSensitiveCheckResponse_Accept() throws Exception {
        // 使用反射调用私有方法进行测试
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String acceptResponse = "{\"decision\":\"accept\",\"rules\":[],\"extend\":null}";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, acceptResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords());
        assertTrue(result.getSensitiveWords().isEmpty());
    }

    @Test
    void testParseSensitiveCheckResponse_HitSensitive() throws Exception {
        // 使用反射调用私有方法进行测试
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String sensitiveResponse = "{\"decision\":{\"ruleCode\":\"3113\",\"ruleDesc\":{\"cargoName\":{\"ruleList\":[\"SENSITIVE_DICT\",\"FORBID_DICT\"],\"hitDetails\":[{\"beginIndex\":10,\"endIndex\":11,\"hitText\":\"枪\",\"index\":105},{\"beginIndex\":3,\"endIndex\":5,\"hitText\":\"火药\",\"index\":2740}]},\"description\":{\"ruleList\":[\"SENSITIVE_DICT\"],\"hitDetails\":[{\"beginIndex\":1,\"endIndex\":3,\"hitText\":\"傻逼\",\"index\":305}]}}},\"rules\":[\"CargoSensitiveCheck_sensitiveRule\"],\"extend\":\"\"}";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, sensitiveResponse);

        assertNotNull(result);
        assertTrue(result.getTaskContentHitSensitiveWords());
        assertTrue(result.getMachineRemarkHitSensitiveWords());
        assertEquals(3, result.getSensitiveWords().size());
        assertTrue(result.getSensitiveWords().contains("枪"));
        assertTrue(result.getSensitiveWords().contains("火药"));
        assertTrue(result.getSensitiveWords().contains("傻逼"));
    }

    @Test
    void testParseSensitiveCheckResponse_InvalidJson() throws Exception {
        // 测试无效JSON的情况
        Method method = SensitiveWordsServiceImpl.class.getDeclaredMethod("parseSensitiveCheckResponse", String.class);
        method.setAccessible(true);

        String invalidResponse = "invalid json";
        SensitiveWordsVO result = (SensitiveWordsVO) method.invoke(sensitiveWordsService, invalidResponse);

        assertNotNull(result);
        assertFalse(result.getTaskContentHitSensitiveWords());
        assertFalse(result.getMachineRemarkHitSensitiveWords()); // 解析失败时为了不阻断流程认为未命中敏感词
        assertTrue(result.getSensitiveWords().isEmpty());
    }
}
