package service;

import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.FileUpLoadResp;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJUploadImgService;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.OutEnterpriseRealService;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/5/11 11:45
 */
@Disabled
class OutEnterpriseRealServiceTest extends TestBase {

    @Autowired
    private OutEnterpriseRealService outEnterpriseRealService;

    @Autowired
    private HBWJUploadImgService hBWJUploadImgService;

    @Test
    void uploadFile() throws Exception{

        // 将 "myfile.txt" 替换为实际本地文件路径

        File file = new File("E://image/boots-40.png");

        //File file = new File("http://devimage.teyuntong.net/dispatch/APP/2024-05-11/0100200043088043ee928ffc9ecbdaef7bd5e06ec6b.jpg");

        RequestBody fileRQ = RequestBody.create(MediaType.parse("image/*"), file);
        MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), fileRQ);


        /*FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file",
                file.getName(), "text/plain", input);*/
        FileUpLoadResp fileUpLoadResp = hBWJUploadImgService.uploadFile(part);

        System.out.println(fileUpLoadResp.getFileUrl());

        /* String fileName = file.getName();

        String contentType = FileTypeEnum.getContentType(fileName);

        byte[] fileBytes = FileUtils.readFileToByteArray(file);

        FileDataInfo fileDataInfo = new FileDataInfo(fileBytes, fileName);

        String s = outEnterpriseRealService.uploadFile(fileDataInfo);

        System.out.println(s);

        assert true;*/
    }
}