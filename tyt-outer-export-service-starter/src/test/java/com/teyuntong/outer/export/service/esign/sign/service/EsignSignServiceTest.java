package com.teyuntong.outer.export.service.esign.sign.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.esign.vo.sign.LocalSafeSignPDF3rdReq;
import com.teyuntong.outer.export.service.client.esign.vo.sign.LocalSafeSignPDF3rdVO;
import com.teyuntong.outer.export.service.service.biz.esign.sign.service.EsignSignService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/11 18:04
 */
@Disabled
class EsignSignServiceTest extends TestBase {

    @Autowired
    private EsignSignService esignSignService;

    @Test
    void localSafeSignPDF3rd() throws Exception{
        String path = "D:/source-files/test/card_img/enterprise/testPdf.json";

        String reqJson = FileUtils.readFileToString(new File(path), StandardCharsets.UTF_8);

        LocalSafeSignPDF3rdReq req = JSON.parseObject(reqJson, LocalSafeSignPDF3rdReq.class);

        LocalSafeSignPDF3rdVO localSafeSignPDF3rdVO = esignSignService.localSafeSignPDF3rd(req);

        Assertions.assertTrue(true);

    }
}