package com.teyuntong.outer.export.service.volcengine;

import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.corporate.service.MobileRpcService;
import com.teyuntong.outer.export.service.client.volcengine.service.BusinessSecurityRpcService;
import com.teyuntong.outer.export.service.client.volcengine.vo.IdCardTwoElementVerifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;

@Slf4j
@Disabled
class BusinessSecurityRpcServiceTest extends TestBase {

    @Autowired
    BusinessSecurityRpcService businessSecurityRpcService;

    @Test
    void getById() throws Exception {
        IdCardTwoElementVerifyDTO dto = new IdCardTwoElementVerifyDTO();
        dto.setIdCardName("刘刚");
        dto.setIdCardNo("230822198801192016");
        businessSecurityRpcService.verifyTwoElement(dto);
    }

}