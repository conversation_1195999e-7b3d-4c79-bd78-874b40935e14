package com.teyuntong.outer.export.service.esign.controller;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.Telecom3FactorsApiReq;
import com.teyuntong.outer.export.service.client.esign.vo.identity.Telecom3FactorsVerifyReq;
import com.teyuntong.outer.export.service.service.rpc.esign.EsignIndividualIdentityAuthController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class EsignIndividualIdentityAuthControllerTest extends TestBase {

    @Autowired
    private EsignIndividualIdentityAuthController esignIndividualIdentityAuthController;

    @Test
    void telecom3FactorsIdentityAuth() throws Exception {
        Telecom3FactorsApiReq req = new Telecom3FactorsApiReq();
        req.setName("123");
        req.setIdNo("123");
        req.setMobileNo("123");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/identity/auth/individual/telecom3Factors")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }

    @Test
    void telecom3FactorsVerify() throws Exception {
        Telecom3FactorsVerifyReq req = new Telecom3FactorsVerifyReq();
        req.setFlowId("123");
        req.setAuthcode("123456");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/identity/auth/individual/telecom3Factors/verify")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }

    @Test
    void telecom3FactorsAuthCode() throws Exception {
        Telecom3FactorsVerifyReq req = new Telecom3FactorsVerifyReq();
        req.setFlowId("123");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/identity/auth/individual/telecom3Factors/authCode")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }

}