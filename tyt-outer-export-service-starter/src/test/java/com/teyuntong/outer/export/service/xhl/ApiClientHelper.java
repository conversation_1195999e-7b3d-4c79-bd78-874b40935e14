package com.teyuntong.outer.export.service.xhl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.service.biz.xhl.util.XhlSignUtil;
import org.junit.jupiter.api.Disabled;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.Map;

@Disabled
public class ApiClientHelper {

    //应用id
    private String appId;
    //api密钥
    private String apiKey;
    //连接超时时间
    private int ConnectTimeout = 30 * 1000;
    //读取超时时间
    private int ReadTimeout = 60 * 1000;

    private String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";

    private String CONTENT_TYPE_GET = "text/plain;charset=UTF-8";
    //
    public String ENCODE_UTF_8 = "UTF-8";

    private final String BOUNDARY = "-----------------7d4a6d158c9";

    private final String TWO_HYPHENS = "--";

    private final String END = "\r\n";

    /**
     * 构造函数，初始化ApiClientHelper对象
     *
     * @param appId  应用ID
     * @param apiKey 应用密钥
     */
    private ApiClientHelper(String appId, String apiKey) {
        this.appId = appId;
        this.apiKey = apiKey;
    }

    /**
     * 创建ApiClientHelper实例
     *
     * @param appId  应用ID
     * @param apiKey 应用密钥
     * @return ApiClientHelper实例
     */
    public static ApiClientHelper build(String appId, String apiKey) {
        return new ApiClientHelper(appId, apiKey);
    }

    /**
     * 发送POST请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String post(String url, Object body) {
        return post(url, getParamsString(url, body), CONTENT_TYPE_FORM, ENCODE_UTF_8);
    }

    /**
     * 发送特定格式的POST请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String postYHB(String url, Object body) {
        return post(url, getParamsStringYHB(url, body), CONTENT_TYPE_FORM, ENCODE_UTF_8);
    }

    /**
     * 发送POST请求
     *
     * @param url         请求URL
     * @param params      请求参数
     * @param contentType 内容类型
     * @param encode      编码方式
     * @return 响应内容
     */
    private String post(String url, String params, String contentType, String encode) {
        StringBuilder sb = new StringBuilder();
        HttpURLConnection conn = null;
        BufferedReader br = null;
        OutputStream output = null;
        try {
            // 创建HTTP连接
            conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Accept-Charset", encode);
            conn.setRequestProperty("Content-Type", contentType);
            conn.setConnectTimeout(ConnectTimeout);
            conn.setReadTimeout(ReadTimeout);
            // 写入请求参数
            output = conn.getOutputStream();
            output.write(params.getBytes(encode));
            output.flush();
            // 读取响应内容
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), ENCODE_UTF_8));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (output != null) {
                    output.close();
                }
                if (br != null) {
                    br.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public String get(String url) {
        return get(url, "");
    }

    /**
     * 发送带有请求体的GET请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String get(String url, Object body) {
        StringBuilder sb = new StringBuilder();
        HttpURLConnection conn = null;
        BufferedReader br = null;
        try {
            // 创建HTTP连接
            conn = (HttpURLConnection) (new URL(getUrl(url, body))).openConnection();
            conn.setRequestMethod("GET");
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Accept-Charset", ENCODE_UTF_8);
            conn.setRequestProperty("Content-Type", CONTENT_TYPE_GET);
            conn.setConnectTimeout(ConnectTimeout);
            conn.setReadTimeout(ReadTimeout);
            // 读取响应内容
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), ENCODE_UTF_8));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return sb.toString();
    }

    /**
     * 下载文件
     *
     * @param url 下载URL
     * @return 下载的文件
     */
    public File download(String url) {
        HttpURLConnection conn = null;
        File file = null;
        try {
            // 创建HTTP连接
            conn = (HttpURLConnection) (new URL(getUrl(url))).openConnection();
            conn.setRequestMethod("GET");
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Accept-Charset", ENCODE_UTF_8);
            conn.setRequestProperty("Content-Type", CONTENT_TYPE_GET);
            conn.setConnectTimeout(ConnectTimeout);
            conn.setReadTimeout(ReadTimeout);
            // 获取文件名
            String fileName = null;
            if (conn.getHeaderField("Content-Disposition") != null) {
                String contentDisposition = URLDecoder.decode(conn.getHeaderField("Content-Disposition"), "UTF-8");
                contentDisposition = contentDisposition.replace("\"", "");
                fileName = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9, contentDisposition.length());
            } else {
                fileName = url.substring(url.lastIndexOf("/") + 1);
                if (fileName.contains("?")) {
                    fileName = fileName.substring(0, fileName.indexOf("?"));
                }
            }
            // 保存文件
//		file = FileUtils.createTempFileToWebApp(null,fileName==null?null:fileName.substring(fileName.lastIndexOf("."), fileName.length()));
            InputStream in = conn.getInputStream();
            Files.copy(in, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return file;
    }

    /**
     * 上传文件
     *
     * @param url       上传URL
     * @param paramsMap 请求参数
     * @param file      文件
     * @return 响应内容
     * @throws Exception 异常
     */
    public String upLoad(String url, Map<String, Object> paramsMap, File file) throws Exception {
        return upLoad(url, paramsMap, file.getName(), new FileInputStream(file));
    }

    /**
     * 上传文件
     *
     * @param url        上传URL
     * @param paramsMap  请求参数
     * @param fileName   文件名
     * @param fileStream 文件流
     * @return 响应内容
     * @throws IOException IO异常
     */
    public String upLoad(String url, Map<String, Object> paramsMap, String fileName, InputStream fileStream) throws IOException {
        StringBuilder result = new StringBuilder();
        BufferedReader br = null;
        OutputStream output = null;
        try {
            // 创建HTTP连接
            HttpURLConnection conn = (HttpURLConnection) (new URL(getUrl(url, paramsMap))).openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("Charset", ENCODE_UTF_8);
            conn.setRequestProperty("Accept-Charset", ENCODE_UTF_8);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=" + BOUNDARY);
            conn.setConnectTimeout(ConnectTimeout);
            conn.setReadTimeout(ReadTimeout);
            output = conn.getOutputStream();
            // 写入请求参数
            if (paramsMap != null) {
                StringBuilder sb = new StringBuilder();
                for (String key : paramsMap.keySet()) {
                    sb.append(TWO_HYPHENS + BOUNDARY + END);
                    sb.append("Content-Disposition: form-data; name=\"" + key + "\" " + END);
                    sb.append(END + URLEncoder.encode(paramsMap.get(key) + "", ENCODE_UTF_8) + END);
                }
                byte[] data = sb.toString().getBytes(ENCODE_UTF_8);
                output.write(data);
            }
            // 写入文件信息
            StringBuilder sb = new StringBuilder();
            sb.append(TWO_HYPHENS + BOUNDARY + END);
            sb.append("Content-Disposition: form-data; name=\"filename\"; filename=\"" + fileName + "\"" + END);
            sb.append(END);
            byte[] data = sb.toString().getBytes();
            output.write(data);
            // 上传文件
            byte[] buf = new byte[2048];
            int len = 0;
            while ((len = fileStream.read(buf)) > 0) {
                output.write(buf, 0, len);
            }
            String endStr = END + TWO_HYPHENS + BOUNDARY + TWO_HYPHENS + END;
            byte[] end_data = endStr.getBytes();
            output.write(end_data);
            output.flush();
            fileStream.close();
            // 读取响应内容
            InputStream is = conn.getInputStream();
            br = new BufferedReader(new InputStreamReader(conn.getInputStream(), ENCODE_UTF_8));
            String line = "";
            while ((line = br.readLine()) != null) {
                result.append(line);
            }
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (output != null) {
                output.close();
            }
            if (br != null) {
                br.close();
            }
        }
        return result.toString();
    }

    /**
     * 获得签名后的Get请求URL
     *
     * @param url  请求的基础URL
     * @param body 请求体，可以为空
     * @return 返回签名后的Get请求URL
     * @throws UnsupportedEncodingException 当编码不被支持时抛出
     */
    public String getUrl(String url, Object body) throws UnsupportedEncodingException {
        return String.format("%s?%s", url, getParamsString(url, body));
    }

    /**
     * 获得签名后的Get请求URL
     *
     * @param url 请求的基础URL
     * @return 返回签名后的Get请求URL
     * @throws UnsupportedEncodingException 当编码不被支持时抛出
     */
    public String getUrl(String url) throws UnsupportedEncodingException {
        return String.format("%s?%s", url, getParamsString(url, ""));
    }

    /**
     * 生成参数字符串
     *
     * @param url  请求的基础URL
     * @param body 请求体，可以为空
     * @return 返回生成的参数字符串
     */
    private String getParamsString(String url, Object body) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String bodyString = this.getBodyString(body);
        StringBuilder bodySB = null;
        try {
            bodySB = new StringBuilder();
            bodySB.append("appId=").append(URLEncoder.encode(appId, ENCODE_UTF_8));
            bodySB.append("&timestamp=").append(URLEncoder.encode(timestamp, ENCODE_UTF_8));
            bodySB.append("&sign=").append(URLEncoder.encode(getSign(url, timestamp, bodyString), ENCODE_UTF_8));
            bodySB.append("&body=").append(URLEncoder.encode(bodyString, ENCODE_UTF_8));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return bodySB.toString();
    }

    /**
     * 生成特定格式的参数字符串
     *
     * @param url  请求的基础URL
     * @param body 请求体，可以为空
     * @return 返回生成的特定格式的参数字符串
     */
    private String getParamsStringYHB(String url, Object body) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String bodyString = this.getBodyString(body);
        StringBuilder bodySB = null;
        try {
            bodySB = new StringBuilder();
            bodySB.append("appId=").append(URLEncoder.encode(appId, ENCODE_UTF_8));
            bodySB.append("&appKey=").append(URLEncoder.encode(apiKey, ENCODE_UTF_8));
            bodySB.append("&timestamp=").append(URLEncoder.encode(timestamp, ENCODE_UTF_8));
            bodySB.append("&content=").append(URLEncoder.encode(bodyString, ENCODE_UTF_8));
            bodySB.append("&sign=").append(URLEncoder.encode(getSignYHB(url, timestamp, bodyString), ENCODE_UTF_8));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return bodySB.toString();
    }

    /**
     * 获取相对于apiUrl的URL
     *
     * @param url 输入的完整或部分URL
     * @return 返回相对于apiUrl的URL
     */
    private String getRelUrl(String url) {
        url = url.trim();
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        if (url.toLowerCase().startsWith("http")) {
            url = url.substring(url.indexOf("//") + 2);
            return url.substring(url.indexOf("/"));
        } else if (url.startsWith("/")) {
            return url;
        } else {
            return "/" + url;
        }
    }

    /**
     * 获取请求体的字符串形式
     *
     * @param body 请求体对象，可以为空
     * @return 返回请求体的字符串形式
     */
    private String getBodyString(Object body) {
        if (body == null) {
            return "";
        } else if (body instanceof String) {
            return body.toString();
        } else {
            return JSON.toJSONString(body);
        }
    }

    /**
     * 生成签名
     *
     * @param url        请求的基础URL
     * @param timestamp  时间戳
     * @param bodyString 请求体的字符串形式
     * @return 返回生成的签名
     */
    private String getSign(String url, String timestamp, String bodyString) {
        return XhlSignUtil.getSign(appId, apiKey, this.getRelUrl(url), timestamp, bodyString);
    }

    /**
     * 生成特定格式的签名
     *
     * @param url        请求的基础URL
     * @param timestamp  时间戳
     * @param bodyString 请求体的字符串形式
     * @return 返回生成的特定格式的签名
     */
    private String getSignYHB(String url, String timestamp, String bodyString) {
        return XhlSignUtil.getSignYHB(appId, apiKey, this.getRelUrl(url), timestamp, bodyString);
    }
}
