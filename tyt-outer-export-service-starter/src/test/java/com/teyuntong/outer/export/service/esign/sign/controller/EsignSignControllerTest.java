package com.teyuntong.outer.export.service.esign.sign.controller;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.esign.enums.sign.OrganizeTemplateTypeEnum;
import com.teyuntong.outer.export.service.client.esign.enums.sign.SealColorEnum;
import com.teyuntong.outer.export.service.client.esign.enums.sign.SignSealTypeEnum;
import com.teyuntong.outer.export.service.client.esign.vo.sign.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class EsignSignControllerTest extends TestBase {

    @Test
    void testCreatePdf() throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("demo.pdf");

        CreatePdfReq req = new CreatePdfReq();
        req.setPdfFileBase64(Base64.getEncoder().encodeToString(IOUtils.toByteArray(classPathResource.getInputStream())));
        req.setPdfFileName("123.pdf");
        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/pdf/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void testAddAccount() throws Exception {
        AddAccountReq req = new AddAccountReq();
        req.setEmail("");//邮箱地址，可空
        req.setMobile("");//用于接收签署验证码的手机号码,可空，可空
        req.setName("*****科技有限公司");//机构名称，不可空
        req.setOrganType(0);// 单位类型，0-普通企业，1-社会团体，2-事业单位，3-民办非企业单位，4-党政及国家机构
        req.setOrganCode("5222******L62");// 组织机构代码号、社会信用代码号或工商注册号
        req.setUserType(0);

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/account/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void testAddTemplateSeal() throws Exception {
        TemplateSealReq req = new TemplateSealReq();
        req.setAccountId("123");
        req.setTemplateType(OrganizeTemplateTypeEnum.DEDICATED);
        req.setColor(SealColorEnum.BLACK);

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/template/seal/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);

    }

    @Test
    void testSendSignMobileCode3rd() throws Exception {
        SendSignMobileCode3rdReq req = new SendSignMobileCode3rdReq();
        req.setAccountId("123");
        req.setMobile("1234");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/mobileCode/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);

    }

    @Test
    void testLocalSafeSignPDF3rd() throws Exception {
        LocalSafeSignPDF3rdReq req = new LocalSafeSignPDF3rdReq();
        req.setAccountId("123");
        req.setSignType(SignSealTypeEnum.KEY);
        req.setMobile("1234");
        req.setCode("1234");

        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq localSafeSignPDF3rdPdfReq =
                new LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPdfReq();
        localSafeSignPDF3rdPdfReq.setFileBase64("123");
        LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPosReq localSafeSignPDF3rdPosReq =
                new LocalSafeSignPDF3rdReq.LocalSafeSignPDF3rdPosReq();

        req.setPdfInfo(localSafeSignPDF3rdPdfReq);
        req.setPosInfo(localSafeSignPDF3rdPosReq);

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/localPdf/saveSign")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);

    }

    @Test
    void testLocalSignPdf() throws Exception {
        LocalSignPdfReq req = new LocalSignPdfReq();
        req.setSignType(SignSealTypeEnum.SINGLE);
        //req.setSealData("SignType.Key");

        LocalSignPdfReq.LocalSafeSignPDF3rdPdfReq localSafeSignPDF3rdPdfReq =
                new LocalSignPdfReq.LocalSafeSignPDF3rdPdfReq();
        localSafeSignPDF3rdPdfReq.setFileBase64("123");
        LocalSignPdfReq.LocalSafeSignPDF3rdPosReq localSafeSignPDF3rdPosReq =
                new LocalSignPdfReq.LocalSafeSignPDF3rdPosReq();
        localSafeSignPDF3rdPosReq.setPosPage("1");

        req.setPdfInfo(localSafeSignPDF3rdPdfReq);
        req.setPosInfo(localSafeSignPDF3rdPosReq);

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/localPdf/sign")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);

    }

    @Test
    void testLocalVerifyPdf() throws Exception {
        LocalVerifyPdfReq req = new LocalVerifyPdfReq();
        ClassPathResource classPathResource = new ClassPathResource("demo.pdf");

        req.setPdfBase64(Base64.getEncoder().encodeToString(IOUtils.toByteArray(classPathResource.getInputStream())));

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/sign/localPdf/verify")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);

    }
}