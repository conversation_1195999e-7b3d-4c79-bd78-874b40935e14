package com.teyuntong.outer.export.service.cticloud;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Collections;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class TaskRpcServiceTest extends TestBase {

    @Test
    void testQueryTask() throws Exception {
        // Arrange
        String name = "testTask";
        Integer type = 1;
        Integer status = 0;
        Integer autoStart = 0;
        Integer autoStop = 0;
        Integer timeType = 1;
        String startTime = "2024-01-01 00:00:00";
        String endTime = "2024-12-31 23:59:59";

        TaskQueryResult taskQueryResult = new TaskQueryResult();
        // Set taskQueryResult fields as needed

        CticloudResp<TaskQueryResult> cticloudResp = new CticloudResp<>();
        cticloudResp.setData(taskQueryResult);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/cticloud/task/query")
                        .param("name", name)
                        .param("type", type.toString())
                        .param("status", status.toString())
                        .param("autoStart", autoStart.toString())
                        .param("autoStop", autoStop.toString())
                        .param("timeType", timeType.toString())
                        .param("startTime", startTime)
                        .param("endTime", endTime))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    void testImportTaskTel() throws Exception {
        ImportTaskTelRequest importTaskTelRequest = new ImportTaskTelRequest();
        importTaskTelRequest.setTaskId("106975");
        importTaskTelRequest.setName("接口api测试用");
        importTaskTelRequest.setTaskTelList(Collections.singletonList(new ImportTaskTelRequest.TaskTelListDTO(
                "13333333333", null, null, null, null, null)));

        ImportTaskTelResponse importTaskTelResponse = new ImportTaskTelResponse();
        // Set importTaskTelResponse fields as needed

        CticloudResp<ImportTaskTelResponse> cticloudResp = new CticloudResp<>();
        cticloudResp.setData(importTaskTelResponse);


        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(importTaskTelRequest);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/cticloud/task/importTaskTel")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    void testAutoCallTask() throws Exception {
        // Arrange
        AutoCallTaskRequest autoCallTaskRequest = new AutoCallTaskRequest();
        autoCallTaskRequest.setTaskName("专车派单AI自动外呼任务-2024-09-02 11:40:49");
        autoCallTaskRequest.setTaskCallValue("特运通提醒您，有北京海淀区到北京海淀区的专车货源等您接单，请尽快接单。");
        autoCallTaskRequest.setCallTelList(Collections.singletonList("18612411401"));
        // Set autoCallTaskRequest fields as needed

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(autoCallTaskRequest);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/cticloud/task/autoCallTask")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    void testAutoDelAutoCallTask() throws Exception {
        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/cticloud/task/autoDeleteAutoCallTask"))
                .andExpect(status().isOk())
                .andReturn();
    }


}