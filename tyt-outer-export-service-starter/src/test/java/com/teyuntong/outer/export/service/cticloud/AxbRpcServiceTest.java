package com.teyuntong.outer.export.service.cticloud;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbBindReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbDeleteReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbUpdateReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.CDRReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.Date;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class AxbRpcServiceTest extends TestBase {

    @Test
    void axbBind() throws Exception {
        AxbBindReq axbBindReq = new AxbBindReq();
        axbBindReq.setTelA("18612411401");
        axbBindReq.setTelB("15295731413");
        axbBindReq.setBizType(1);
        axbBindReq.setBizId(12345L);
        axbBindReq.setExpiration(8000);
        axbBindReq.setExtraField("extra!");
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(axbBindReq);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/axb/bind")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }



    @Test
    void testBindGet() throws Exception {
        String telA = "18612411401";
        String telB = "15295731413";
        int bizType = 1;
        long bizId = 12345L;
        int expiration = 80;
        String extraField = "extra!";

        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/axb/bindGet")
                        .param("telA", telA)
                        .param("telB", telB)
                        .param("bizType", String.valueOf(bizType))
                        .param("bizId", String.valueOf(bizId))
                        .param("expiration", String.valueOf(expiration))
                        .param("extraField", extraField)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);

    }

    @Test
    void testAxbUpdate() throws Exception {
        AxbUpdateReq updateReq = new AxbUpdateReq();
        updateReq.setTelA("18612411401");
//        updateReq.setTelB("15295731413");
        updateReq.setExpiration(80);
        updateReq.setExtraField("extra!");
        updateReq.setId(78L);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(updateReq);

        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/axb/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);

    }

    @Test
    void testGetAxbInfo() throws Exception {
        int bizType = 1;
        long bizId = 12345L;

        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/axb/info")
                        .param("bizType", String.valueOf(bizType))
                        .param("bizId", String.valueOf(bizId))
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);

    }

    @Test
    void testAxbDelete() throws Exception {
        AxbDeleteReq deleteReq = new AxbDeleteReq();
        deleteReq.setBizType(1);
        deleteReq.setBizId(12345L);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(deleteReq);

        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/axb/delete")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);

    }

    @Test
    void testGetCDR() throws Exception {

        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.NOVEMBER, 22, 0, 0, 0);
        Date date1 = calendar.getTime();

        // 设置2024年7月23日的日期
        calendar.set(2024, Calendar.NOVEMBER, 30, 0, 0, 0);
        Date date2 = calendar.getTime();

        CDRReq cdrReq = new CDRReq();
        cdrReq.setStartTime(date1);
        cdrReq.setEndTime(date2);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(cdrReq);

        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/axb/getCDR")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);

    }

}