package com.teyuntong.outer.export.service.service.biz.transport.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LocationTraceDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.service.LocationService;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.*;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJHistoryTrackResult;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJVehicleLocationResult;
import com.teyuntong.outer.export.service.service.biz.zj.service.CarLocationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/05 16:45
 */
@Slf4j
@Disabled
public class CarLoactionTest extends TestBase {

    @Autowired
    private CarLocationService carLocationService;

    @Autowired
    private LocationService locationService;

    @Test
    void carRealTimeLocationTest() {
        String carHeadNo = "陕D99327,沪DR3806,豫RJF662";
        ZJVehicleLocationResult result = carLocationService.getCarRealTimeLocation(carHeadNo, null);
        log.info("result:{}", result);
    }


    @Test
    void carLocusTest() {

        String carHeadNo = "豫RJF662";

        ZJHistoryTrackResult result = carLocationService.getCarLocus(carHeadNo, null, "2024-11-01 00:00:00", "2024-11-03 23:59:59");
        log.info("result:{}", result);

    }


    @Test
    void mbLastLocationTest() {

        List<LastLocationDTO> lastLocationDTOList = new ArrayList<>();
        LastLocationDTO lastLocationDTO = new LastLocationDTO();
        lastLocationDTO.setVehicleNo("沪A8OvXNdi8#");
        lastLocationDTO.setVehicleColor(2);
        lastLocationDTOList.add(lastLocationDTO);
        List<VehicleLocationVO> result = locationService.getVehicleLastLocation(lastLocationDTOList);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * {"chargeChannels":[3],"endTime":1734786822000,"startTime":1734614022000,"userId":965006065496945502,"vehicleNo":"沪A88888"}
     */
    @Test
    void mbLastLocationTest2() {
        LocationTraceDTO locationTraceDTO = new LocationTraceDTO();

        List<Integer> chargeChannels = new ArrayList<>();
        chargeChannels.add(3);
        locationTraceDTO.setVehicleNo("沪A88888");
        locationTraceDTO.setUserId(965006065498912915L); // 965006065496945502   965006065498912915L
        locationTraceDTO.setStartTime(1734614022000L);
        locationTraceDTO.setEndTime(1734786822000L);
        locationTraceDTO.setChargeChannels(chargeChannels);
        LocationTraceVO locationTrace = locationService.getLocationTrace(locationTraceDTO);
        log.info("locationTrace:{}", JSON.toJSONString(locationTrace));
    }
//    userId：965006065499914502
//    startTime: 1733882007647
//    endTime: 1734486807647

    @Test
    void mbLastLocationTest3() {

        List<Long> userIds = new ArrayList<>();
        userIds.add(965006065498912915L);
        // userIds.add(96500606523432L);

        List<AppLastPositionVO> appLastPosition = locationService.getAppLastPosition(userIds);
        if(CollUtil.isNotEmpty(appLastPosition)){
            log.info("locationTrace:{}", JSON.toJSONString(appLastPosition));
        }else {
            log.info("locationTrace:{}", JSON.toJSONString("暂无定位信息"));
        }

    }

    @Test
    void mbLbsAuthTest() {

        // String vehicleNo = "沪A88888";
        String vehicleNo = "豫RJF662";
        List<LbsAuthVO> lbsAuth = locationService.getLbsAuth(vehicleNo);
        log.info("lbsAuth:{}", JSON.toJSONString(lbsAuth));

    }

    @Test
    void mbGetVehicleSinoiovLastLocationTest() {
        List<LastLocationDTO> lastLocationDTOList = new ArrayList<>();
        LastLocationDTO lastLocationDTO = new LastLocationDTO();
        lastLocationDTO.setVehicleNo("冀FU7616");
        lastLocationDTO.setVehicleColor(2);
        lastLocationDTOList.add(lastLocationDTO);

        List<SinoiovVehicleLastLocationVO> vehicleSinoiovLastLocation = locationService.getVehicleSinoiovLastLocation(lastLocationDTOList);
        log.info("vehicleSinoiovLastLocation:{}", JSON.toJSONString(vehicleSinoiovLastLocation));

    }
}
