package com.teyuntong.outer.export.service.client.invoice.hbwj.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.GoodsUnitEnum;
import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ModifyAmountType;
import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.TransportType;
import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ValuationType;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/17 16:50
 */
@Disabled
public class InvoiceWjOpenApiServiceTest extends TestBase {

    @Autowired
    private InvoiceWjOpenApiService invoiceWjOpenApiService;

    // @Autowired
    // private HBWJService hBWJService;


    @Test
    public void applyFreight() throws Exception {
        ApplyFreightRequest applyFreightRequest = new ApplyFreightRequest();

        List<ApplyFreightInfo> applyFreightInfoList = new ArrayList<>();
        ApplyFreightInfo applyFreightInfo = new ApplyFreightInfo();
        applyFreightInfo.setWaybillId(5343L);
        applyFreightInfo.setWaybillCode("******************");
        applyFreightInfo.setFeeType("FREIGHT");
        applyFreightInfo.setAppliedAmount(new BigDecimal(100));
        applyFreightInfo.setPayeePhone("***********");
        applyFreightInfo.setPayeeType("SELF");
        applyFreightInfo.setBankCardNo("6228480018522130071");
        applyFreightInfo.setBankAccountName("候宏伟");
        applyFreightInfo.setIdCardNo("372928198103151010");
        applyFreightInfo.setBankName("中国农业银行股份有限公司北京龙岗路支行");
        applyFreightInfoList.add(applyFreightInfo);

        applyFreightRequest.setApplyFreightInfoList(applyFreightInfoList);
        applyFreightRequest.setBusinessType("ADVANCE_TRANSPORT");

//        String userCode = "YZM_T3mjfl4BMX";
        String userCode = "YZM_T3wVRznSKf";

        WebResult<List<ApplyFreightResp>> listWebResult = invoiceWjOpenApiService.applyFreight(applyFreightRequest, userCode);
        System.out.println(JSON.toJSON(listWebResult));



    }


    @Test
    public void createDriver() throws Exception {

        HbwjDriverReport report = new HbwjDriverReport();
        report.setDriverName("杨坤");
        report.setIdCardNo("370704197808152015");
        report.setPhone("***********");
        report.setDrivingNo("370704197808152015");
        report.setDriverType("A2C1");
        //身份证正面-国徽
        report.setIdCardFrontImg("http://devimage.teyuntong.net/dispatch/APP/2024-05-11/0100200043095129befe14f9d4dcd9cfc6ca8daeb1e.jpg");

        //身份证反面-数字
        report.setIdCardBackImg("http://devimage.teyuntong.net/dispatch/APP/2024-05-11/0100200043088043ee928ffc9ecbdaef7bd5e06ec6b.jpg");
        //身份证有效期开始
        report.setIdCardPeriodStart("2007-07-06 08:00:00");
        //身份证有效期结束
        report.setIdCardPeriodEnd("2027-07-06 08:00:00");
        //驾驶证图片
        report.setDrivingImg("http://devimage.teyuntong.net/dispatch/APP/2024-05-11/01002000430be735441be5088a096ba82c36097897c.jpg");
        //驾驶证有效期开始\结束
        report.setDrivingPeriodStart("1996-05-02 08:00:00");
        report.setDrivingPeriodEnd("2026-05-04 08:00:00");
        //驾驶证发证机关
        report.setIssuingOrganization("山东省潍坊市公安局交通警察支队");
        //从业资格证图片
        report.setQualificationCardImg("http://devimage.teyuntong.net/dispatch/APP/2024-05-11/01002000430b22109b650850f873b53be42be7f3d57.jpg");
        //从业资格证有效期
        report.setQualificationCardPeriodStart("2020-05-27 08:00:00");
        //从业资格证结束日期
        report.setQualificationCardPeriodEnd("2026-05-26 08:00:00");
        //从业资格证号
        report.setQualificationCardNo("370704197808152015");
        //从业资格证姓名
        report.setQualificationName("杨坤");
        //驾驶证姓名
        report.setDrivingName("杨坤");
        DriverDetailResponse repo = invoiceWjOpenApiService.createDriver(report);


    }


    @Test
    public void createCar() throws Exception {
        HbwjCarReport report = new HbwjCarReport();

        DriverDetailResponse repo = invoiceWjOpenApiService.createCar(report);


    }

    @Test
    public void uploadWeightReceipts()   throws Exception{
        UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto = new UploadWeightReceiptsReqDto();

        uploadWeightReceiptsReqDto.setWaybillCode("******************");
        uploadWeightReceiptsReqDto.setAttachType("DELIVERY_RECEIPT");

        List<UploadFileInfoDto> uploadFileInfoDtoList = new ArrayList<>();
//        FileOutputStream fos = new FileOutputStream("D:\\temp\\a.jpg", true);
//        byte[] bytes = readFileToByteArray("D:\\temp\\a.jpg");
//        byte[] bytes1 = new byte[fos.get];

        UploadFileInfoDto uploadFileInfoDto1 = new UploadFileInfoDto();
//        uploadFileInfoDto1.setFileUrl("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff83151.jpeg");
        uploadFileInfoDto1.setFileName("image1.jpg");
        uploadFileInfoDto1.setFileUrl("http://tyt-images.oss-cn-beijing.aliyuncs.com/dev/tytModel/driverOrderImages/2024/07/24/1002000800_1721802209000_36028.png");
//        uploadFileInfoDto1.setFileDataByte(bytes);
//        UploadFileInfoDto uploadFileInfoDto2 = new UploadFileInfoDto();
//        uploadFileInfoDto2.setFileUrl("https://img-blog.csdnimg.cn/img_convert/a1050ef40c723475202aad085c350bb7.png");
//        uploadFileInfoDto2.setFileName("image1");

        uploadFileInfoDtoList.add(uploadFileInfoDto1);
//        uploadFileInfoDtoList.add(uploadFileInfoDto2);

        uploadWeightReceiptsReqDto.setUploadFileInfoDtoList(uploadFileInfoDtoList);

        String userCode = "YZM_T3mjfl4BMX";
        String o = invoiceWjOpenApiService.uploadWeightReceipts(uploadWeightReceiptsReqDto,userCode);
        System.out.println(JSON.toJSON(o));
    }

    @Test
    public void getFundVerificationCode() throws Exception {
        String userCode = "YZM_T3mjfl4BMX";

        String s = invoiceWjOpenApiService.getFundVerificationCode("***********",userCode);
        System.out.println(JSON.toJSON(s));
    }


    @Test
    public void addWaybillThree() throws Exception {

        CreateWaybillThreeRequest request = new CreateWaybillThreeRequest();
        // 用户会员代码
        request.setUserCode("YZM_T3wVRznSKf");
        // 设置三方配置
        CreateWaybillThreeRequest.ThreeConfig threeConfig = new CreateWaybillThreeRequest.ThreeConfig();
        threeConfig.setCreateWaybillStatus(2); // 设置建单后运单状态为待起运
        threeConfig.setIsSendWaybillContract(true); // 设置发送运输合同为true
        request.setThreeConfig(threeConfig);

        // 设置运单信息
        CreateWaybillThreeRequest.WaybillDto waybillDto = new CreateWaybillThreeRequest.WaybillDto();
        waybillDto.setBusinessSource("WAYBILL"); // 设置业务来源为直接派单
        waybillDto.setWaybillType(1); // 设置运单类型为直接建单
        waybillDto.setUpstreamCode(System.currentTimeMillis() + ""); //三方单号
        waybillDto.setThreeCreateTime("2024-07-22 14:56:20");
        waybillDto.setTransportType("CAR"); // 设置运输方式为汽运
        waybillDto.setValuationType("CAR"); // 设置计价方式为单车/船
        request.setWaybillDto(waybillDto);

        // 设置运力信息
        CreateWaybillThreeRequest.CarDriverDto carDriverDto = new CreateWaybillThreeRequest.CarDriverDto();
        carDriverDto.setDriverName("杨坤"); // 设置驾驶人姓名
        carDriverDto.setDriverPhone("***********"); // 设置驾驶人手机号
        carDriverDto.setIdCardNo("370704197808152015");

        String travelNum = "京FT7063";
        carDriverDto.setTravelNum(travelNum); // 设置车船号
        request.setCarDriverDto(carDriverDto);


        // 设置路线信息
        CreateWaybillThreeRequest.RouteInfoDto routeInfoDto = new CreateWaybillThreeRequest.RouteInfoDto();
        routeInfoDto.setSendProvince("北京市"); // 设置发货省
        routeInfoDto.setSendCity("北京市"); // 设置发货市
        routeInfoDto.setSendArea("海淀区"); // 设置发货区
        routeInfoDto.setSendAddress("知春路6号锦秋国际大厦一层A03Costa Coffee COSTA COFFEE(锦秋国际大厦店)"); // 设置发货地址
        routeInfoDto.setReceiveProvince("湖南省"); // 设置收货省
        routeInfoDto.setReceiveCity("长沙市"); // 设置收货市
        routeInfoDto.setReceiveArea("岳麓区"); // 设置收货区
        routeInfoDto.setReceiveAddress("登高路1号(湖南大学地铁站2号口步行310米)"); // 设置收货地址
        request.setRouteInfoDto(routeInfoDto);

        // 设置货物信息
        CreateWaybillThreeRequest.GoodsInfoDto goodsInfoDto = new CreateWaybillThreeRequest.GoodsInfoDto();
        goodsInfoDto.setGoodsCategory("1200"); // 设置货物品类为 1200机械、设备、电器
        goodsInfoDto.setGoodsUnit("TON"); // 设置货物计量单位为吨
        goodsInfoDto.setGoodsWeight(new BigDecimal("19.8")); // 设置货物重量
        // goodsInfoDto.setUnitAmount(); // 设置运输单价
        goodsInfoDto.setAmount(new BigDecimal("5465")); // 设置运输金额
        goodsInfoDto.setGoodsName("12起重机"); // 设置货物品名
        request.setGoodsInfoDto(goodsInfoDto);

        // 设置项目信息
        CreateWaybillThreeRequest.ProjectStoreDto projectStoreDto = new CreateWaybillThreeRequest.ProjectStoreDto();
        projectStoreDto.setIsAddProject(false); // 设置新增项目为true
        projectStoreDto.setProjectName("HT2024072200000001"); // 设置项目名称 (用户中心获取)
        // projectStoreDto.setSettlementName("测试结算公司"); // 设置结算主体名称
        // projectStoreDto.setCreateAmountUser("SHIPPER"); // 设置建单费支付人为货主支付
        // projectStoreDto.setDriverFreightUser("CARRIER_BROKER"); // 设置司机运费支付人为承运商/经纪人支付
        // projectStoreDto.setCredentialRequest(3); // 设置凭证要求为电子凭证
        // projectStoreDto.setGoodsSourceType(1); // 设置货源类型为平台货源
        request.setProjectStoreDto(projectStoreDto);

        // // 设置结算信息
        // CreateWaybillThreeRequest.UpWaybillSettlementDto upWaybillSettlementDto = new CreateWaybillThreeRequest.UpWaybillSettlementDto();
        // upWaybillSettlementDto.setValuationType("QUANTITY");
        // upWaybillSettlementDto.setReceivableUnitAmount();
        // upWaybillSettlementDto.setReceivableAmount();
        // request.setUpWaybillSettlementDto(upWaybillSettlementDto);

        System.out.println("开票-调用三方服务商创建运单接口请求参数：{}" + request);
        WebResult<CreateWaybillThreeResponse> result = invoiceWjOpenApiService.addWaybillThree(request);
        System.out.println("开票-调用三方服务商创建运单接口返回参数：{}" + result);
    }


    // @Test
    // void getGaoDeDistrictVoList() throws Exception {
    //     Object gaoDeDistrictVoList = hBWJService.getGaoDeDistrictVoList();
    //     System.out.println(gaoDeDistrictVoList);
    // }

    @Test
    public void setPayPwd() throws Exception{
        SavePayPwdRequest request = new SavePayPwdRequest();
        request.setUserCode("YZM_T3mjfl4BMX");
        request.setNewPayPwd("123211");
        request.setConfirmPayPwd("123211");
        WebResult<Object> result = invoiceWjOpenApiService.setPayPwd(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void changePayPwd() throws Exception{
        ChangePayPwdRequest request = new ChangePayPwdRequest();
        request.setUserCode("YZM_T3mjfl4BMX");
        request.setOldPayPwd("123211");
        request.setNewPayPwd("123411");
        request.setConfirmPayPwd("123411");
        WebResult<Object> result = invoiceWjOpenApiService.changePayPwd(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void changePhoneCaptchaSendForAccount() throws Exception{
        ChangePhoneCaptchaSendRequest request = new ChangePhoneCaptchaSendRequest();
        request.setPhone("***********");
        request.setUserCode("YZM_wPPkbB7ltq");
        WebResult<Object> result = invoiceWjOpenApiService.changePhoneCaptchaSendForAccount(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void changePhoneCaptchaCheck() throws Exception{
        ChangePhoneCaptchaCheckRequest request = new ChangePhoneCaptchaCheckRequest();
        request.setPhone("***********");
        request.setCaptcha("108042");
        request.setUserCode("YZM_wPPkbB7ltq");
        WebResult<ChangePhoneCaptchaCheckResponse> result = invoiceWjOpenApiService.changePhoneCaptchaCheck(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void changePhone() throws Exception{
        ChangePhoneRequest request = new ChangePhoneRequest();
        request.setTick("ffffc15e20674bd8bf3b011c01cae61d");
        request.setNewPhone("***********");
        request.setCaptcha("108042");
        request.setUserCode("YZM_wPPkbB7ltq");
        WebResult<Object> result = invoiceWjOpenApiService.changePhone(request);
        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void selectCustomerRechargeWalletList() throws Exception{
        QueryWalletRequest request = new QueryWalletRequest();
        request.setSettlementNo("CPF000003");
        request.setUserCode("YZM_T3mjfl4BMX");
        QueryWalletResponse response = invoiceWjOpenApiService.selectCustomerRechargeWalletList(request);
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void pickGoods()   throws Exception{
        PickGoodsThreeRequest request = new PickGoodsThreeRequest();

        request.setWaybillCode("******************");

        List<String> loadImgs=new ArrayList<>();
        loadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff83151.jpeg");
        loadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff8334.jpeg");
        request.setRealityLoadImg(loadImgs);

        request.setRealityLoadWeight(new BigDecimal(1));
        request.setRealityLoadTime("2024-07-25 16:48:12");
        request.setUserCode("YZM_T3wVRznSKf");
        WebResult<Object> objectWebResult = invoiceWjOpenApiService.pickGoods(request);
        System.out.println(JSON.toJSONString(objectWebResult));
    }

    @Test
    public void arriveGoods() throws Exception{
        ArriveGoodsThreeRequest request = new ArriveGoodsThreeRequest();

        request.setWaybillCode("******************");

        List<String> loadImgs=new ArrayList<>();
        loadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff83151.jpeg");
        loadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff8334.jpeg");
        request.setRealityLoadImg(loadImgs);

        List<String> unloadImgs=new ArrayList<>();
        unloadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff831513.jpeg");
        unloadImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff83345.jpeg");
        request.setUnRealityLoadImg(unloadImgs);

        List<String> returnDepositImgs=new ArrayList<>();
        returnDepositImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff8315133.jpeg");
        returnDepositImgs.add("https://i-blog.csdnimg.cn/direct/43104765d4ba476492fedc7d4ff833454.jpeg");
        request.setReturnDepositImg(returnDepositImgs);

        request.setRealityLoadWeight(new BigDecimal(1));
        request.setRealityUnloadWeight(new BigDecimal(1));
        request.setRealityUnloadTime("2024-07-25 18:12:12");
        request.setUserCode("YZM_T3wVRznSKf");
        WebResult<Object> objectWebResult = invoiceWjOpenApiService.arriveGoods(request);
        System.out.println(JSON.toJSONString(objectWebResult));
    }

    @Test
    public void batchDoPay() throws Exception {

        BatchDoPayRequest batchDoPayRequest = new BatchDoPayRequest();
        batchDoPayRequest.setPassword("113355");
        List<String> paymentNos = new ArrayList<>();
        paymentNos.add("ZFD2024072500000005");

        batchDoPayRequest.setPaymentNos(paymentNos);
        batchDoPayRequest.setVsgCode("1234");
        batchDoPayRequest.setPhone("***********");

        String userCode = "YZM_T3wVRznSKf";
        WebResult<List<BatchDoPayResponse>> listWebResult = invoiceWjOpenApiService.batchDoPay(batchDoPayRequest, userCode);
        System.out.println(JSON.toJSONString(listWebResult));
    }



    public static byte[] readFileToByteArray(String filePath) {
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        ByteArrayOutputStream bos = null;
        try {
            fis = new FileInputStream(filePath);
            bis = new BufferedInputStream(fis);
            bos = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int bytesRead;
            // 读取文件到缓冲区，再将缓冲区的内容写入到ByteArrayOutputStream中
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }

            // 将ByteArrayOutputStream的内容转换为byte[]
            byte[] fileData = bos.toByteArray();
            return fileData;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            // 关闭资源
            try {
                if (bis != null) bis.close();
                if (fis != null) fis.close();
                if (bos != null) bos.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    @Test
    public void createProtocol() throws Exception {
        CreateProtocolRequest request = new CreateProtocolRequest();
        request.setYyzContractTypeEnum("SINGLE_TRANSPORT_NOT_PAYEE");
        request.setWaybillCode("YD2024072400000002");
        request.setUserCode("YZM_T3mjfl4BMX");
        WebResult<CreateProtocolResponse> protocol = invoiceWjOpenApiService.createProtocol(request);
        System.out.println(JSON.toJSON(protocol));
    }

    @Test
    public void editWaybillThree() throws Exception {



        EditWaybillThreeRequest editWaybillThreeRequest = new EditWaybillThreeRequest();
        GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
        goodsInfoDto.setGoodsUnit(GoodsUnitEnum.TON);
        goodsInfoDto.setUnitAmount(new BigDecimal(0));
        goodsInfoDto.setAmount(new BigDecimal("5676"));
        goodsInfoDto.setGoodsName("20起重机");
        goodsInfoDto.setGoodsWeight(new BigDecimal("22.9"));

        RouteInfoDto routeInfoDto = new RouteInfoDto();
        routeInfoDto.setSendProvince("北京市"); // 设置发货省
        routeInfoDto.setSendCity("北京市"); // 设置发货市
        routeInfoDto.setSendArea("海淀区"); // 设置发货区
        routeInfoDto.setSendAddress("知春路6号锦秋国际大厦一层A03Costa Coffee COSTA COFFEE(锦秋国际大厦店)"); // 设置发货地址
        routeInfoDto.setReceiveProvince("湖南省"); // 设置收货省
        routeInfoDto.setReceiveCity("长沙市"); // 设置收货市
        routeInfoDto.setReceiveArea("岳麓区"); // 设置收货区
        routeInfoDto.setReceiveAddress("登高路1号(湖南大学地铁站2号口步行310米)"); // 设置收货地址
        /*routeInfoDto.setSendLat(new BigDecimal(transportProtocolDO.getStartLatitude()));
        routeInfoDto.setSendLng(new BigDecimal(transportProtocolDO.getStartLongitude()));
        routeInfoDto.setReceiveLng(new BigDecimal(transportProtocolDO.getDestLongitude()));
        routeInfoDto.setReceiveLat(new BigDecimal(transportProtocolDO.getDestLatitude()));*/

        WaybillDto waybillDto = new WaybillDto();
        waybillDto.setWaybillCode("******************");
        waybillDto.setValuationType(ValuationType.CAR);
        waybillDto.setTransportType(TransportType.CAR);
        waybillDto.setUpstreamCode("12733");
        ThreeConfig threeConfig = new ThreeConfig();
        threeConfig.setIsSendWaybillContract(false);
        editWaybillThreeRequest.setThreeConfig(threeConfig);
        editWaybillThreeRequest.setWaybillDto(waybillDto);
        editWaybillThreeRequest.setGoodsInfoDto(goodsInfoDto);
        editWaybillThreeRequest.setRouteInfoDto(routeInfoDto);
        editWaybillThreeRequest.setUserCode("YZM_T3mjfl4BMX");
        WebResult<EditWaybillThreeResponse> editWaybillThreeResponse = invoiceWjOpenApiService.editWaybillThree(editWaybillThreeRequest);
        System.out.println(editWaybillThreeResponse);
    }

    @Test
    public void updateAmount() throws Exception {
        AmountUpdateRequest amountUpdateRequest = new AmountUpdateRequest();
        UpdateAmountDto updateAmountDto = new UpdateAmountDto();
        AmountUpdateListDownDto amountUpdateListDownDto = new AmountUpdateListDownDto();
        //三方运单号
        amountUpdateRequest.setWayBillCode("******************");
        updateAmountDto.setModifyAmountType(ModifyAmountType.REPAIR);
        updateAmountDto.setModifyAmount((int)(190000/100));
        amountUpdateRequest.getAmountUpdateList().add(updateAmountDto);
        amountUpdateRequest.setUserCode("YZM_T3mjfl4BMX");
        boolean b = invoiceWjOpenApiService.amountUpdate(amountUpdateRequest);

    }

    @Test
    public void invoiceCreate() throws Exception {
        //{"bankAccount":"1","bankName":"1","email":"1","registerAddress":"1","registerTelephone":"1","taxpayerIdentificationNumber":"91150900MA0N477W5Y"}
//        InvoiceRequest invoiceRequest = new InvoiceRequest();
//        invoiceRequest.setTaxpayerIdentificationNumber("91620303MADD6H6R09");
//        invoiceRequest.setRegisterAddress("甘肃省金昌市经济技术开发区新华东路68号2-16");
//        invoiceRequest.setRegisterTelephone("***********");
//        invoiceRequest.setBankName("342401197309281614");
//        invoiceRequest.setBankAccount("招商银行");
//        invoiceRequest.setEmail("<EMAIL>");
//        WebResult<Long> result = invoiceWjOpenApiService.invoiceCreate(invoiceRequest, "YZM_T3mjfl4BMX");


        InvoiceRequest invoiceRequest = new InvoiceRequest();
        invoiceRequest.setTaxpayerIdentificationNumber("91150900MA0N477W5Y");
        invoiceRequest.setRegisterAddress("1");
        invoiceRequest.setRegisterTelephone("1");
        invoiceRequest.setBankName("1");
        invoiceRequest.setBankAccount("1");
        invoiceRequest.setEmail("1");
        WebResult<Long> result = invoiceWjOpenApiService.invoiceCreate(invoiceRequest, "YZM_MCEkFqxseh");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void invoiceUpdate() throws Exception {
        InvoiceRequest invoiceRequest = new InvoiceRequest();
        invoiceRequest.setId(508L);
        invoiceRequest.setTaxpayerIdentificationNumber("91620303MADD6H6R09");
        invoiceRequest.setRegisterAddress("许昌市胖东来商贸集团有限公司");
        invoiceRequest.setRegisterTelephone("***********");
        invoiceRequest.setBankName("342401197309281614");
        invoiceRequest.setBankAccount("招商银行");
        invoiceRequest.setEmail("<EMAIL>");
        invoiceRequest.setSendAddress("北京市海淀区知春路6号(锦秋国际大厦)06层B01室 ");
        invoiceRequest.setReceiveName("孔保平");
        invoiceRequest.setReceivePhone("***********");
        WebResult<Long> result = invoiceWjOpenApiService.invoiceUpdate(invoiceRequest, "YZM_nU9i8Sz44C");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void doInvoiceCreate() throws Exception {
        InvoiceCreateRequest invoiceCreateRequest = new InvoiceCreateRequest();
        invoiceCreateRequest.setInvoiceMaintenanceId(508L);
        invoiceCreateRequest.setSettlementNo("CPF000002");
        invoiceCreateRequest.setInvoiceKind("SHIPPING_FEE");
        invoiceCreateRequest.setInvoiceType("SPECIAL_INVOICES");
        invoiceCreateRequest.setWaybillCodeList(Arrays.asList("******************"));
        invoiceCreateRequest.setInvoiceRemark("备注");
        invoiceCreateRequest.setBuyerName("哈哈哈");
        String userCode = "YZM_nU9i8Sz44C";
        WebResult<Object> objectWebResult = invoiceWjOpenApiService.doInvoiceCreate(invoiceCreateRequest, userCode);
        System.out.println(JSON.toJSONString(JSON.toJSONString(objectWebResult)));
    }


    @Test
    public void queryInvoiceCallBackVo() throws Exception {
         String invoiceNumber = "FP2024081500000001";
        String userCode = "YZM_nU9i8Sz44C";

        InvoiceInfoRequest invoiceInfoRequest = new InvoiceInfoRequest();
        invoiceInfoRequest.setInvoiceNumber(invoiceNumber);
        WebResult<InvoiceResponse> invoiceResponseWebResult = invoiceWjOpenApiService.queryInvoiceCallBackVo(invoiceInfoRequest, userCode);
        System.out.println(JSON.toJSONString(invoiceResponseWebResult));

    }


    @Test
    public void getPaymentDetailByNo() throws Exception {
        String userCode = "YZM_nU9i8Sz44C";
        String paymentNo = "ZFD2024112600000010";
        WebResult<PaymentOrderResp> invoiceResponseWebResult = invoiceWjOpenApiService.getPaymentDetailByNo(paymentNo, userCode);
        System.out.println(JSON.toJSONString(invoiceResponseWebResult));

    }
}
