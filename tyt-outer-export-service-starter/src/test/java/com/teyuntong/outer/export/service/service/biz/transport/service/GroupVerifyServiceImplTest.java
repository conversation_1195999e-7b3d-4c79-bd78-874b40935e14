package com.teyuntong.outer.export.service.service.biz.transport.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportQCVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.dto.RoadTransportVerifyDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupVerifyService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQCVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportVerifyResultVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VerifyResultVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024-4-26 16:30:20
 */
@Slf4j
@Disabled
class GroupVerifyServiceImplTest extends TestBase {

    @Autowired
    GroupVerifyService groupVerifyService;

    @Test
    void roadTransportVerify() {
        RoadTransportVerifyDTO dto = RoadTransportVerifyDTO.builder().vehicleNo("皖K69883").vehicleVIN("LRDS6PEB9ET021242").build();
        RoadTransportVerifyResultVO result = groupVerifyService.roadTransportVerify(dto);
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    void roadTransportQCVerify() {
        RoadTransportQCVerifyDTO dto = RoadTransportQCVerifyDTO.builder().idCard("341225199904121218").certificateCode("RY3412002022004496").build();
        RoadTransportQCVerifyResultVO result = groupVerifyService.roadTransportQCVerify(dto);
        log.info("result:{}", JSON.toJSONString(result));
    }
}