package com.teyuntong.outer.export.service.gaode;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.corporate.service.MobileRpcService;
import com.teyuntong.outer.export.service.client.gaode.service.GaoDeMapRpcService;
import com.teyuntong.outer.export.service.client.gaode.vo.GaoDeRegeoReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

@Slf4j
@Disabled
class GaoDeMapRpcServiceTest extends TestBase {

    @Autowired
    GaoDeMapRpcService gaoDeMapRpcService;

    @Test
    void regeoAddress() throws Exception {
        GaoDeRegeoReq req = new GaoDeRegeoReq();
        req.setLongitude("116.679099");
        req.setLatitude("39.849152");

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/gaode/regeoAddress")
                        .param("longitude", req.getLongitude())
                        .param("latitude", req.getLatitude())
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

}