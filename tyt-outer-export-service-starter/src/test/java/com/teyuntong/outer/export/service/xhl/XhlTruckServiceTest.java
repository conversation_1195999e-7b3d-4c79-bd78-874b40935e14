package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDriverEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlDriverService;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlTruckService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlTruckServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlTruckService xhlTruckService;

    /**
     * 新增车辆
     *
     * @throws Exception
     */
    @Test
    void testSaveTruck() throws Exception {
        XhlTruckEntity truckEntity = new XhlTruckEntity();
        truckEntity.setPlateNumber("京A12346"); // 必填
        truckEntity.setTruckType("重型货车"); // 必填
        truckEntity.setLength("6000"); // 必填
        truckEntity.setWidth("2500"); // 必填
        truckEntity.setHeight("3000"); // 必填
        truckEntity.setVehicleLicenseUrl("https://example.com/vehicle_license.jpg"); // 必填
        truckEntity.setVehicleLicenseBackUrl("https://example.com/vehicle_license_back.jpg"); // 必填
        truckEntity.setVehicleLicenseCheckUrl("https://example.com/vehicle_license_check.jpg"); // 必填
        truckEntity.setRegDate("2020-01-01"); // 必填
        truckEntity.setScrapDate("2030-01-01"); // 必填
        truckEntity.setIssueDate("2020-01-01"); // 必填
        truckEntity.setOptCertNo("**********"); // 必填
        truckEntity.setOptCertNoUrl("https://example.com/opt_cert.jpg"); // 必填
        truckEntity.setOptCertExpireTime("2025-12-31"); // 必填
        truckEntity.setPlateColor("1"); // 必填
        truckEntity.setOwnerName("李四"); // 必填
        truckEntity.setIsPersonal("2"); // 必填
        truckEntity.setEnergyType("B"); // 必填
        truckEntity.setTotalWeight("40000"); // 必填
        truckEntity.setCurbWeight("12000"); // 必填
        truckEntity.setApproveWeight("37000"); // 必填
        truckEntity.setTractionWeight("0"); // 必填
        truckEntity.setUseCharacter("货运"); // 必填
        truckEntity.setVin("1HGCM82633A004352"); // 必填
        truckEntity.setIssuingOrganizations("北京市公安局交通管理局"); // 必填
        truckEntity.setEngineNo("**********1234567"); // 必填
        truckEntity.setBusinessCertificate("91440700090124276R"); // 必填
        truckEntity.setBusinessCertificateExpireTime("2025-12-31"); // 必填
        truckEntity.setCreateTime("2023-10-01 12:00:00"); // 必填
        XhlDataResult<Object> result = xhlTruckService.saveTruck(truckEntity);
        log.info("新增车辆-返回结果，truckEntity={}，响应参数：{} ", truckEntity, result);
    }

    @Test
    void testSaveTruckMock() throws Exception {
        XhlTruckEntity truckEntity = new XhlTruckEntity();
        truckEntity.setPlateNumber("京A12345"); // 必填
        truckEntity.setTruckType("重型货车"); // 必填
        truckEntity.setLength("6000"); // 必填
        truckEntity.setWidth("2500"); // 必填
        truckEntity.setHeight("3000"); // 必填
        truckEntity.setVehicleLicenseUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        truckEntity.setVehicleLicenseBackUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        truckEntity.setVehicleLicenseCheckUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        truckEntity.setRegDate("2020-01-01"); // 必填
        truckEntity.setScrapDate("2030-01-01"); // 必填
        truckEntity.setIssueDate("2020-01-01"); // 必填
        truckEntity.setOptCertNo("**********"); // 必填
        truckEntity.setOptCertNoUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        truckEntity.setOptCertExpireTime("2025-12-31"); // 必填
        truckEntity.setPlateColor("1"); // 必填
        truckEntity.setOwnerName("李四"); // 必填
        truckEntity.setIsPersonal("2"); // 必填
        truckEntity.setEnergyType("B"); // 必填
        truckEntity.setTotalWeight("40000"); // 必填
        truckEntity.setCurbWeight("12000"); // 必填
        truckEntity.setApproveWeight("37000"); // 必填
        truckEntity.setTractionWeight("0"); // 必填
        truckEntity.setUseCharacter("货运"); // 必填
        truckEntity.setVin("1HGCM82633A004352"); // 必填
        truckEntity.setIssuingOrganizations("北京市公安局交通管理局"); // 必填
        truckEntity.setEngineNo("**********1234567"); // 必填
        truckEntity.setBusinessCertificate("91440700090124276R"); // 必填
        truckEntity.setBusinessCertificateExpireTime("2025-12-31"); // 必填
        truckEntity.setCreateTime("2023-10-01 12:00:00"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(truckEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/truck/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 查询车辆
     *
     * @throws Exception
     */
    @Test
    void testQueryTruck() throws Exception {
        xhlTruckService.queryTruck("京A12345");
    }

    @Test
    void testQueryTruckMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/truck/query")
                        .param("plateNumber", "京A12345")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }



    /**
     * 修改车辆
     *
     * @throws Exception
     */
    @Test
    void testUpdateTruck() throws Exception {
        XhlTruckEntity truckEntity = new XhlTruckEntity();
        truckEntity.setPlateNumber("京A12345");
        truckEntity.setTruckType("重型货车");
        xhlTruckService.updateTruck(truckEntity);
    }

    @Test
    void testUpdateTruckMock() throws Exception {
        XhlTruckEntity truckEntity = new XhlTruckEntity();
        truckEntity.setPlateNumber("京A12345");
        truckEntity.setTruckType("重型货车");
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(truckEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/truck/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }



}