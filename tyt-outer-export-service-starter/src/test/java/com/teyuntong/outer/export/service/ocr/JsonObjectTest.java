package com.teyuntong.outer.export.service.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.RoadTransportVo;
import com.teyuntong.outer.export.service.service.biz.ocr.service.BaiduOcrService;
import com.teyuntong.outer.export.service.service.biz.ocr.service.impl.BaiduOcrServiceImpl;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/4/25 18:24
 */
@Disabled
public class JsonObjectTest extends TestBase {

    @Test
    void idCardFrontOcr() throws Exception {

        BaiduOcrService baiduOcrService = new BaiduOcrServiceImpl();

        String path = "D:/source-files/test/card_img/idcard.json";

        File jsonFile = new File(path);

        String jsonText = FileUtils.readFileToString(jsonFile, StandardCharsets.UTF_8);

        JSONObject jsonObject = JSON.parseObject(jsonText);

        String name = baiduOcrService.getJsonString(jsonObject, "words_result", "姓名", "words");

        String name1 = baiduOcrService.getJsonString(jsonObject, "words_result", "姓名", "age");

        String name2 = baiduOcrService.getJsonString(jsonObject, "words_result", "姓名", "location");

        System.out.println("finished ... ");
    }


    @Test
    void jsonTest(){

        RoadTransportVo idCardBackVo = new RoadTransportVo();

        String jsonString = JSON.toJSONString(idCardBackVo, SerializerFeature.WriteMapNullValue);

        System.out.println(jsonString);

    }


}