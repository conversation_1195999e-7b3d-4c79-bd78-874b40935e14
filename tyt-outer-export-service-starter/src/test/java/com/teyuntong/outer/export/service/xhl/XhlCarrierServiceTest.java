package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbBindReq;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlCarrierEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlCarrierService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlCarrierServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlCarrierService xhlService;

    @Test
    void testApi4Map() throws Exception {
        Map<String, Object> params1 = new HashMap<String, Object>();

        params1.put("appId", "800007");
        params1.put("companyName", "XXXXXX");


        params1.put("areaCode", "130500");
        params1.put("businessCertificate", "130525304937");
        params1.put("businessCertificateExpireTime", "2024-04-28");
        params1.put("energyType", "B");
        params1.put("fullLoadWeight", "40000");
        params1.put("height", "3570");
        params1.put("isOnline", "2");
        params1.put("isPersonal", "2");
        params1.put("length", "6950");
        params1.put("loadWeight", "37000");
        params1.put("optCertExpireTime", "2024-04-28");
        params1.put("optCertNo", "***********");


        /**
         * appId 800007
         * appSecret 9dfa67133039ab0dcf4d8f6f0d743ca1
         */
        String rst2 = ApiClientHelper.build(xhlProperties.getAppId(), xhlProperties.getApiKey()).post(xhlProperties.getApiUrl() + "/otms/external/api/tyt/company/save", params1);


        log.info("Request success: {}", rst2);

//        // 创建 Retrofit 实例
//        XhlInterfaceClient apiService = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
//
//        // 构建请求体
//        String bodyString = JSON.toJSONString(params1);
//        RequestBody requestBody = RequestBody.create(bodyString, MediaType.parse("application/json"));
//
//        // 执行请求
//        Call<String> call = apiService.saveCompany(requestBody);
//        Response<String> response = call.execute();
//
//        if (response.isSuccessful()) {
//            String rst = response.body();
//            log.info("Request success: {}", rst);
//        } else {
//            log.error("Request failed: {}", response.errorBody().string());
//        }
    }


    @Test
    void testApi4Map2() throws Exception {
        Map<String, Object> params1 = new HashMap<String, Object>();
        params1.put("appId", "800007");
        params1.put("companyName", "特运通科技");
        String rst2 = ApiClientHelper.build(xhlProperties.getAppId(), xhlProperties.getApiKey()).get(xhlProperties.getApiUrl() + "/otms/external/api/tyt/company/query", params1);
        log.info("Request success: {}", rst2);
    }


    /**
     * 新增托运人
     *
     * @throws Exception
     */
    @Test
    void testSaveCompany() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setAppId("800007");
        carrier.setCompanyName("特运通科技2");
        carrier.setPerson("法人姓名");
        carrier.setPersonPhone("***********");
        carrier.setIdCardNo("230822198800112312");
        carrier.setUnifiedSocialCreditCode("91440700090124276R");
        carrier.setAddress("公司地址");
        carrier.setAreaCode("130500");
        carrier.setBusinessCertificatePhotoUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setIdCardNoFrontUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setIdCardNoBackUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setPermitPhotoUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setRate("0.05");
        carrier.setRateType("1");
        carrier.setEffectiveDate("2024-01-01");
        carrier.setExpiryDate("2024-12-31");
        carrier.setKpCustomerPhone("开票信息电话");
        carrier.setKpCustomerAddress("开票信息地址");
        carrier.setKpBankName("开票信息开户行");
        carrier.setKpBankAccountNo("开票信息银行账号");
        carrier.setCreateTime("2023-10-01 12:00:00");

        xhlService.saveCompany(carrier);

//        // 创建 Retrofit 实例
//        XhlInterfaceClient apiService = xhlRetrofitSupplier.getRetrofit().create(XhlInterfaceClient.class);
//
//        // 执行请求
//        Call<XhlDataResult<String>> call = apiService.saveCompany(carrier);
//        Response<XhlDataResult<String>> response = call.execute();
//
//        if (response.isSuccessful()) {
//            XhlDataResult<String> rst = response.body();
//            log.info("Request success: {}", rst);
//        } else {
//            log.error("Request failed: {}", response.errorBody().string());
//        }
    }


    @Test
    void testSaveCompanyMock() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setAppId("800007");
        carrier.setCompanyName("特运通科技2");
        carrier.setPerson("法人姓名");
        carrier.setPersonPhone("***********");
        carrier.setIdCardNo("230822198800112312");
        carrier.setUnifiedSocialCreditCode("91440700090124276R");
        carrier.setAddress("公司地址");
        carrier.setAreaCode("130500");
        carrier.setBusinessCertificatePhotoUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setIdCardNoFrontUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setIdCardNoBackUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setPermitPhotoUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335");
        carrier.setRate("0.05");
        carrier.setRateType("1");
        carrier.setEffectiveDate("2024-01-01");
        carrier.setExpiryDate("2024-12-31");
        carrier.setKpCustomerPhone("开票信息电话");
        carrier.setKpCustomerAddress("开票信息地址");
        carrier.setKpBankName("开票信息开户行");
        carrier.setKpBankAccountNo("开票信息银行账号");
        carrier.setCreateTime("2023-10-01 12:00:00");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(carrier);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/company/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }


    /**
     * 查询托运人
     *
     * @throws Exception
     */
    @Test
    void testQueryCompany() throws Exception {
        xhlService.queryCompany("特运通科技");
    }

    @Test
    void testQueryCompanyMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/company/query")
                        .param("companyName", "特运通科技")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 修改托运人
     *
     * @throws Exception
     */
    @Test
    void testUpdateCompany() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setPerson("刘刚");
        xhlService.updateCompany(carrier);
    }

    @Test
    void testUpdateCompanyMock() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setPerson("刘刚");
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(carrier);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/company/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }


    /**
     * 托运人绑卡
     *
     * @throws Exception
     */
    @Test
    void testSaveBank() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setBankNo("****************");
        carrier.setBankTypeName("北京银行");
        carrier.setBankBranchName("中关村支行");
        carrier.setBranchNo("000000");
        xhlService.saveBank(carrier);
    }

    @Test
    void testSaveBankMock() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setBankNo("****************");
        carrier.setBankTypeName("北京银行");
        carrier.setBankBranchName("中关村支行");
        carrier.setBranchNo("000000");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(carrier);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/company/bank/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));

    }

    /**
     * 托运人解绑银行卡
     *
     * @throws Exception
     */
    @Test
    void testDeleteBank() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setBankNo("****************");
        xhlService.deleteBank(carrier);
    }

    @Test
    void testDeleteBankMock() throws Exception {
        XhlCarrierEntity carrier = new XhlCarrierEntity();
        carrier.setCompanyName("特运通科技");
        carrier.setBankNo("****************");
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(carrier);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/company/bank/delete")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }


    /**
     * 查询托运人余额
     *
     * @throws Exception
     */
    @Test
    void testQueryBalance() throws Exception {
        xhlService.queryBalance("嘉祥晨光运输有限公司");
    }

    @Test
    void testQueryBalanceMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/company/balance")
                        .param("companyName", "特运通科技")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }
}