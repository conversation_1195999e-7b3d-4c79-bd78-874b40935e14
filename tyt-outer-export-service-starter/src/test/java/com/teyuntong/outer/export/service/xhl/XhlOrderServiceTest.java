package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlOrderEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlOrderService;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlTruckService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlOrderServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlOrderService xhlOrderService;

    /**
     * 新增订单
     *
     * @throws Exception
     */
    @Test
    void testSaveOrder() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setAppId("appId"); // 必填
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpOrderNo("ORD123456789"); // 必填
        orderEntity.setShipperName("张三"); // 必填
        orderEntity.setShipperPhone("13800001111"); // 必填
        orderEntity.setShippingAddress("北京市朝阳区某街道"); // 必填
        orderEntity.setStartLng("116.4074"); // 必填
        orderEntity.setStartLat("39.9042"); // 必填
        orderEntity.setStartArea("110100"); // 必填
        orderEntity.setConsigneeBranch("收货单位"); // 必填
        orderEntity.setConsigneeName("李四"); // 必填
        orderEntity.setConsigneePhone("13900002222"); // 必填
        orderEntity.setConsigneeAddress("上海市浦东新区某街道"); // 必填
        orderEntity.setEndLng("121.4737"); // 必填
        orderEntity.setEndLat("31.2304"); // 必填
        orderEntity.setEndArea("310115"); // 必填
        orderEntity.setGoodsName("煤炭"); // 必填
        orderEntity.setGoodsType("0100"); // 必填
        orderEntity.setWeight("10"); // 必填
        orderEntity.setUnitPrice("1000"); // 必填
        orderEntity.setGoodsVolume("0"); // 必填
        orderEntity.setGoodsNumber("0"); // 必填
        orderEntity.setValuationWay("1"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setCreateTime("2023-10-01 12:00:00"); // 必填
        orderEntity.setRemark("备注信息"); // 非必填

        xhlOrderService.saveOrder(orderEntity);

        log.info("Order Entity: {}", orderEntity);
    }


    @Test
    void testSaveOrderMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setAppId("appId"); // 必填
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpOrderNo("ORD1234567890"); // 必填
        orderEntity.setShipperName("张三"); // 必填
        orderEntity.setShipperPhone("13800001111"); // 必填
        orderEntity.setShippingAddress("北京市朝阳区某街道"); // 必填
        orderEntity.setStartLng("116.4074"); // 必填
        orderEntity.setStartLat("39.9042"); // 必填
        orderEntity.setStartArea("110100"); // 必填
        orderEntity.setConsigneeBranch("收货单位"); // 必填
        orderEntity.setConsigneeName("李四"); // 必填
        orderEntity.setConsigneePhone("13900002222"); // 必填
        orderEntity.setConsigneeAddress("上海市浦东新区某街道"); // 必填
        orderEntity.setEndLng("121.4737"); // 必填
        orderEntity.setEndLat("31.2304"); // 必填
        orderEntity.setEndArea("310115"); // 必填
        orderEntity.setGoodsName("煤炭"); // 必填
        orderEntity.setGoodsType("0100"); // 必填
        orderEntity.setWeight("10"); // 必填
        orderEntity.setUnitPrice("1000"); // 必填
        orderEntity.setGoodsVolume("0"); // 必填
        orderEntity.setGoodsNumber("0"); // 必填
        orderEntity.setValuationWay("1"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setCreateTime("2023-10-01 12:00:00"); // 必填
        orderEntity.setRemark("备注信息"); // 非必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/order/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));

    }

    /**
     * 查询订单
     *
     * @throws Exception
     */
    @Test
    void testQueryOrder() throws Exception {
        xhlOrderService.queryOrder("特运通科技", "ORD123456789");
    }


    @Test
    void testQueryOrderMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/order/query")
                        .param("companyName", "特运通科技")
                        .param("tpOrderNo", "ORD123456789")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 修改订单
     *
     * @throws Exception
     */
    @Test
    void testUpdateOrder() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpOrderNo("ORD123456789");
        orderEntity.setShipperName("张三");
        orderEntity.setShipperPhone("13800001111");
        orderEntity.setShippingAddress("北京市朝阳区某街道");
        orderEntity.setStartLng("116.4074");
        orderEntity.setStartLat("39.9042");
        orderEntity.setStartArea("110100");
        orderEntity.setConsigneeBranch("收货单位");
        orderEntity.setConsigneeName("李四");
        orderEntity.setConsigneePhone("13900002222");
        orderEntity.setConsigneeAddress("上海市浦东新区某街道");
        orderEntity.setEndLng("121.4737");
        orderEntity.setEndLat("31.2304");
        orderEntity.setEndArea("310115");
        orderEntity.setGoodsName("煤炭");
        orderEntity.setGoodsType("0100");
        orderEntity.setWeight("10");
        orderEntity.setUnitPrice("1000");
        orderEntity.setGoodsVolume("0");
        orderEntity.setGoodsNumber("0");
        orderEntity.setValuationWay("1");
        orderEntity.setShippingCharge("0");
        orderEntity.setFreight("0");
        orderEntity.setCreateTime("2023-10-01 12:00:00");
        orderEntity.setRemark("更新后的备注信息");

        xhlOrderService.updateOrder(orderEntity);

        log.info("Updated Order Entity: {}", orderEntity);
    }

    @Test
    void testUpdateOrderMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpOrderNo("ORD123456789");
        orderEntity.setShipperName("张三");
        orderEntity.setShipperPhone("13800001111");
        orderEntity.setShippingAddress("北京市朝阳区某街道");
        orderEntity.setStartLng("116.4074");
        orderEntity.setStartLat("39.9042");
        orderEntity.setStartArea("110100");
        orderEntity.setConsigneeBranch("收货单位");
        orderEntity.setConsigneeName("李四");
        orderEntity.setConsigneePhone("13900002222");
        orderEntity.setConsigneeAddress("上海市浦东新区某街道");
        orderEntity.setEndLng("121.4737");
        orderEntity.setEndLat("31.2304");
        orderEntity.setEndArea("310115");
        orderEntity.setGoodsName("煤炭");
        orderEntity.setGoodsType("0100");
        orderEntity.setWeight("10");
        orderEntity.setUnitPrice("1000");
        orderEntity.setGoodsVolume("0");
        orderEntity.setGoodsNumber("0");
        orderEntity.setValuationWay("1");
        orderEntity.setShippingCharge("0");
        orderEntity.setFreight("0");
        orderEntity.setCreateTime("2023-10-01 12:00:00");
        orderEntity.setRemark("更新后的备注信息");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/order/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 派单
     *
     * @throws Exception
     */
    @Test
    void testSendOrder() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpOrderNo("ORD123456789"); // 必填
        orderEntity.setTpWaybillNo("WB123456789"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setDriverIdCardNo("230822198801192016"); // 必填
        orderEntity.setPlateNumber("京A12345"); // 必填
        orderEntity.setSendTime("2025-01-14 14:00:00"); // 必填

        xhlOrderService.sendOrder(orderEntity);

    }

    @Test
    void testSendOrderMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpOrderNo("ORD1234567890"); // 必填
        orderEntity.setTpWaybillNo("WB1234567890"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setDriverIdCardNo("230822198801192016"); // 必填
        orderEntity.setPlateNumber("京A12345"); // 必填
        orderEntity.setSendTime("2025-01-14 14:00:00"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/order/send")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));

    }

    /**
     * 运单发车
     *
     * @throws Exception
     */
    @Test
    void testDepartWaybill() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setDepartTime("2025-01-14 17:00:00"); // 必填
        orderEntity.setDepartWeight("10");
        orderEntity.setDepartVolume("10");
        orderEntity.setDepartPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");

        xhlOrderService.departWaybill(orderEntity);
    }

    @Test
    void testDepartWaybillMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setDepartTime("2025-01-14 17:00:00"); // 必填
        orderEntity.setDepartWeight("10");
        orderEntity.setDepartVolume("10");
        orderEntity.setDepartPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/depart")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 运单签收
     *
     * @throws Exception
     */
    @Test
    void testSignWaybill() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setSignTime("2025-01-14 16:40:00"); // 必填
        orderEntity.setSignWeight("10");
        orderEntity.setSettleWeight("10");
        orderEntity.setSignVolume("10");
        orderEntity.setSettleVolume("10");
        orderEntity.setReceiptPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");

        xhlOrderService.signWaybill(orderEntity);
    }

    @Test
    void testSignWaybillMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setSignTime("2025-01-14 17:40:00"); // 必填
        orderEntity.setSignWeight("10");
        orderEntity.setSettleWeight("10");
        orderEntity.setSignVolume("10");
        orderEntity.setSettleVolume("10");
        orderEntity.setReceiptPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/sign")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 运单删除
     *
     * @throws Exception
     */
    @Test
    void testDeleteWaybill() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789"); // 必填

        xhlOrderService.deleteWaybill(orderEntity);
    }

    @Test
    void testDeleteWaybillMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789"); // 必填
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/delete")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 修改运单
     *
     * @throws Exception
     */
    @Test
    void testUpdateWaybill() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setSignTime("2025-01-14 16:00:00"); // 必填
        orderEntity.setSignWeight("10");
        orderEntity.setSettleWeight("10");
        orderEntity.setSignVolume("10");
        orderEntity.setSettleVolume("10");
        orderEntity.setReceiptPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");
        xhlOrderService.updateWaybill(orderEntity);
    }

    @Test
    void testUpdateWaybillMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setTpWaybillNo("WB123456789");
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setSignTime("2025-01-14 16:00:00"); // 必填
        orderEntity.setSignWeight("10");
        orderEntity.setSettleWeight("10");
        orderEntity.setSignVolume("10");
        orderEntity.setSettleVolume("10");
        orderEntity.setReceiptPhoto("https://www.teyuntong.com/gw/public_images/btu2.png");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 查询运单
     *
     * @throws Exception
     */
    @Test
    void testQueryWaybill() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/waybill/query")
                        .param("companyName", "特运通科技")
                        .param("tpWaybillNo", "WB123456789")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }


    @Test
    void testSaveWaybillMock() throws Exception {
        XhlOrderEntity orderEntity = new XhlOrderEntity();
        orderEntity.setAppId("appId"); // 必填
        orderEntity.setCompanyName("特运通科技"); // 必填
        orderEntity.setTpOrderNo("ORD1234567891"); // 必填
        orderEntity.setShipperName("张三"); // 必填
        orderEntity.setShipperPhone("13800001111"); // 必填
        orderEntity.setShippingAddress("北京市朝阳区某街道"); // 必填
        orderEntity.setStartLng("116.4074"); // 必填
        orderEntity.setStartLat("39.9042"); // 必填
        orderEntity.setStartArea("110100"); // 必填
        orderEntity.setConsigneeBranch("收货单位"); // 必填
        orderEntity.setConsigneeName("李四"); // 必填
        orderEntity.setConsigneePhone("13900002222"); // 必填
        orderEntity.setConsigneeAddress("上海市浦东新区某街道"); // 必填
        orderEntity.setEndLng("121.4737"); // 必填
        orderEntity.setEndLat("31.2304"); // 必填
        orderEntity.setEndArea("310115"); // 必填
        orderEntity.setGoodsName("煤炭"); // 必填
        orderEntity.setGoodsType("0100"); // 必填
        orderEntity.setWeight("10"); // 必填
        orderEntity.setUnitPrice("1000"); // 必填
        orderEntity.setGoodsVolume("0"); // 必填
        orderEntity.setGoodsNumber("0"); // 必填
        orderEntity.setValuationWay("1"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setCreateTime("2023-10-01 12:00:00"); // 必填
        orderEntity.setRemark("备注信息"); // 非必填

        orderEntity.setTpWaybillNo("WB1234567891"); // 必填
        orderEntity.setShippingCharge("0"); // 必填
        orderEntity.setFreight("0"); // 必填
        orderEntity.setDriverIdCardNo("230822198801192016"); // 必填
        orderEntity.setPlateNumber("京A12345"); // 必填
        orderEntity.setSendTime("2025-01-14 14:00:00"); // 必填


        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(orderEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));

    }
}