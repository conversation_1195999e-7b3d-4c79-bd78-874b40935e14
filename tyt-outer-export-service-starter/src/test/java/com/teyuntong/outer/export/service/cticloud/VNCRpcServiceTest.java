package com.teyuntong.outer.export.service.cticloud;

import com.teyuntong.outer.export.service.TestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class VNCRpcServiceTest extends TestBase {

    @Test
    void testGetRecordUrl() throws Exception {
        // Arrange
        String recordName = "testRecording";
        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/vnc/getRecordUrl")
                        .param("recordName", recordName))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    void testAsrCreate() throws Exception {
        // Arrange
        String callId = "12345";
        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/vnc/asrCreate")
                        .param("callId", callId))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    void testAsrQuery() throws Exception {
        // Arrange
        String callId = "12345";
        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/vnc/asrQuery")
                        .param("callId", callId))
                .andExpect(status().isOk())
                .andReturn();
    }


}