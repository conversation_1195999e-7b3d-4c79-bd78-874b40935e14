package com.teyuntong.outer.export.service.transport;

import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.transport.service.WordSplitService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 分词提取
 *
 * <AUTHOR>
 * @date 2023/3/20 15:25
 */
@Disabled
class WordSplitServiceTest extends TestBase {

    @Autowired
    WordSplitService wordSplitService;

    @Test
    void splitContent() {

        String[] contentArray = {"小松挖掘机200", "挖掘机小松200", "宝马翻斗003", "长江7号", "装备20吨", "@@@测试解放162推土机测试"};

        for(String oneContent : contentArray){

            List<String> nameList = wordSplitService.splitContent(oneContent);

            String nameListStr = StringUtils.join(nameList, "\n");

            System.out.println(nameListStr);

        }

        System.out.println("finished ... ");
        assertNotNull(contentArray);
    }
}