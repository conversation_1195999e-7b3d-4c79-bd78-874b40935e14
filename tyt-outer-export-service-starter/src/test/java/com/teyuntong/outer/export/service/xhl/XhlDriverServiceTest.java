package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.invoice.xhl.dto.XhlDriverDTO;
import com.teyuntong.outer.export.service.client.invoice.xhl.service.InvoiceXhlOpenApiService;
import com.teyuntong.outer.export.service.client.invoice.xhl.vo.XhlDriverVO;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlCarrierEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDataResult;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDriverEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlDriverTruckEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlDriverService;
import com.teyuntong.outer.export.service.service.biz.xhl.util.TytBeanUtil;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlDriverServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlDriverService xhlDriverService;

    @Autowired
    InvoiceXhlOpenApiService invoiceXhlOpenApiService;

    /**
     * 新增驾驶员
     *
     * @throws Exception
     */
    @Test
    void testSaveDriver() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setAppId("800007"); // 必填
        driverEntity.setDriverPhone("13800003333"); // 必填
        driverEntity.setDriverName("李四"); // 必填
        driverEntity.setIdCardNo("530127198908101051"); // 必填
        driverEntity.setAddress("北京市朝阳区某街道"); // 必填
        driverEntity.setDriverType("A1"); // 必填
        driverEntity.setCardStartUseDate("2000-01-01"); // 必填
        driverEntity.setCardEndUseDate("长期"); // 必填
        driverEntity.setStartUseDate("2010-01-01"); // 必填
        driverEntity.setEndUseDate("长期"); // 必填
        driverEntity.setIdCardNoFrontUrl("https://example.com/id_card_front.jpg"); // 必填
        driverEntity.setIdCardNoBackUrl("https://example.com/id_card_back.jpg"); // 必填
        driverEntity.setDrivingLicenseUrl("https://example.com/driving_license.jpg"); // 必填
        driverEntity.setDrivingLicenseBackUrl("https://example.com/driving_license_back.jpg"); // 非必填
        driverEntity.setQualiCertNoUrl("https://example.com/qualification_cert.jpg"); // 必填
        driverEntity.setQualificationCertNo("**********"); // 必填
        driverEntity.setQualificationCertExpireTime("2025-12-31"); // 必填
        driverEntity.setIssuingOrganizations("北京市公安局交通管理局"); // 必填
        driverEntity.setCreateTime("2023-10-01 12:00:00"); // 必填
        // XhlDataResult<Object> result = xhlDriverService.saveDriver(driverEntity);
        XhlDriverDTO entity = TytBeanUtil.convertBean(driverEntity, XhlDriverDTO.class);
        WebResult<XhlDriverVO> xhlDriverVOWebResult = invoiceXhlOpenApiService.saveDriver(entity);
        log.info("新增驾驶员结果：{}", xhlDriverVOWebResult);
    }

    @Test
    void testSaveDriverMock() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setAppId("800007"); // 必填
        driverEntity.setDriverPhone("13800001111"); // 必填
        driverEntity.setDriverName("张三"); // 必填
        driverEntity.setIdCardNo("230822198801192016"); // 必填
        driverEntity.setAddress("北京市朝阳区某街道"); // 必填
        driverEntity.setDriverType("A1"); // 必填
        driverEntity.setCardStartUseDate("2000-01-01"); // 必填
        driverEntity.setCardEndUseDate("长期"); // 必填
        driverEntity.setStartUseDate("2010-01-01"); // 必填
        driverEntity.setEndUseDate("长期"); // 必填
        driverEntity.setIdCardNoFrontUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        driverEntity.setIdCardNoBackUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        driverEntity.setDrivingLicenseUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        driverEntity.setDrivingLicenseBackUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 非必填
        driverEntity.setQualiCertNoUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 必填
        driverEntity.setQualificationCertNo("**********"); // 必填
        driverEntity.setQualificationCertExpireTime("2025-12-31"); // 必填
        driverEntity.setIssuingOrganizations("北京市公安局交通管理局"); // 必填
        driverEntity.setCreateTime("2023-10-01 12:00:00"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/driver/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }


    /**
     * 查询驾驶员
     *
     * @throws Exception
     */
    @Test
    void testQueryDriver() throws Exception {
        XhlDataResult<Object> result = xhlDriverService.queryDriver("530127198908101051");
        log.info("查询驾驶员结果：{}", result);
    }

    @Test
    void testQueryDriverMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/driver/query")
                        .param("idCardNo", "230822198801192016")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 修改驾驶员
     *
     * @throws Exception
     */
    @Test
    void testUpdateDriver() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setDriverName("刘刚");
        xhlDriverService.updateDriver(driverEntity);
    }

    @Test
    void testUpdateDriverMock() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setDriverName("刘刚");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/driver/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 驾驶员绑卡
     *
     * @throws Exception
     */
    @Test
    void testSaveBank() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setAppId("800007");
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setBankNo("****************");
        xhlDriverService.saveBank(driverEntity);
    }

    @Test
    void testSaveBankMock() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setBankNo("****************");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/driver/bank/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 驾驶员解绑银行卡
     *
     * @throws Exception
     */
    @Test
    void testDeleteBank() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setAppId("800007");
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setBankNo("****************");
        xhlDriverService.deleteBank(driverEntity);
    }

    @Test
    void testDeleteBankMock() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setAppId("800007");
        driverEntity.setIdCardNo("230822198801192016");
        driverEntity.setBankNo("****************");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/driver/bank/delete")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }


    /**
     * 新增人车合照
     *
     * @throws Exception
     */
    @Test
    void testSaveDriverTruck() throws Exception {
        XhlDriverTruckEntity driverTruckEntity = new XhlDriverTruckEntity();
        driverTruckEntity.setIdCardNo("230822198801192016"); // 必填
        driverTruckEntity.setPlateNumber("京A12345"); // 必填
        driverTruckEntity.setDriverTruckUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335"); // 必填
        xhlDriverService.saveDriverTruck(driverTruckEntity);
    }

    @Test
    void testSaveDriverTruckMock() throws Exception {
        XhlDriverTruckEntity driverTruckEntity = new XhlDriverTruckEntity();
        driverTruckEntity.setIdCardNo("230822198801192016"); // 必填
        driverTruckEntity.setPlateNumber("京A12345"); // 必填
        driverTruckEntity.setDriverTruckUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=335"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverTruckEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/rel/driver_truck/save")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 查询人车合照
     *
     * @throws Exception
     */
    @Test
    void testQueryDriverTruck() throws Exception {
        xhlDriverService.queryDriverTruck("230822198801192016", "京A12345");
    }

    @Test
    void testQueryDriverTruckMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/rel/driver_truck/query")
                        .param("idCardNo", "230822198801192016")
                        .param("plateNumber", "京A12345")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 修改人车合照
     *
     * @throws Exception
     */
    @Test
    void testUpdateDriverTruck() throws Exception {
        XhlDriverTruckEntity driverTruckEntity = new XhlDriverTruckEntity();
        driverTruckEntity.setIdCardNo("230822198801192016"); // 必填
        driverTruckEntity.setPlateNumber("京A12345"); // 必填
        driverTruckEntity.setDriverTruckUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=3351"); // 必填
        xhlDriverService.updateDriverTruck(driverTruckEntity);
    }

    @Test
    void testUpdateDriverTruckMock() throws Exception {
        XhlDriverTruckEntity driverTruckEntity = new XhlDriverTruckEntity();
        driverTruckEntity.setIdCardNo("230822198801192016"); // 必填
        driverTruckEntity.setPlateNumber("京A12345"); // 必填
        driverTruckEntity.setDriverTruckUrl("https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPG?w=475&h=3351"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverTruckEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/rel/driver_truck/update")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 新增司机授权（委托代征）
     *
     * @throws Exception
     */
    @Test
    void testSaveEmpowerDriver() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setIdCardNo("230822198801192016"); // 必填
        xhlDriverService.saveEmpowerDriver(driverEntity);
    }

    @Test
    void testSaveEmpowerDriverMock() throws Exception {
        XhlDriverEntity driverEntity = new XhlDriverEntity();
        driverEntity.setIdCardNo("230822198801192016"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(driverEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/contract/driver/empower")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 查询司机授权（委托代征）
     *
     * @throws Exception
     */
    @Test
    void testQueryEmpowerDriver() throws Exception {
        xhlDriverService.queryEmpowerDriver("230822198801192016");
    }

    @Test
    void testQueryEmpowerDriverMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/contract/driver/query")
                        .param("idCardNo", "230822198801192016")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

}