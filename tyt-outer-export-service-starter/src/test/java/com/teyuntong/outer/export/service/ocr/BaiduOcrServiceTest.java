package com.teyuntong.outer.export.service.ocr;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.*;
import com.teyuntong.outer.export.service.service.biz.ocr.service.BaiduOcrService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2023/4/25 18:24
 */
@Slf4j
@Disabled
public class BaiduOcrServiceTest extends TestBase {

    @Autowired
    private BaiduOcrService baiduOcrService;

    @Test
    void idCardFrontOcr() {
        String url = "http://tytaudio.teyuntong.net/local/card_img/idCardFront/20230426/card_front_D6E665905B5B480E8903F5E594BC07DA.jpg";

//        url = "https://www.chinanews.com.cn/sh/2015/05-28/U557P4T8D7307126F107DT20150528105550.jpg";

//        url = "http://tytaudio.teyuntong.net/local/card_img/idCardFront/20230426/card_front_96597E8FD7654B4B9CFC734DB80887AF.png";

        url = "http://tytaudio.teyuntong.net/local/card_img/idCard_back/20230427/card_front_back_7231C6691C9748218BDC9F34C15E4400.png";

        OcrIdCardFrontVo ocrIdCardFrontVo = baiduOcrService.idCardFrontOcr(url);

        System.out.println("finished ... ");
    }

    @Test
    void idCardBackOcr() {

        String url = "http://tytaudio.teyuntong.net/local/card_img/idCard_back/20230426/card_front_D7357FE8AA0948A6A5D998F4F86CEE5C.png";

        OcrIdCardBackVo ocrIdCardBackVo = baiduOcrService.idCardBackOcr(url);

        System.out.println("finished ... ");

    }

    @Test
    void driverLicenseFrontOcr() {
        String url = "http://tytaudio.teyuntong.net/local/card_img/driver_card/20230427/card_front_back_6B7A0ED57A304DE79B4380419ED724F1.png";
        DriverLicenseFrontVo driverLicenseFrontVo = baiduOcrService.driverLicenseFrontOcr(url);
        System.out.println("finished ... ");
    }

    @Test
    void driverLicenseMainOcr() {

        String url = "http://tytaudio.teyuntong.net/local/card_img/driver_card/20230427/card_front_back_6B7A0ED57A304DE79B4380419ED724F1.png";

    }

    @Test
    void driverLicenseBackOcr() {
        String url = "http://tytaudio.teyuntong.net/local/card_img/driver_card/20230427/card_front_back_6B7A0ED57A304DE79B4380419ED724F1.png";
        DriverLicenseBackVo driverLicenseBackVo = baiduOcrService.driverLicenseBackOcr(url);
        System.out.println("finished ... ");
    }

    @Test
    void vehicleLicenseMainOcr() {
        String url = "http://peimages.teyuntong.net/dispatch/APP/2024-3-6/090c2d130094026deafebe054f40aca6.png";
        VehicleLicenseFrontVo vehicleLicenseFrontVo = baiduOcrService.vehicleLicenseFrontOcr(url);
        System.out.println("finished ... " + vehicleLicenseFrontVo);
    }

    @Test
    void vehicleLicenseBackOcr() {
        String url = "http://peimages.teyuntong.net/dispatch/APP/2024-3-6/090c2d130094026deafebe054f40aca6.png";
        VehicleLicenseBackVo vehicleLicenseBackVo = baiduOcrService.vehicleLicenseBackOcr(url);
        System.out.println("finished ... " + vehicleLicenseBackVo);
    }

    @Test
    void businessLicenseOcr() {

        String url = "http://tytaudio.teyuntong.net/local/card_img/business_card/20230428/business_1A263CC3345B4DC4B478ACB19C2FD83C.png";

        OcrBusinessLicenseVo businessLicenseVo = baiduOcrService.businessLicenseOcr(url);

        System.out.println("finished ... ");

    }

    @Test
    void roadTransportOcr() {

        String url = "http://tytaudio.teyuntong.net/local/card_img/business_card/20230428/business_0620C1B03D44431A9DFAB29D927004B2.png";

        RoadTransportVo roadTransportVo = baiduOcrService.roadTransportOcr(url);

        System.out.println("finished ... " + roadTransportVo);
    }

    @Test
    public void testVehicleLicenseBackOcr() {

        System.out.println("start ... ");

        String[] urlArray = {
                "http://peimages.teyuntong.net/dispatch/APP/2024-04-06/0437927463bb26092fcff02732ff361cc02da1e.jpg",
                "http://peimages.teyuntong.net/dispatch/APP/2024-3-6/090c2d130094026deafebe054f40aca6.png"
        };

        for (int i = 0; i < 15; i++) {

            String url = urlArray[15];

            System.out.println(url);

            Long startTime = System.currentTimeMillis();

            DriverLicenseFrontVo driverLicenseFrontVo = baiduOcrService.driverLicenseFrontOcr(url);

            log.info(JSON.toJSONString(driverLicenseFrontVo));


            VehicleLicenseBackVo vehicleLicenseBackVo = baiduOcrService.vehicleLicenseBackOcr(url);

            log.info(JSON.toJSONString(vehicleLicenseBackVo));


            log.info("===============================");

        }

        log.info("finished ... ");

        assertTrue(true);

    }

}