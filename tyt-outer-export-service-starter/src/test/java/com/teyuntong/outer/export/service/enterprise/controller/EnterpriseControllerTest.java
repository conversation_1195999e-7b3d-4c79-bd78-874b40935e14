package com.teyuntong.outer.export.service.enterprise.controller;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.enterprise.vo.EnterpriseAccountApiReq;
import com.teyuntong.outer.export.service.client.enterprise.vo.QueryEnterpriseBalanceApiReq;
import com.teyuntong.outer.export.service.client.enterprise.vo.QueryEnterpriseBillApiReq;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateContractRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateProjectRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateCompanyRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateProjectResponse;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.CreateDetailDO;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.CreateDetailVO;
import com.teyuntong.outer.export.service.service.biz.hbwj.pojo.PrincipalInfoVO;
import com.teyuntong.outer.export.service.service.biz.hbwj.service.HBWJService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class EnterpriseControllerTest extends TestBase {

    @Autowired
    HBWJService hBWJService;

    @Test
    void getPrincipalList() throws Exception {
        List<PrincipalInfoVO> principalList = hBWJService.getPrincipalList();
        log.info("principal list :{}", JSON.toJSONString(principalList));
    }

    @Test
    void createCompany() throws Exception {
        CreateCompanyRequest createCompanyRequest = new CreateCompanyRequest();
        createCompanyRequest.setCompanyName("北京邦利德网络科技有限公司");
        createCompanyRequest.setUnifiedCreditcCode("91110108MA01G0FB09");
        createCompanyRequest.setAdminName("王云龙");
        createCompanyRequest.setAdminPhone("***********");
        createCompanyRequest.setSettlementPrincipalId(2L);
        createCompanyRequest.setBusinessLicenseUrl("http://devimage.teyuntong.net/dispatch/APP/2024-03-27/01002000430ac1b0c87131dae31c3b34dacee08fe53.jpg");
        createCompanyRequest.setTradeTimeStart("2023-01-01 00:00:00");
        createCompanyRequest.setTradeTimeEnd("2025-12-31 23:59:59");
        createCompanyRequest.setFinanceContact("王云龙");
        createCompanyRequest.setFinanceContactTel("***********");
        createCompanyRequest.setBusinessContact("王云龙");
        createCompanyRequest.setBusinessContactTel("***********");
        hBWJService.createCompany(createCompanyRequest);
    }

    @Test
    void createContract() throws Exception {
        CreateContractRequest createContractRequest = new CreateContractRequest();
        createContractRequest.setContractFirstPartyId(280L);
        createContractRequest.setContractFirstPartyName("北京邦利德网络科技有限公司");
        createContractRequest.setContractSecondPartyId(2L);
        createContractRequest.setContractSecondPartyName("湖北我家物流服务有限公司");
        createContractRequest.setContractRate(new BigDecimal("10"));
        createContractRequest.setContractStartDate("2024-07-18 00:00:00");
        createContractRequest.setContractEndDate("2025-07-18 00:00:00");
        hBWJService.createContract(createContractRequest);
    }

    @Test
    void queryCompanyDetail() throws Exception {
        CreateDetailDO createDetailDO = new CreateDetailDO();
        createDetailDO.setUnifiedCreditcCode("91110108MA01G0FB09");
        CreateDetailVO createDetailVO = hBWJService.queryCompanyDetail(createDetailDO);
        log.info("createDetailVO:{}", JSON.toJSONString(createDetailVO));
    }

    @Test
    void createProject() throws Exception {
        CreateProjectRequest createProjectRequest = new CreateProjectRequest();
        createProjectRequest.setContractNo("******************");
        createProjectRequest.setProjectName("******************");
        createProjectRequest.setProjectSimpleName("******************");
        CreateProjectResponse project = hBWJService.createProject(createProjectRequest, "YZM_T3wVRznSKf");
        log.info("project result:{}", JSON.toJSONString(project));
    }

    @Test
    void enterpriseOpenAccount() throws Exception {
        EnterpriseAccountApiReq req = new EnterpriseAccountApiReq();
        req.setUid("111111");
        req.setEnterpriseName("苑小妹测试001");
        req.setLoginName("111111");
        mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/enterpriseOpenAccount")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }


    @Test
    void enterpriseOpenNetAccount() throws Exception {
        EnterpriseAccountApiReq req = new EnterpriseAccountApiReq();
        req.setUid("111111");
        req.setEnterpriseName("苑小妹测试001");
        req.setLoginName("111111");
        mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/enterpriseOpenNetAccount")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }

    @Test
    void queryEnterpriseNetAccountInfo() throws Exception {
        EnterpriseAccountApiReq req = new EnterpriseAccountApiReq();
        req.setUid("111111");
        req.setEnterpriseName("苑小妹测试001");
        req.setLoginName("111111");
        mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/queryEnterpriseNetAccountInfo")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }



    @Test
    void queryEnterpriseBalance() throws Exception {
        QueryEnterpriseBalanceApiReq req = new QueryEnterpriseBalanceApiReq();
        req.setUid("910000384769910001B");

        mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/queryEnterpriseBalance")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }


    @Test
    void queryEnterpriseBill() throws Exception {
        QueryEnterpriseBillApiReq req = new QueryEnterpriseBillApiReq();
        req.setUid("910000384769910001B");

        mockMvc.perform(MockMvcRequestBuilders.get("/enterprise/queryEnterpriseBill")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());

        Assertions.assertTrue(true);
    }




}