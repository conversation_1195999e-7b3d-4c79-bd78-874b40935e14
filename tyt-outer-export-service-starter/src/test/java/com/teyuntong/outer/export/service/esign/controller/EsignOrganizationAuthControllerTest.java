package com.teyuntong.outer.export.service.esign.controller;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.esign.vo.identity.EnterpriseFourFactorsReq;
import com.teyuntong.outer.export.service.client.esign.vo.identity.EnterpriseThreeFactorsReq;
import com.teyuntong.outer.export.service.client.esign.vo.identity.EnterpriseThreeFactorsVO;
import com.teyuntong.outer.export.service.client.esign.vo.identity.LegalRepSignReq;
import com.teyuntong.outer.export.service.service.rpc.esign.EsignOrganizationAuthController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class EsignOrganizationAuthControllerTest extends TestBase {

    @Autowired
    private EsignOrganizationAuthController esignOrganizationAuthController;

    @Test
    void testEnterpriseFourFactors() throws Exception {
        EnterpriseFourFactorsReq req = new EnterpriseFourFactorsReq();
        req.setName("123");
        req.setOrgCode("123");
        req.setLegalRepName("123");
        req.setLegalRepIdNo("123");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/identity/auth/organization/enterprise/fourFactors")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void testLegalRepSign() throws Exception {
        LegalRepSignReq req = new LegalRepSignReq();
        req.setFlowId("123");
        req.setAgentName("123");
        req.setAgentIdNo("123");
        req.setMobileNo("123");
        req.setLegalRepIdNo("123");

        mockMvc.perform(MockMvcRequestBuilders.post("/esign/identity/auth/organization/legalRepSign")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONBytes(req))
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void testSignUrl() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/esign/identity/auth/organization/signUrl")
                        .queryParam("flowId", "123")
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void testLegalRepSignResult() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/esign/identity/auth/organization/legalRepSignResult")
                        .queryParam("flowId", "123")
                )
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void enterpriseThreeFactors() throws Exception{

        EnterpriseThreeFactorsReq req = new EnterpriseThreeFactorsReq();

        req.setName("esigntest北京邦利德网络科技有限公司PAAK");
        req.setOrgCode("910000384769910010");
        req.setLegalRepName("余长河");
        req.setContextId("100536");
        //req.setNotifyUrl();
        //req.setAgentFlowId("3274167118677411552");

        WebResult<EnterpriseThreeFactorsVO> enterpriseThreeFactorsVOResultMsgBean = esignOrganizationAuthController.enterpriseThreeFactors(req);

        Assertions.assertTrue(true);

    }


}