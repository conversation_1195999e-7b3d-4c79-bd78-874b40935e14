package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlFreightEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlTruckEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlFreightService;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlTruckService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlFreightServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlFreightService xhlFreightService;

    /**
     * 运费打款
     *
     * @throws Exception
     */
    @Test
    void testPayMoney() throws Exception {
        XhlFreightEntity freightEntity = new XhlFreightEntity();
        freightEntity.setCompanyName("特运通科技"); // 必填
        freightEntity.setTpWaybillNo("WB123456789"); // 必填
        freightEntity.setHdRunningNumber("HD123456789");
        freightEntity.setFreight("1000"); // 必填
        freightEntity.setServiceCharge("100"); // 必填
        freightEntity.setAgentWay("1"); // 必填
        freightEntity.setPayType("2"); // 必填，默认到付2
        freightEntity.setOwnerName("李四"); // 必填
        freightEntity.setIdCardNo("230822198801192016"); // 必填
        freightEntity.setBankNo("****************"); // 必填
        freightEntity.setBankTypeName("中国工商银行"); // 必填
        freightEntity.setBankBranchNo("************"); // 必填
        xhlFreightService.payMoney(freightEntity);
    }

    @Test
    void testPayMoneyMock() throws Exception {
        XhlFreightEntity freightEntity = new XhlFreightEntity();
        freightEntity.setCompanyName("特运通科技"); // 必填
        freightEntity.setTpWaybillNo("WB123456789"); // 必填
        freightEntity.setHdRunningNumber("HD123456789");
        freightEntity.setFreight("1000"); // 必填
        freightEntity.setServiceCharge("100"); // 必填
        freightEntity.setAgentWay("1"); // 必填
        freightEntity.setPayType("2"); // 必填，默认到付2
        freightEntity.setOwnerName("李四"); // 必填
        freightEntity.setIdCardNo("230822198801192016"); // 必填
        freightEntity.setBankNo("****************"); // 必填
        freightEntity.setBankTypeName("中国工商银行"); // 必填
        freightEntity.setBankBranchNo("************"); // 必填

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(freightEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/payMoney")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }

    /**
     * 查询运费支付结果
     *
     * @throws Exception
     */
    @Test
    void testQueryPay() throws Exception {
        xhlFreightService.queryPay("特运通科技", "WB123456789");
    }

    @Test
    void testQueryPayMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/waybill/queryPay")
                        .param("companyName", "特运通科技")
                        .param("tpWaybillNo", "WB123456789")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 修改运费
     *
     * @throws Exception
     */
    @Test
    void testUpdateCharge() throws Exception {
        XhlFreightEntity freightEntity = new XhlFreightEntity();
        freightEntity.setCompanyName("特运通科技"); // 必填
        freightEntity.setTpWaybillNo("WB123456789"); // 必填
        freightEntity.setShippingCharge("100");
        freightEntity.setFreight("1000"); // 必填
        freightEntity.setDepartWeight("100"); // 必填
        freightEntity.setSignWeight("1"); // 必填
        freightEntity.setSettleWeight("2"); // 必填，默认到付2
        xhlFreightService.updateCharge(freightEntity);

    }

    @Test
    void testUpdateChargeMock() throws Exception {
        XhlFreightEntity freightEntity = new XhlFreightEntity();
        freightEntity.setCompanyName("特运通科技"); // 必填
        freightEntity.setTpWaybillNo("WB123456789"); // 必填
        freightEntity.setShippingCharge("100");
        freightEntity.setFreight("1000"); // 必填
        freightEntity.setDepartWeight("100"); // 必填
        freightEntity.setSignWeight("1"); // 必填
        freightEntity.setSettleWeight("2"); // 必填，默认到付2

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(freightEntity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/waybill/updateCharge")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }



}