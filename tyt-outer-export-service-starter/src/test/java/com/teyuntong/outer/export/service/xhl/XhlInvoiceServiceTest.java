package com.teyuntong.outer.export.service.xhl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.xhl.config.XhlRetrofitSupplier;
import com.teyuntong.outer.export.service.service.biz.xhl.pojo.XhlInvoiceEntity;
import com.teyuntong.outer.export.service.service.biz.xhl.service.XhlInvoiceService;
import com.teyuntong.outer.export.service.service.common.property.XhlProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@EnableConfigurationProperties(XhlProperties.class)
@Disabled
class XhlInvoiceServiceTest extends TestBase {

    @Autowired
    XhlProperties xhlProperties;

    @Autowired
    XhlRetrofitSupplier xhlRetrofitSupplier;

    @Autowired
    XhlInvoiceService xhlInvoiceService;

    /**
     * 发票申请
     *
     * @throws Exception
     */
    @Test
    void testApplyInvoice() throws Exception {
        XhlInvoiceEntity entity = new XhlInvoiceEntity();
        entity.setCompanyName("特运通科技"); // 真实的公司名称
        entity.setInvoiceApplyNo("INV123456789"); // 真实的发票申请单号
        entity.setTpWaybillNos("WB123456789,WB987654321"); // 真实的三方运单号，多个逗号隔开
        entity.setKpCustomerName("开票方公司名称"); // 真实的开票方公司名称
        entity.setKpTaxNo("123456789012345678"); // 真实的开票方统一社会代码
        entity.setKpCustomerAddress("北京市朝阳区某某路某某号"); // 真实的开票方公司地址
        entity.setKpCustomerPhone("***********"); // 真实的开票方电话
        entity.setKpBankName("中国工商银行"); // 真实的开票方开户银行
        entity.setKpBankAccountNo("****************"); // 真实的开票方银行账户
        entity.setType("1"); // 发票类型，1:数电票，2:纸质发票
        entity.setHasList("1"); // 开票清单，1:需要清单，2:不需要清单
        entity.setUnit("吨"); // 开票单位（吨、方、件等）
        entity.setNum("10"); // 数量
        entity.setNorms("标准规格"); // 规格类型
        entity.setInvoiceListUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 发票清单地址
        entity.setRemark("这是发票备注"); // 发票备注
        // 调用申请发票的方法
        xhlInvoiceService.applyInvoice(entity);
    }

    @Test
    void testApplyInvoiceMock() throws Exception {
        XhlInvoiceEntity entity = new XhlInvoiceEntity();
        entity.setCompanyName("特运通科技"); // 真实的公司名称
        entity.setInvoiceApplyNo("INV123456789"); // 真实的发票申请单号
        entity.setTpWaybillNos("WB123456789,WB987654321"); // 真实的三方运单号，多个逗号隔开
        entity.setKpCustomerName("开票方公司名称"); // 真实的开票方公司名称
        entity.setKpTaxNo("123456789012345678"); // 真实的开票方统一社会代码
        entity.setKpCustomerAddress("北京市朝阳区某某路某某号"); // 真实的开票方公司地址
        entity.setKpCustomerPhone("***********"); // 真实的开票方电话
        entity.setKpBankName("中国工商银行"); // 真实的开票方开户银行
        entity.setKpBankAccountNo("****************"); // 真实的开票方银行账户
        entity.setType("1"); // 发票类型，1:数电票，2:纸质发票
        entity.setHasList("1"); // 开票清单，1:需要清单，2:不需要清单
        entity.setUnit("吨"); // 开票单位（吨、方、件等）
        entity.setNum("10"); // 数量
        entity.setNorms("标准规格"); // 规格类型
        entity.setInvoiceListUrl("https://www.teyuntong.com/gw/public_images/btu2.png"); // 发票清单地址
        entity.setRemark("这是发票备注"); // 发票备注
        // 调用申请发票的方法
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(entity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/invoice/apply")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));

    }

    /**
     * 查询发票申请
     *
     * @throws Exception
     */
    @Test
    void testQueryInvoice() throws Exception {
        xhlInvoiceService.queryInvoice("INV123456789");
    }

    @Test
    void testQueryInvoiceMock() throws Exception {
        MvcResult resultActions = mockMvc.perform(MockMvcRequestBuilders.get("/invoice/xhl/invoice/query")
                        .param("invoiceApplyNo", "INV123456789")
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = resultActions.getResponse().getContentAsString(StandardCharsets.UTF_8);
        log.info("Response Content: {}", responseContent);
    }

    /**
     * 发票取消
     *
     * @throws Exception
     */
    @Test
    void testDeleteInvoice() throws Exception {
        XhlInvoiceEntity entity = new XhlInvoiceEntity();
        entity.setInvoiceApplyNo("INV123456789");
        entity.setCompanyName("特运通科技");
        xhlInvoiceService.deleteInvoice(entity);
    }

    @Test
    void testDeleteInvoiceMock() throws Exception {
        XhlInvoiceEntity entity = new XhlInvoiceEntity();
        entity.setInvoiceApplyNo("INV123456789");
        entity.setCompanyName("特运通科技");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(entity);

        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.post("/invoice/xhl/invoice/delete")
                        .content(jsonRequest)
                        .header(HttpHeaders.CONTENT_TYPE, "application/json"))
                .andDo((it) -> System.out.println(it.getResponse().getContentAsString(StandardCharsets.UTF_8)));

        System.out.println(JSONUtil.toJsonStr(resultActions));
    }



}