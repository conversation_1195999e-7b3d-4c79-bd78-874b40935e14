package com.teyuntong.outer.export.service.service.mq;

import com.teyuntong.infra.common.rocketmq.core.BatchConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.ConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.OrderedConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.common.mq.OuterExportMessageType;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/11/08 13:49
 */
@Disabled
class RocketMqProducerTest extends TestBase {

    /**
     * 使用 @MockBean 防止 spring boot 创建 ConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private ConsumerBeanInitializer consumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 orderedConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private OrderedConsumerBeanInitializer orderedConsumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 batchConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private BatchConsumerBeanInitializer batchConsumerBeanInitializer;
    /**
     * rocket mq 消息发送者
     */
    @Autowired
    private RocketMqProducer producer;
    /**
     * 创建 rocket mq 消息的工厂
     */
    @Autowired
    private MqMessageFactory mqMessageFactory;

    private TmpMessageBean createMessageBean(Integer order) {
        Date now = new Date();
        return TmpMessageBean.builder()
                .createTime(now)
                .order(order)
                .content(RandomStringUtils.randomAlphabetic(16))
                .remark(RandomStringUtils.randomAlphabetic(32))
                .id(ThreadLocalRandom.current().nextLong(100000, 999999))
                .build();
    }

}
