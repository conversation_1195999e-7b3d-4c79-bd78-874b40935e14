package com.teyuntong.outer.export.service.service.biz.transport.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.account.dto.AccountRpcDTO;
import com.teyuntong.outer.export.service.client.account.dto.LicenseRpcDTO;
import com.teyuntong.outer.export.service.client.account.service.AccountRpcService;
import com.teyuntong.outer.export.service.client.account.vo.AccountInfoRpcVO;
import com.teyuntong.outer.export.service.client.account.vo.VehicleLicenseRpcVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBAccountDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.dto.MBLicenseDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.service.MBAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBAccountInfoVO;
import com.teyuntong.outer.export.service.service.biz.manbang.account.vo.MBLicenseVO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LastLocationDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.dto.LocationTraceDTO;
import com.teyuntong.outer.export.service.service.biz.manbang.location.service.LocationService;
import com.teyuntong.outer.export.service.service.biz.manbang.location.vo.*;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJHistoryTrackResult;
import com.teyuntong.outer.export.service.service.biz.zj.pojo.ZJVehicleLocationResult;
import com.teyuntong.outer.export.service.service.biz.zj.service.CarLocationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/05 16:45
 */
@Slf4j
@Disabled
public class AccountTest extends TestBase {

    @Autowired
    private MBAccountService mbAccountService;

    @Autowired
    private AccountRpcService accountRpcService;



    @Test
    void testAccount() {
//        MBAccountDTO mbAccountDTO = new MBAccountDTO();
//        mbAccountDTO.setMobile("***********");
//        List<MBAccountInfoVO> accountInfoVOList = mbAccountService.queryAccountsByMobile(mbAccountDTO);
        AccountRpcDTO accountRpcDTO = new AccountRpcDTO();
        accountRpcDTO.setMobile("***********");
        List<AccountInfoRpcVO> accountInfoRpcVOS = accountRpcService.queryAccountsByMobile(accountRpcDTO);
        log.info("result:{}", JSON.toJSON(accountInfoRpcVOS));
    }

    @Test
    void testLicense() {
//        MBLicenseDTO mbLicenseDTO = new MBLicenseDTO();
//        mbLicenseDTO.setAccountId(965006065499967242L);
//        mbLicenseDTO.setLicenseTypes(List.of(100,300));
//        MBLicenseVO mbLicenseVO = mbAccountService.queryLicenseByTypes(mbLicenseDTO);

        LicenseRpcDTO licenseRpcDTO = new LicenseRpcDTO();
        licenseRpcDTO.setAccountId(965006065499967242L);
        licenseRpcDTO.setLicenseTypes(List.of(300));
        VehicleLicenseRpcVO vehicleLicenseRpcVO = accountRpcService.queryLicenseByTypes(licenseRpcDTO);
        log.info("result:{}", JSON.toJSON(vehicleLicenseRpcVO));
    }

}
