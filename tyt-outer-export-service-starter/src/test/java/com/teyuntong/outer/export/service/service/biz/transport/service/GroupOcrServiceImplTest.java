package com.teyuntong.outer.export.service.service.biz.transport.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.ocr.service.OCRRpcService;
import com.teyuntong.outer.export.service.client.ocr.vo.BusinessLicenseOcrRpcVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupOcrService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.BusinessLicenseOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQualificationCertificateOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VehicleLicenseDeputyPageBackOcrVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/04/25 15:24
 */
@Slf4j
@Disabled
class GroupOcrServiceImplTest extends TestBase {

    @Autowired
    GroupOcrService groupOcrService;

    @Autowired
    OCRRpcService ocrRpcService;

    @Test
    void roadTransportBackOcr() {
        RoadTransportBackOcrVO result = groupOcrService.roadTransportBackOcr("https://boss.teyuntong.net/manage_new/root_pic//data/pictures/car/2405301618291979.jpeg");
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    void vehicleLicenseDeputyPageBackOcr() {
        VehicleLicenseDeputyPageBackOcrVO result = groupOcrService.vehicleLicenseDeputyPageBackOcr("https://boss.teyuntong.net/manage_new/root_pic//data/pictures/car/2404241640122954.png");
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    void roadTransportQualificationCertificateOcr() {
        RoadTransportQualificationCertificateOcrVO result = groupOcrService.roadTransportQualificationCertificateOcr("https://peimages.teyuntong.net/dispatch/APP/2024-4-29/b74ba69e8863f73f63d89afa81e8e34d.png");
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    void roadTransportQualificationCertificateBackOcr() {
        RoadTransportQualificationCertificateOcrVO result = groupOcrService.roadTransportQualificationCertificateBackOcr("https://peimages.teyuntong.net/dispatch/APP/2024-4-29/b74ba69e8863f73f63d89afa81e8e34d.png");
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    void businessLicenseOcr() {
        BusinessLicenseOcrRpcVO result = ocrRpcService.businessLicenseOcr("https://peimages.teyuntong.net/dispatch/APP/2025-5-16/b232c524a8cf4522f9333580a3790c31.png");
        log.info("result:{}", JSON.toJSONString(result));
    }
}