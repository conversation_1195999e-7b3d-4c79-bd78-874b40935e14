package com.teyuntong.outer.export.service.service.biz.transport.service;

import com.alibaba.fastjson.JSON;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.service.biz.manbang.bankcard.service.BankAccountService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.service.GroupOcrService;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportBackOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.RoadTransportQualificationCertificateOcrVO;
import com.teyuntong.outer.export.service.service.biz.manbang.ocr.vo.VehicleLicenseDeputyPageBackOcrVO;
import com.wlqq.wallet.gateway.client.response.mgs.BankAccountDetail;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/04/25 15:24
 */
@Slf4j
@Disabled
class GroupBankCardServiceImplTest extends TestBase {

    @Autowired
    BankAccountService bankAccountService;

    @Test
    void roadTransportBackOcr() {
        List<BankAccountDetail> result = bankAccountService.getGroupBankCardList(1000000591L);
        log.info("result:{}", JSON.toJSONString(result));
    }

}