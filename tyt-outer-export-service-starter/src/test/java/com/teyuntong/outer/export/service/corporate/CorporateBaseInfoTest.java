package com.teyuntong.outer.export.service.corporate;

import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.corporate.service.CorporateBaseInfoRpcService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/12/09 10:44
 */
@Disabled
public class CorporateBaseInfoTest extends TestBase {
    @Autowired
    CorporateBaseInfoRpcService rpcService;

    @Test
    void getCorporate() throws Exception {
        rpcService.getCorporate("北京邦利德网络科技有限公司");
    }

    @Test
    void getIcBasicInfoNormal() throws Exception {
        rpcService.getIcBasicInfoNormal("北京邦利德网络科技有限公司");
    }
}
