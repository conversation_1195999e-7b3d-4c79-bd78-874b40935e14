package com.teyuntong.outer.export.service.megvii;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.outer.export.service.TestBase;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.GetBizTokenV3Req;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.VerifyV3Req;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
class MegaiiFaceIdV3RpcServiceTest extends TestBase {


    @Test
    void testVerify() throws Exception {
        VerifyV3Req req = new VerifyV3Req();
        req.setBizToken("1733807985,27f8f6c8-43b5-4318-bacb-7c07350f4e05");
        req.setMegliveDataBase64("demo");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(req);

        mockMvc.perform(MockMvcRequestBuilders.post("/megaii/face_id/v3/verify")
                        .content(jsonRequest)
                        .contentType("application/json"))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
    }

    @Test
    void getBizToken() throws Exception {
        GetBizTokenV3Req req = new GetBizTokenV3Req();
        req.setIdCardName("刘刚");
        req.setIdCardNumber("");

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(req);

        mockMvc.perform(MockMvcRequestBuilders.post("/megaii/face_id/v3/get_biz_token")
                        .content(jsonRequest)
                        .contentType("application/json"))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
    }
}