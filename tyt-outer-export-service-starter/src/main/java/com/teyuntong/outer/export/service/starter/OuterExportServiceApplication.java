package com.teyuntong.outer.export.service.starter;

import com.teyuntong.infra.common.retrofit.core.EnableRetrofitClient;
import com.teyuntong.outer.export.service.client.constant.OuterExportConstant;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableScheduling
@EnableFeignClients(basePackages = {
        OuterExportConstant.REMOTE_BASE_PACKAGE
})
@EnableDiscoveryClient
@EnableAspectJAutoProxy(proxyTargetClass = true)
@MapperScan(basePackages = "com.teyuntong.outer.export.service", annotationClass = Mapper.class)
@SpringBootApplication(scanBasePackages = "com.teyuntong.outer.export.service")
@EnableRetrofitClient(basePackages = "com.teyuntong.outer.export.service")
@OpenAPIDefinition(info = @Info(title = "外部出口服务", version = "1.0", description = "外部出口服务文档"))
public class OuterExportServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OuterExportServiceApplication.class, args);
    }

}