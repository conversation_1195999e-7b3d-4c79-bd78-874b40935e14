server:
  port: 9002
  servlet:
    context-path: /outer-export
  tomcat:
    max-connections: 2000

management:
  endpoints:
    web:
      exposure:
        include: health

spring:
  profiles:
    active: local
  application:
    name: tyt-outer-export-service
  #上传文件大小设置
  servlet:
    multipart:
      enabled: true
      #上传单个文件大小限制
      max-file-size: 10MB
      #上传总文件大小限制
      max-request-size: 50MB
  jackson:
    defaultPropertyInclusion: non_null
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: true
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  cache:
    redis:
      cacheNullValues: true
      # 默认缓存失效时间, 30m
      timeToLive: 30m
  cloud:
    loadbalancer:
      enabled: true
      retry:
        enabled: true
        avoid-previous-instance: true

feign:
  metrics:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    connection-timeout: 10000
    ok-http:
      read-timeout: 60000
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: false
    alphanumeric-ids:
      # 此项为 false 时, resilience4j 的 instances，key的命名规则为 <feignClientClassName>#<calledMethod>(<parameterTypes>)
      # e.g.
      #      resilience4j.timelimiter:
      #        instances:
      #          CircuitBreakerTestService#getBinWithDelayInSeconds(int):
      #            timeoutDuration: 3s
      # 此项为 true 时, resilience4j 的 instance name 会去掉非数字和字母，相当于name=name.replaceAll("[^a-zA-Z0-9]", "")
      # e.g.
      #      resilience4j.timelimiter:
      #        instances:
      #          CircuitBreakerTestServicegetBinWithDelayInSecondsint:
      #            timeoutDuration: 3s
      enabled: true

springdoc:
  swagger-ui:
    # 访问 /swagger-ui/index.html 可以访问 swagger, 生产环境需要关闭
    enabled: true
  auto-tag-classes: false
  default-produces-media-type: application/json
  api-docs:
    # 访问 /v3/api-docs 可以获取api定义, 生产环境需要关闭
    enabled: true
  packages-to-exclude:
    # 排除 openfeign 接口, 包括对外提供的和引用外部的, 注意修改包名
    - com.teyuntong.scaffold.client
    - com.teyuntong.scaffold.service

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
  mapper-locations: classpath:mapper/*.xml


logging:
  # config: http://mse-96d0cfa2-p.nacos-ans.mse.aliyuncs.com:8848/nacos/v1/cs/configs?group=DEFAULT_GROUP&username=tyt&password=tyt&dataId=logback-spring-skywalking.xml
  config: classpath:logback-spring-bak.xml
  level:
    root: info
    # 通过此项更改 com.teyuntong 包下的日志级别
    com.teyuntong: debug
#  sls:
#    configs:
#      tmp-store:
#        enable: true
#        access-key-id: LTAI5tJiQ78PL5Jz77yzRCKU
#        access-key-secret: ******************************
#        endpoint: cn-beijing.log.aliyuncs.com
#        project: tmp-project
#        log-store: tmp-logstore

---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: 60b84c10-ce6f-401b-8452-514f593cb317
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
        group: ${os.name}___${java.library.path}___${java.home}___${java.runtime.version}___${user.name}
      config:
        server-addr: http://mse-96d0cfa2-p.nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: 60b84c10-ce6f-401b-8452-514f593cb317
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
        # 会在配置中心寻找 dataId 为 tyt-outer-export-service.yaml 的配置
        name: ${spring.application.name}
        extension-configs:
          - dataId: foo.yaml
            group: DEFAULT_GROUP
            refresh: true
---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: e7839125-3aa2-4a32-a927-5f01d7581a00
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: e7839125-3aa2-4a32-a927-5f01d7581a00
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-outer-export-service.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: a0786143-63f0-4a01-aaf7-f2fe06eddcfe
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: a0786143-63f0-4a01-aaf7-f2fe06eddcfe
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-outer-export-service.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: release
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: bf9db828-b029-4436-a35d-9321e77f05bc
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: bf9db828-b029-4436-a35d-9321e77f05bc
        access-key: LTAI5tHGFX6zobh5oYER28ZC
        secret-key: ******************************
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-outer-export-service.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      discovery:
        server-addr: https://mse-050b1f62-nacos-ans.mse.aliyuncs.com
        namespace: b73c3f26-ba58-4348-a1a8-5f94015ae9ec
        access-key: LTAI5tJaEsHG9wAToMPLkQgs
        secret-key: ******************************
      config:
        server-addr: https://mse-050b1f62-nacos-ans.mse.aliyuncs.com
        file-extension: yaml
        namespace: b73c3f26-ba58-4348-a1a8-5f94015ae9ec
        access-key: LTAI5tJaEsHG9wAToMPLkQgs
        secret-key: ******************************
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-outer-export-service.yaml 的配置
        name: ${spring.application.name}
logging:
  level:
    root: info
    # 通过此项更改 com.teyuntong 包下的日志级别
    com.teyuntong: info

springdoc:
  swagger-ui:
    # 访问 /swagger-ui/index.html 可以访问 swagger, 生产环境需要关闭
    enabled: false
  api-docs:
    # 访问 /v3/api-docs 可以获取api定义, 生产环境需要关闭
    enabled: false