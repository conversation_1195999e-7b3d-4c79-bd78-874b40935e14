custom:
  feign:
    decoder:
      log-business-exception: true
      throw-business-exception: true

resilience4j.circuitbreaker:
  configs:
    default:
      minimumNumberOfCalls: 5
      sliding-window-size: 60
      sliding-window-type: count_based
      failureRateThreshold: 50

resilience4j.timelimiter:
  configs:
    default:
      timeoutDuration: 2s

retrofit:
  circuitbreaker:
    resilience4j:
      enable: false
