package com.teyuntong.outer.export.service.adapter.enterprise;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.enterprise.vo.QueryEnterpriseBalanceApiReq;
import com.teyuntong.outer.export.service.client.enterprise.vo.QueryEnterpriseBalanceApiResp;
import com.teyuntong.outer.export.service.service.biz.manbang.enterprise.service.EnterpriseAccountService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/06/06 16:00
 */
@Tag(name = "企业开户接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/enterprise")
public class EnterpriseAccountController {

    private final EnterpriseAccountService enterpriseAccountService;

    
    /**
     *  获取企业余额信息
     * <AUTHOR>
     * @param uid 平台企业会员的公司id(此处表示企业统一社会信用码)
     * @return WebResult<TransportMainAppVO>
     */
    @GetMapping("/queryEnterpriseBalance/{uid}")
    public WebResult<QueryEnterpriseBalanceApiResp> queryEnterpriseBalance(@PathVariable String uid) {
        QueryEnterpriseBalanceApiReq req= new QueryEnterpriseBalanceApiReq();
        req.setUid(uid);
        QueryEnterpriseBalanceApiResp enterpriseBalanceApiResp = enterpriseAccountService.queryEnterpriseBalance(req);
        return WebResult.success(enterpriseBalanceApiResp);
    }
}
