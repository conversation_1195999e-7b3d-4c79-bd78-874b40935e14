package com.teyuntong.outer.export.service.adapter.transport;

import com.teyuntong.outer.export.service.adapter.transport.app.TransportMainAppVO;
import com.teyuntong.outer.export.service.service.biz.transport.pojo.TransportMainDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 * @since 2023/10/24 16:02
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class TransportAdapterConvert {

    public abstract TransportMainAppVO convertDTO2VO(TransportMainDTO transportMainDTO);
}
