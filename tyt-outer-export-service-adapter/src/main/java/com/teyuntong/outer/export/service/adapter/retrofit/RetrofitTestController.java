package com.teyuntong.outer.export.service.adapter.retrofit;

import com.teyuntong.infra.common.definition.bean.WebResult;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.retry.annotation.Retry;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/10 16:15
 */
@Tag(name = "retrofit测试接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/retrofit")
@Slf4j
public class RetrofitTestController {

    private final BaiDuRetrofitSupplier.BaiduRetrofit baiduRetrofit;
    private final TimeoutSupplier.TimeoutRetrofit timeoutRetrofit;
    private final RetrySupplier.RetryRetrofit retryRetrofit;
    private final RateLimitSupplier.RateLimitRetrofit rateLimitRetrofit;
    private final FallbackSupplier.FallbackRetrofit fallbackRetrofit;

    /**
     * 通过 retrofit 访问百度
     *
     * @return 百度响应结果
     */
    @RequestMapping(value = "/baidu", method = {RequestMethod.GET, RequestMethod.POST})
    public WebResult<String> baidu(HttpServletRequest request) throws IOException {
        log.info("request to baidu");
        String string = baiduRetrofit.get().execute().body();
        return WebResult.success(string);
    }

    /**
     * 测试 retrofit 超时
     *
     * @param delay 等待时间, 单位秒
     */
    @GetMapping("/timeout/{delay}")
    public WebResult<Map<String, Object>> timeout(@PathVariable("delay") int delay) throws IOException {
        Map<String, Object> body = timeoutRetrofit.getBinWithDelayInSeconds(delay).execute().body();
        return WebResult.success(body);
    }

    /**
     * 测试 retrofit 限流
     */
    @GetMapping("/rateLimiter")
    @RateLimiter(name = "timeoutTestGroup")
    public WebResult<String> rateLimiter() throws IOException {
        String body = rateLimitRetrofit.get().execute().body();
        return WebResult.success(body);
    }

    /**
     * 测试 retrofit 重试
     */
    @GetMapping("/retry")
    @Retry(name = "timeoutTestGroup")
    public WebResult<Map<String, Object>> retry() throws IOException {
        // @retry 不要用在 Retrofit 的接口上面，因为接口方法只是构建了一个 Call 对象, 只有调用 Call 对象的 execute 才会发送 http 请求
        // 异步执行请求(enqueue) 使用 @Retry 无效, 只能使用自定义的 RetryOnFailureInterceptor 拦截器进行重试
        Map<String, Object> body = retryRetrofit.retry().execute().body();
        return WebResult.success(body);
    }

    /**
     * 测试 retrofit fallback
     */
    @GetMapping("/fallback")
    public WebResult<String> fallback() throws IOException {
        String body = fallbackRetrofit.fallback().execute().body();
        return WebResult.success(body);
    }

    /**
     * 测试 retrofit fallback
     */
    @PostMapping("/fallback1")
    public WebResult<String> fallback1(@RequestBody Object requestBody) throws IOException {
        String body = fallbackRetrofit.fallback1(requestBody).execute().body();
        return WebResult.success(body);
    }

    /**
     * 测试 retrofit fallback
     */
    @PostMapping("/fallback2")
    public WebResult<String> fallback2(@RequestBody Object requestBody) throws IOException {
        String body = fallbackRetrofit.fallback2(requestBody, Map.of("foo", "bar")).execute().body();
        return WebResult.success(body);
    }
}
