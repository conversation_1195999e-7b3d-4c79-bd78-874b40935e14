package com.teyuntong.outer.export.service.adapter.retrofit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.circuitbreaker.RetrofitFallbackFactory;
import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import com.teyuntong.infra.common.retrofit.utils.RetrofitUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.http.GET;
import retrofit2.http.Path;

import java.time.Duration;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class TimeoutSupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;

    @Override
    public Retrofit getRetrofit() {

        OkHttpClient.Builder okHttpCLientBuilder = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(5000))
                .readTimeout(Duration.ofMillis(5000));

        return new Retrofit.Builder()
                .baseUrl("https://httpbin.org/")
                .client(okHttpCLientBuilder.build())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .build();
    }

    @RetrofitClient(value = TimeoutSupplier.class, fallbackFactory = TimeoutRetrofitFallbackFactory.class)
    interface TimeoutRetrofit {

        @GET(value = "/delay/{delayInSeconds}")
        Call<Map<String, Object>> getBinWithDelayInSeconds(@Path("delayInSeconds") int delayInSeconds);
    }

    @Component
    static class TimeoutRetrofitFallback implements TimeoutRetrofit {

        @Override
        public Call<Map<String, Object>> getBinWithDelayInSeconds(int delayInSeconds) {
            return RetrofitUtil.createFallbackCall(Map.of("fall", "back"));
        }
    }

    @Slf4j
    @Component
    static class TimeoutRetrofitFallbackFactory implements RetrofitFallbackFactory<TimeoutRetrofit> {

        @Override
        public TimeoutRetrofit create(Throwable t) {
            return delay -> {
                log.error("fallback", t);
                return RetrofitUtil.createFallbackCall(Map.of("fallback", "timeout"));
            };
        }
    }
}
