package com.teyuntong.outer.export.service.adapter.retrofit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.circuitbreaker.RetrofitFallbackFactory;
import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import com.teyuntong.infra.common.retrofit.utils.RetrofitUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.time.Duration;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class FallbackSupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;

    @Override
    public Retrofit getRetrofit() {

        OkHttpClient.Builder okHttpCLientBuilder = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(5000))
                .readTimeout(Duration.ofMillis(5000));

        return new Retrofit.Builder()
                .baseUrl("http://127.0.0.1:11223/")
                .client(okHttpCLientBuilder.build())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .build();
    }

    @RetrofitClient(value = FallbackSupplier.class, fallback =
            FallbackRetrofitImpl.class)
    @UseInterceptor(LogInfoInterceptor.class)
    interface FallbackRetrofit {

        @GET(value = "/fallback")
        Call<String> fallback();

        @POST(value = "/fallback1")
        Call<String> fallback1(@Body Object requestBody);

        @POST(value = "/fallback2")
        Call<String> fallback2(@Body Object requestBody, @HeaderMap Map<String, String> header);
    }

    @Component
    static class FallbackRetrofitImpl implements FallbackRetrofit {

        @Override
        public Call<String> fallback() {
            return RetrofitUtil.createFallbackCall("fallback");
        }

        @Override
        public Call<String> fallback1(Object requestBody) {
            return RetrofitUtil.createFallbackCall("fallback1");
        }

        @Override
        public Call<String> fallback2(Object requestBody, Map<String, String> header) {
            return RetrofitUtil.createFallbackCall("fallback2");
        }
    }

    @Component
    @Slf4j
    static class FallbackRetrofitFactory implements RetrofitFallbackFactory<FallbackRetrofit> {

        @Override
        public FallbackRetrofit create(Throwable t) {
            log.error("fallback", t);
            return new FallbackRetrofitImpl();
        }
    }
}
