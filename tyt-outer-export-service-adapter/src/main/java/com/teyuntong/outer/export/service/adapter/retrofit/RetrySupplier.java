package com.teyuntong.outer.export.service.adapter.retrofit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.http.GET;

import java.time.Duration;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class RetrySupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;

    @Override
    public Retrofit getRetrofit() {

        OkHttpClient.Builder okHttpCLientBuilder = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(5000))
                .readTimeout(Duration.ofMillis(5000));

        return new Retrofit.Builder()
                .baseUrl("http://127.0.0.1:11223/")
                .client(okHttpCLientBuilder.build())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .build();
    }

    @RetrofitClient(value = RetrySupplier.class)
    interface RetryRetrofit {

        @GET(value = "/retry")
        Call<Map<String, Object>> retry();
    }
}
