package com.teyuntong.outer.export.service.adapter.retrofit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.interceptor.UseInterceptor;
import com.teyuntong.infra.common.retrofit.suppplier.OkHttpClientSupplier;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;
import retrofit2.http.GET;

import java.time.Duration;

@Component
@RequiredArgsConstructor
public class BaiDuRetrofitSupplier implements RetrofitSupplier, OkHttpClientSupplier {

    private final ObjectMapper mapper;

    @Override
    public Retrofit getRetrofit() {

        return new Retrofit.Builder()
                .baseUrl("https:baidu.com/")
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .build();
    }

    @Override
    public OkHttpClient getOkHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(2999))
                .readTimeout(Duration.ofMillis(2655)).build();
    }

    @RetrofitClient(value = BaiDuRetrofitSupplier.class, okhttpClient = BaiDuRetrofitSupplier.class)
    @UseInterceptor(LogInfoInterceptor.class)
    interface BaiduRetrofit {

        @GET(value = "/")
        Call<String> get();
    }
}
