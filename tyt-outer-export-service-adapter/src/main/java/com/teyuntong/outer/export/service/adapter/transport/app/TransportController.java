package com.teyuntong.outer.export.service.adapter.transport.app;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.adapter.transport.TransportAdapterConvert;
import com.teyuntong.outer.export.service.service.biz.transport.pojo.TransportMainDTO;
import com.teyuntong.outer.export.service.service.biz.transport.service.TransportMainService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/10/24 15:58
 */
@Tag(name = "货源接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/transport")
public class TransportController {

    private final TransportMainService transportMainService;
    private final TransportAdapterConvert transportAdapterConvert;

    /**
     * 查询货源
     *
     * @param id 货源id
     * @return 货源信息
     */
    @GetMapping("/{id}")
    public WebResult<TransportMainAppVO> getById(@PathVariable Long id) {
        TransportMainDTO dto = transportMainService.getById(id);
        return WebResult.success(transportAdapterConvert.convertDTO2VO(dto));
    }
}
