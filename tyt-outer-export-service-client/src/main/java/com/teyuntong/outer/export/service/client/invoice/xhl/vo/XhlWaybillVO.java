package com.teyuntong.outer.export.service.client.invoice.xhl.vo;

import lombok.Data;

/**
 * 翔和翎 运单实体类
 *
 * <AUTHOR>
 * @since 2025-2-22 16:43:19
 */
@Data
public class XhlWaybillVO {

    /**
     * 三方运单号
     */
    private String tpWaybillNo;

    /**
     * 运单号
     */
    private String sendTruckNo;

    /**
     * 审核日志
     */
    private String checkLog;

    /**
     * 运费
     */
    private String freight;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 服务费状态，1:未支付，2:支付中，3:支付失败，4:支付成功
     */
    private String serviceChargeStatus;

    /**
     * 发票代码
     */
    private String invoice_code;

    /**
     * 发票发车重量合计
     */
    private String actual_weight;

    /**
     * 运单审核状态，审核状态，0:未审核，1:通过，2:不通过
     */
    private String checkStatus;

    /**
     * 税率，例如9，就是9%
     */
    private String rate;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 发票号码
     */
    private String invoice_no;

    /**
     * 开票时间，时间戳格式
     */
    private String invoice_time;

    /**
     * 发票金额
     */
    private String money_amount;

    /**
     * 发票价税合计
     */
    private String total_money;

    /**
     * 发票签收重量合计
     */
    private String sign_weight;

    /**
     * 发票状态，1:未开票，2:已开票
     */
    private String invoice_status;
}
