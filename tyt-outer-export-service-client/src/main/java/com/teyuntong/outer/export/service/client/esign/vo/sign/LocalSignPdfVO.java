package com.teyuntong.outer.export.service.client.esign.vo.sign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocalSignPdfVO {
    /**
     * 客户自由标示位，用于辨识文件
     */
    private String markBit;
    /**
     * 签署记录id
     */
    private String signServiceId;
    /**
     * 完成签署的文档 base64 数据
     */
    private String pdfBase64;
}
