package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票开票信息
 *
 * <AUTHOR>
 * @since 2024/08/14 19:40
 */
@Data
public class InvoiceResponse implements Serializable {

    /**
     * 发票单据号.
     */
    private String invoiceNumber;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 发票状态描述.
     */
    private String invoiceStatusDesc;

    /**
     * 开票时间	yyyy-MM-dd HH:mm:ss.
     */
    private String invoiceDateTime;

    /**
     * 快递号
     */
    private String trackingNo;

    /**
     * userCode
     */
    private String userCode;

    /**
     * 需要开票的公司名称
     */
    private String receiveInvoiceName;

    /**
     * 结算主体企业名称
     */
    private String bilingInvoiceName;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 税额
     */
    private String taxAmount;

    /**
     * 开票金额
     */
    private String invoiceAmount;

    /**
     * 开票种类
     */
    private String invoiceType;

    /**
     * 开票种类描述
     */
    private String invoiceTypeDesc;

    /**
     *发票备注
     */
    private String invoiceRemark;


    private String sellerName;


    private String sellerTaxpayerIdentificationNumber;


    private String sellerAddress;


    private String sellerTelephone;


    private String sellerBankName;


    private String sellerBankAccount;


    private String buyerName;


    private String buyerTaxpayerIdentificationNumber;


    private String buyerAddress;


    private String buyerTelephone;


    private String buyerBankName;


    private String buyerBankAccount;


    private String mailingAddress;


    private String mailingEmail;


    private String mailingName;


    private String mailingPhone;


    private String invoiceKind;


    private String invoiceKindDesc;

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    private String applyTime;


    private String trackingName;

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    private String trackingTime;


    private String taxNumber;


    private String taxCode;
}
