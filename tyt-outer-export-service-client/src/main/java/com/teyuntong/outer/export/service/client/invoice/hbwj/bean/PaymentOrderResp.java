package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentOrderResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 结算主体号码
     */
    private String settlementNo;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 增值服务费开票状态 1-未申请，2-申请中，3-已开票
     */
    private Integer serviceChargeInvoiceStatus;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 车船号
     */
    private String travelNum;

    /**
     * 申请金额（元）
     */
    private BigDecimal appliedAmount;

    /**
     * 支付金额（元）
     */
    private BigDecimal paymentAmount;

    /**
     * 司机应收金额（元）
     */
    private BigDecimal driverReceiveAmount;

    /**
     * 收款人银行
     */
    private String payeeBankName;

    /**
     * 收款人银行卡号、钱包账号
     */
    private String payeeAccountNo;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 收款人手机号
     */
    private String payeePhone;

    /**
     * 收款人身份证号
     */
    private String payeeIdCardNo;

    /**
     * 支付状态: WAIT_DRIVER_CONFIRM( "待司机确认"), APPLY_AUDITING( "申请审核中"), WAIT_PAY( "待支付"),
     * CONFIRM_AUDITING( "支付确认审核中"), PAYING( "支付中"), PAY_FAIL( "支付失败"), HAS_RECEIVED( "已到账"),
     * REFUSE( "已拒绝"), HAS_INVALID( "已作废")
     */
    private String paymentStatus;

    /**
     * 支付失败或者拒绝原因
     */
    private String failureReason;

    /**
     * 申请人名称
     */
    private String applyName;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核时间（人工）
     */
    private Date auditTime;

    /**
     * 支付确认审核时间（人工）
     */
    private Date confirmAuditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间（系统）
     */
    private Date auditTimeSys;

    /**
     * 支付确认审核时间（系统）
     */
    private Date confirmAuditTimeSys;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 作废时间
     */
    private Date refuseTime;

    /**
     * 到账时间
     */
    private Date payArriveTime;

    /**
     * 批次支付单号
     */
    private String batchPaymentNo;

    /**
     * 银行回单地址
     */
    private String receiptPictureUrl;

}
