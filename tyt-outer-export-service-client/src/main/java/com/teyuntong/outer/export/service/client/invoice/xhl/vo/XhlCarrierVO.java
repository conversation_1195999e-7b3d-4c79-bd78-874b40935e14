package com.teyuntong.outer.export.service.client.invoice.xhl.vo;

import lombok.Data;

/**
 * 托运人实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlCarrierVO {


    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 法人姓名
     */
    private String person;

    /**
     * 法人电话
     */
    private String personPhone;

    /**
     * 法人身份证号
     */
    private String idCardNo;

    /**
     * 统一社会代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 公司地址城市编码
     */
    private String areaCode;

    /**
     * 营业执照照片
     */
    private String businessCertificatePhotoUrl;

    /**
     * 法人身份证正面照片地址
     */
    private String idCardNoFrontUrl;

    /**
     * 法人身份证反面照片地址
     */
    private String idCardNoBackUrl;

    /**
     * 开户许可证照片地址
     */
    private String permitPhotoUrl;

    /**
     * 客户点数，传百分比，例如5%，传入0.05即可
     */
    private String rate;

    /**
     * 1:不含税（硬点），2:含税（软点）
     */
    private String rateType;

    /**
     * 营业执照有效期开始，yyyy-MM-dd格式
     */
    private String effectiveDate;

    /**
     * 营业执照有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    private String expiryDate;

    /**
     * 开票信息电话
     */
    private String kpCustomerPhone;

    /**
     * 开票信息地址
     */
    private String kpCustomerAddress;

    /**
     * 开票信息开户行
     */
    private String kpBankName;

    /**
     * 开票信息银行账号
     */
    private String kpBankAccountNo;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String createTime;


    /**
     * 虚户会员id
     */
    private String njUserId;

    /**
     * 虚户账号
     */
    private String njAcctNo;

    /**
     * 余额，单位元
     */
    private String balance;

    /**
     * 冻结金额，单位元
     */
    private String ctrlBalance;

    /**
     * 卡号
     */
    private String bankNo;

    /**
     * 银行名称
     */
    private String bankTypeName;

    /**
     * 开户行
     */
    private String bankBranchName;
}
