package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/19 14:50
 */
@Data
public class UploadFileInfoDto implements Serializable {

    /**
     * 文件名称 带后缀 如 图片.jpg
     */
    private String fileName;

    /**
     * 文件图片url 与 fileDataByte 二选一即可
     */
    private byte[] fileDataByte;

    /**
     * 文件图片url 与 fileDataByte 二选一即可
     * 附件类型
     * (See: 附件类型
     * DELIVERY_RECEIPT 回单
     * LOADING_WEIGHT_RECEIPT 装货磅单
     * UNLOADING_WEIGHT_RECEIPT 卸货磅单
     * LOADING_WEIGHT_IMG 装货照片
     * UNLOADING_WEIGHT_IMG 卸货照片
     * EXCEPTION_PHOTO 异常图片
     * DRIVER_EVALUATION_ATTACHMENT 司机评价附件
     * SHIPPER_EVALUATION_ATTACHMENT 货主评价附件
     * INSURANCE_POLICY 保险单附件)
     */
    private String fileUrl;

}
