package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.*;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 运单详情响应
 *
 * <AUTHOR>
 * @since 2024/12/13 10:23
 */
@Data
public class WaybillThreeOffsetResp {

    /**
     * 委托方名称（客户名称，受票方）
     */
    private String ownerName;

    /**
     * 委托方签好运账户编码
     */
    private String ownerUserCode;

    /**
     * 订单或计划单编号
     */
    private String orderCode;

    /**
     * 运单编号
     */
    private String waybillCode;


    /**
     * 运单状态：WAIT_TAKE_ORDER待接单 WAIT_DEPARTURE待起运 WAIT_ARRIVE运输中 HAS_FINISH已送达 HAS_CANCE
     */
    private WayBillStatusEnum waybillStatus;



}
