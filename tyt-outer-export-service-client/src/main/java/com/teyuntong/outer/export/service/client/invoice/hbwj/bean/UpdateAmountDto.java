package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ModifyAmountType;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateAmountDto implements Serializable {
        /**
         * 运费调整类型
         * (See: 运费调整类型
         * REPAIR 增补
         * DEDUCT 扣减)
         */
        private ModifyAmountType modifyAmountType;

        /**
         * 调整金额（元）
         */
        private Integer modifyAmount;

        private String modifyReason;

}
