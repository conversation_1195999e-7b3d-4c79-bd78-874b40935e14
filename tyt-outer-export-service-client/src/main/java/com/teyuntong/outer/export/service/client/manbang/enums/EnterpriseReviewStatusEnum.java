package com.teyuntong.outer.export.service.client.manbang.enums;

import lombok.Getter;

/**
 * 集团企业实名审核状态.
 *
 * <AUTHOR>
 * @date 2024-5-13 16:19:37
 */
public enum EnterpriseReviewStatusEnum {
    //waitUpload / init / checkPass / checkReject
    WAIT_UPLOAD("waitUpload", "未提交"),
    INIT("init", "审核中"),
    CHECK_PASS("checkPass", "审核通过"),
    CHECK_REJECT("checkReject", "审核驳回"),
    ;

    @Getter
    private final String code;

    @Getter
    private final String zhName;

    EnterpriseReviewStatusEnum(String code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(String reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}