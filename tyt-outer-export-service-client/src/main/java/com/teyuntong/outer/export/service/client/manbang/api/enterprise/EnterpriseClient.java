package com.teyuntong.outer.export.service.client.manbang.api.enterprise;

import com.teyuntong.outer.export.service.client.constant.OuterExportConstant;
import com.teyuntong.outer.export.service.client.enterprise.vo.*;
import com.teyuntong.outer.export.service.client.fallback.CommonExceptionFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 【满帮】企业相关接口
 *
 * <AUTHOR>
 * @since 2024/4/2 19:01
 */
@Primary
@FeignClient(name = OuterExportConstant.SERVICE_PROVIDER + "/enterprise",
        fallbackFactory = CommonExceptionFallbackFactory.class)
public interface EnterpriseClient {

    /**
     * 【满帮】创建企业用户
     *
     * @param req
     * @return WebResult<CreateEnterpriseApiResp>
     */
    @PostMapping("/createEnterprise")
    CreateEnterpriseApiResp createEnterprise(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 【满帮】企业用户开户
     *
     * @param req
     * @return WebResult<CreateEnterpriseApiResp>
     */
    @PostMapping("/enterpriseOpenAccount")
    Boolean enterpriseOpenAccount(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 【满帮】企业网商开户
     *
     * @param req
     * @return WebResult<CreateEnterpriseApiResp>
     */
    @PostMapping("/enterpriseOpenNetAccount")
    EnterpriseOpenNetAccountApiResp enterpriseOpenNetAccount(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 【满帮】查询网商开户信息
     *
     * @param req
     * @return WebResult<CreateEnterpriseApiResp>
     */
    @PostMapping("/queryEnterpriseNetAccountInfo")
    CreateEnterpriseApiResp queryEnterpriseNetAccountInfo(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 【满帮】查看企业用户账户余额
     *
     * @param req
     * @return WebResult<QueryEnterpriseBalanceApiResp>
     */
    @PostMapping("/queryEnterpriseBalance")
    QueryEnterpriseBalanceApiResp queryEnterpriseBalance(@RequestBody @Validated QueryEnterpriseBalanceApiReq req);


}
