/**
 * ├─client                              -- client模块, 负责提供服务间调用的约束, 供其他服务引用
 * │  ├─api                               -- 为其他服务调用提供的api
 * │  ├─dto                               -- 为其他服务调用提供的dto
 * <p>
 * <p>
 * 由于远程调用使用openfeign，所以此包提供的接口，本项目中接口的实现，调用项目中怎样使用此接口有固定约束:
 * <p>
 * <p>
 * 此项目中提供的api接口，需要由spring-web包中的{@link org.springframework.web.bind.annotation.RequestMapping}及其衍生注解
 * {@link org.springframework.web.bind.annotation.GetMapping}等标记，由接口提供方限制调用的 url 等相关参数，代码如下
 *
 * <pre> {@code
 *
 * @RequestMapping("/demo")
 * public interface UserRpcService {
 *
 *     @GetMapping("/user")
 *     UserDTO getUser();
 * }
 *
 * }</pre>
 * <p>
 * 本项目内需要实现 api 对外提供的接口(通常在service的rpc包中实现), 且实现类仅需由@RestController标注(不要覆盖@RequestMapping，否则调用方无法正常调用接口)作为 http
 * 的服务提供方，代码如下
 * <pre> {@code
 *
 * @RestController
 * public class UserRpcServiceImpl implements UserRpcService {
 *
 *     @Override
 *     public UserDTO getUser() {
 *         return new UserDTO();
 *     }
 * }
 *
 * }</pre>
 * <p>
 * 调用方需要继承 api 对外提供的接口，并使用 @FeignClient 标注，代码如下
 * <pre> {@code
 *
 * @FeignClient(name = "tyt-user", contextId = "UserRemoteService", fallback =
 *         UserRemoteService.UserRemoteFallBackService.class)
 * public interface UserRemoteService extends UserRpcService {
 *
 *     @Component
 *     class UserRemoteFallBackService implements UserRpcService {
 *         @Override
 *         public UserRpcDTO getUser(Long id) {
 *             throw new NoFallbackAvailableException("Boom!", new RuntimeException());
 *         }
 *     }
 * }
 *
 * }</pre>
 * <p>
 * 这样做的好处是，只在 client 包内维护路由信息, 对服务调用者和服务提供者，同时屏蔽了 @RequestMapping 中的路由和其他细节，
 * 既能避免服务间由路由定义产生的不必要的麻烦，又能使双方同时专注于业务
 */

package com.teyuntong.outer.export.service.client;