package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**
 * 创建企业认证信息DO
 *
 * <AUTHOR>
 * @since 2024-7-17 13:17:09
 */
@Data
public class CreateCompanyRequest {

    /**
     * OWNER(10, "委托方"),
     * CARRIER(20, "承运方"),
     */
    private String[] identityIds = new String[] {"OWNER"};
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 统一社会信用代码
     */
    private String unifiedCreditcCode;
    /**
     * 企业管理员
     */
    private String adminName;
    /**
     * 管理员手机号
     */
    private String adminPhone;
    /**
     * 结算主体编号
     */
    private Long settlementPrincipalId;
    /**
     * 营业执照URL
     */
    private String businessLicenseUrl;
    /**
     * 营业期限起
     */
    private String tradeTimeStart;
    /**
     * 营业期限止
     */
    private String tradeTimeEnd;
    /**
     * 财务联系人
     */
    private String financeContact;
    /**
     * 财务手机号
     */
    private String financeContactTel;
    /**
     * 业务联系人
     */
    private String businessContact;
    /**
     * 业务员手机号
     */
    private String businessContactTel;
}
