package com.teyuntong.outer.export.service.client.common.old.enums;

/**
 * Created by duanwc on 2019/12/26.
 *
 * 操作状态返回码
 */
public enum ReturnCodeEnum {

    CODE_200(200, "操作成功"),
    CODE_201(201, "请进行短信验证"),
    CODE_500(500, "服务器错误"),
    CODE_400(400, "输入有误，请更正重试"),
    CODE_401(401, "不在白名单"),
    CODE_402(402, "基础参数错误"),
    CODE_405(405, "输入有误，请更正重试"),
    CODE_408(408, "网络繁忙，请稍候重试"),
    CODE_433(433, "无效签名");


    private int code;
    private String msg;

    ReturnCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
