package com.teyuntong.outer.export.service.client.account.service;

import com.teyuntong.outer.export.service.client.account.dto.AccountRpcDTO;
import com.teyuntong.outer.export.service.client.account.dto.LicenseRpcDTO;
import com.teyuntong.outer.export.service.client.account.vo.AccountInfoRpcVO;
import com.teyuntong.outer.export.service.client.account.vo.VehicleLicenseRpcVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/15 14:36
 */

public interface AccountRpcService {


    @PostMapping("/rpc/mb/queryAccountsByMobile")
    List<AccountInfoRpcVO> queryAccountsByMobile(@RequestBody AccountRpcDTO accountRpcDTO);


    @PostMapping("/rpc/mb/queryLicenseByTypes")
    VehicleLicenseRpcVO queryLicenseByTypes(@RequestBody LicenseRpcDTO licenseRpcDTO);
}
