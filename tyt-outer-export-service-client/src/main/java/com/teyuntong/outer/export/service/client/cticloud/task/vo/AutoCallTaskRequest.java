package com.teyuntong.outer.export.service.client.cticloud.task.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18 10:38
 */
@Data
public class AutoCallTaskRequest {

    /**
     * 电话内容
     */
    @JsonProperty("taskCallValue")
    private String taskCallValue;

    /**
     * 任务名称
     */
    @JsonProperty("taskName")
    private String taskName;

    /**
     * 手机号List
     */
    @JsonProperty("callTelList")
    private List<String> callTelList;

    /**
     * 是否限制时间段，默认限制
     */
    @JsonProperty("limitTime")
    private Boolean limitTime = true;

}
