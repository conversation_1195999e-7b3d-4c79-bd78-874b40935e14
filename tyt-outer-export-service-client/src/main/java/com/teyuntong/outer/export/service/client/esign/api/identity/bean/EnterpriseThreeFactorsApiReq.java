package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/02/28 14:25
 */
@Data
public class EnterpriseThreeFactorsApiReq {

    /**
     * 组织机构证件（如营业执照）上的组织机构名称
     * 是否必填：是
     */
    @NotBlank
    private String name;

    /**
     * 组织机构证件号
     * 支持统一社会信用代码号和工商注册号（部分个体工商户）
     */
    @NotBlank
    private String orgCode;

    /**
     * 法定代表人名称
     */
    @NotBlank
    private String legalRepName;

    /**
     * 自定义业务标识，，将在异步通知及跳转时携带返回对接方
     */
    private String contextId;

    /**
     * 认证结束后异步通知地址,具体见 异步通知 章节说明
     */
    private String notifyUrl;

}
