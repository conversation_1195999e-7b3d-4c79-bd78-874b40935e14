package com.teyuntong.outer.export.service.client.volcengine.service;

import com.teyuntong.outer.export.service.client.volcengine.vo.IdCardTwoElementVerifyDTO;
import com.teyuntong.outer.export.service.client.volcengine.vo.IdCardTwoElementVerifyVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 火山引擎身份要素认证-外部调用
 *
 * <AUTHOR>
 * @since 2024/12/05 21:32
 */
public interface BusinessSecurityRpcService {


    /**
     * 用户信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/volcengine/business_security/id_card/two_element_verify")
    IdCardTwoElementVerifyVO verifyTwoElement(@RequestBody @Validated IdCardTwoElementVerifyDTO dto);
}
