package com.teyuntong.outer.export.service.client.cticloud.axb.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/14 上午10:46
 */
@Data
public class AxbDeleteReq implements Serializable {

    /**
     * 要删除的数据id，仅支持传[ids]或者传[bizType、bizId]删除，三个参数都存在默认按ids删除
     */
    private List<Long> ids;

    /**
     * 要删除的数据的三方ID
     */
    private List<String> thirdPartyIds;
    /**
     * 要删除的业务类型，仅支持传[ids]或者传[bizType、bizId]删除，三个参数都存在默认按ids删除
     */
    private Integer bizType;

    /**
     * 要删除的业务id，仅支持传[ids]或者传[bizType、bizId]删除，三个参数都存在默认按ids删除
     */
    private Long bizId;
}
