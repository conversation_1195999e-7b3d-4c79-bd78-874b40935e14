package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/17 13:39
 */
@Data
public class HbwjDriverReport {

    /**
     *司机姓名
     */
    private String driverName;

    /**
     *身份证号
     */
    private String idCardNo;

    /**
     *手机号
     */
    private String phone;

    /**
     *驾驶证号
     */
    private String drivingNo;

    /**
     *准驾车型
     */
    private String driverType;

    /**
     *身份证正面url
     */
    private String idCardFrontImg;

    /**
     *身份证反面url
     */
    private String idCardBackImg;

    /**
     *身份证有效期（开始）
     */
    private String idCardPeriodStart;

    /**
     *身份证有效期（结束）
     */
    private String idCardPeriodEnd;

    /**
     *政治面貌
     */
    private String politicalStatus;

    /**
     *驾驶证图片url
     */
    private String drivingImg;

    /**
     *驾驶证副本图片url
     */
    private String drivingCopyImg;

    /**
     *驾驶证有效期（开始）
     */
    private String drivingPeriodStart;

    /**
     *驾驶证有效期（结束）
     */
    private String drivingPeriodEnd;

    /**
     *驾驶证发证机关
     */
    private String issuingOrganization;

    /**
     *从业资格证图片url
     * 如果后续绑定的车辆有以下三个条件中的一个
     * 1.车长超过4.2米
     * 2.车辆类型含有“重型”“中型”
     * 3.车辆总质量超过4500kg
     * 该字段必填
     */
    private String qualificationCardImg;

    /**
     *从业资格有效期（开始）
     * 如果后续绑定的车辆有以下三个条件中的一个
     * 1.车长超过4.2米
     * 2.车辆类型含有“重型”“中型”
     * 3.车辆总质量超过4500kg
     * 该字段必填
     */
    private String qualificationCardPeriodStart;

    /**
     *从业资格有效期（结束）
     * 如果后续绑定的车辆有以下三个条件中的一个
     * 1.车长超过4.2米
     * 2.车辆类型含有“重型”“中型”
     * 3.车辆总质量超过4500kg
     * 该字段必填
     */
    private String qualificationCardPeriodEnd;


    /**
     *司机从业资格证号
     * 如果后续绑定的车辆有以下三个条件中的一个
     * 1.车长超过4.2米
     * 2.车辆类型含有“重型”“中型”
     * 3.车辆总质量超过4500kg
     * 该字段必填
     */
    private String qualificationCardNo;

    /**
     *驾驶证姓名
     */
    private String drivingName;

    /**
     *从业资格证姓名
     * 如果后续绑定的车辆有以下三个条件中的一个
     * 1.车长超过4.2米
     * 2.车辆类型含有“重型”“中型”
     * 3.车辆总质量超过4500kg
     * 该字段必填
     */
    private String qualificationName;

    /**
     *开户名 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String bankAccountName;

    /**
     *开户身份证号 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String bankIdCardNo;

    /**
     *预留手机号 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String bankPhone;

    /**
     *银行卡号 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String bankCardNo;

    /**
     *开户行名称 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String bankName;

    /**
     *银行卡照片 若要给司机绑定银行卡，该字段不能为空，否则不做添加银行卡的处理（当前添加的卡必须为司机本人的银行卡）
     */
    private String attachUrl;

    private String userCode;
}
