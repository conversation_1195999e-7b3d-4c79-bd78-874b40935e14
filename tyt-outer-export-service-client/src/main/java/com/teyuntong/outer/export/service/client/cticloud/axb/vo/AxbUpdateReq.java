package com.teyuntong.outer.export.service.client.cticloud.axb.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/14 上午10:46
 */
@Data
public class AxbUpdateReq implements Serializable {

    /**
     * 数据的id
     */
    private Long id;

    /**
     * 要修改的A电话（由于第三方接口限制，telA、telB 不能同时更新）
     */
    private String telA;

    /**
     * 要修改的B电话（由于第三方接口限制，telA、telB 不能同时更新）
     */
    private String telB;

    /**
     * 有效期，单位为秒，不传默认为0：永久有效
     */
    private Integer expiration;

    /**
     * 自定义字段
     */
    private String extraField;

    /**
     * 特运通业务字段，传给天润用于在天润后台管理中进行字段的筛选查询，编码后的json字符串
     */
    private String userField;

}
