package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 上传回单请求对象
 *
 * <AUTHOR>
 * @since 2024/07/19 14:33
 */
@Data
public class UploadWeightReceiptsReqDto {

    /**
     * 运单id
     */
    private String waybillCode;

    /**
     * 图片/文件上传对象
     */
    private List<UploadFileInfoDto> uploadFileInfoDtoList;

    /**
     * 附件类型
     */
    private String attachType;

    /**
     * 快递单号
     */
    private String expressageCode;

    /**
     * 快递单收到时间
     */
    private Date expressageUnloadTime;

    /**
     * 回单状态
     * (See: 回单状态
     * RECEIVED 回单已收
     * TRANSMITTED 回单已传)
     */
    private String returnReceiptStatus;
}
