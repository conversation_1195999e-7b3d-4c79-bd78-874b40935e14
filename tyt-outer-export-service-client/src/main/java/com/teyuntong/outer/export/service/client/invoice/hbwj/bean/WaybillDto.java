package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.TransportType;
import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ValuationType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public  class WaybillDto {
    /**
     * 运单编号
     */
    private String waybillCode;

    /**
     * 预计装货时间
     */
    private String predictLoadTime;

    /**
     * 预计卸货时间
     */
    private String predictUnloadTime;

    /**
     * 上游单号
     */
    private String upstreamCode;

    /**
     * 三方创建时间
     */
    private String threeCreateTime;

    /**
     * 运输方式
     * CAR 汽运
     * SHIP 船运
     * RAIL 铁运
     * MULTIMODAL 汽运+船运
     */
    private TransportType transportType;

    /**
     * 回单押金
     */
    private BigDecimal returnDepositAmount;

    /**
     * 订船费
     */
    private BigDecimal bookingShipAmount;

    /**
     * 出航费
     */
    private BigDecimal outSailAmount;

    /**
     * 预付款金额
     */
    private BigDecimal advancePayProportion;

    /**
     * 亏涨吨id(新增亏涨吨之后，可以用这个id与运单关联)
     */
    private Long tonLossGrowthId;

    /**
     * 计价方式
     * QUANTITY 按量
     * CAR 单车/船
     */
    private ValuationType valuationType;
}