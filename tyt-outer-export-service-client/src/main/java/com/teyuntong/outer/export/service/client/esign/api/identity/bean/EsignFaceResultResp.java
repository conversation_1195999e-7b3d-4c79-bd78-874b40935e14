package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:37
 */
@Data
public class EsignFaceResultResp {

    /**
     * 刷脸结果状态
     * ING刷脸地址已申请
     * SCAN刷脸地址已使用，但尚未接收到刷脸结果
     * SUCCESS刷脸认证通过
     * FAIL刷脸认证失败
     */
    private String	status;

    /**
     * 刷脸结果描述
     */
    private String	message;

    /**
     * 刷脸照片相似度得分（目前腾讯云刷脸和e签宝自研刷脸才会返回具体值）
     */
    private String	similarity;

    /**
     * 刷脸活体检测得分（目前腾讯云刷脸和e签宝自研刷脸才会返回具体值）
     */
    private String	livingScore;

}
