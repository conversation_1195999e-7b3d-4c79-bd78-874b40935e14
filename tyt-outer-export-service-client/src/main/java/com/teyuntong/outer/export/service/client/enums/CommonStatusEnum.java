package com.teyuntong.outer.export.service.client.enums;

import lombok.Getter;

/**
 * 全局状态枚举.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum CommonStatusEnum {
    //全局状态枚举
    yes(1, "yes"),
    no(0, "no"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    CommonStatusEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}