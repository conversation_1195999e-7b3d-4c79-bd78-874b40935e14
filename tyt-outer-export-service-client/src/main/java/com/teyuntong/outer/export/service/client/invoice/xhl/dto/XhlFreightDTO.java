package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 翔和翎 运费实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlFreightDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {XhlFreightDTO.AddInfo.class, XhlFreightDTO.UpdateInfo.class})
    private String companyName;

    /**
     * 三方运单号
     */
    @NotBlank(message = "三方运单号不能为空", groups = {XhlFreightDTO.AddInfo.class, XhlFreightDTO.UpdateInfo.class})
    private String tpWaybillNo;

    /**
     * 货达交易流水号
     */
    @NotBlank(message = "货达交易流水号不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String hdRunningNumber;

    /**
     * 承运费，每次支付金额，单位元
     */
    @NotBlank(message = "承运费不能为空", groups = {XhlFreightDTO.AddInfo.class, XhlFreightDTO.UpdateInfo.class})
    private String freight;

    /**
     * 服务费，单位元
     */
    @NotBlank(message = "服务费不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String serviceCharge;

    /**
     * 收款类型，1司机
     */
    @NotBlank(message = "收款类型不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String agentWay;

    /**
     * 支付方式，1现付2到付3回付，默认到付2
     */
    @NotBlank(message = "支付方式不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String payType;

    /**
     * 持卡人（收款人）
     */
    @NotBlank(message = "持卡人不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String ownerName;

    /**
     * 持卡人（收款人）身份证号
     */
    @NotBlank(message = "持卡人（收款人）身份证号不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String idCardNo;

    /**
     * 持卡人（收款人）银行卡号
     */
    @NotBlank(message = "持卡人（收款人）银行卡号不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String bankNo;

    /**
     * 持卡人银行名称
     */
    @NotBlank(message = "持卡人银行名称不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String bankTypeName;

    /**
     * 银行行号
     */
//    @NotBlank(message = "银行行号不能为空", groups = {XhlFreightDTO.AddInfo.class})
    private String bankBranchNo;


    /**
     * 托运费(含税)
     */
    @NotBlank(message = "托运费不能为空", groups = {XhlFreightDTO.UpdateInfo.class})
    private String shippingCharge;

    /**
     * 发车重量
     */
    @NotBlank(message = "发车重量不能为空", groups = {XhlFreightDTO.UpdateInfo.class})
    private String departWeight;

    /**
     * 签收重量
     */
    @NotBlank(message = "签收重量不能为空", groups = {XhlFreightDTO.UpdateInfo.class})
    private String signWeight;

    /**
     * 结算重量
     */
    @NotBlank(message = "结算重量不能为空", groups = {XhlFreightDTO.UpdateInfo.class})
    private String settleWeight;
    /**
     * 支付时间，时间戳格式
     */
    private String payTime;
    /**
     * 支付类型，1:现付，2:到付，3:回付
     */
    private String type;
    /**
     * 状态，1:待付款，2:付款中，3:打款失败，4打款成功
     */
    private String payStatus;
    /**
     * 支付结果
     */
    private String payLog;
    /**
     * 支付回单地址
     */
    private String bankReceiptUrl;
}
