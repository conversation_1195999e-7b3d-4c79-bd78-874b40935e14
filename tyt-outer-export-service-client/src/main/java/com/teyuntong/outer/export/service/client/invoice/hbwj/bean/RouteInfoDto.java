package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public  class RouteInfoDto {
    /**
     * 发货地简称
     */
    private String sendSimpleName;

    /**
     * 发货省
     */
    private String sendProvince;

    /**
     * 发货市
     */
    private String sendCity;

    /**
     * 发货区
     */
    private String sendArea;

    /**
     * 发货地址（不填的话优先取发货区，然后取发货市）
     */
    private String sendAddress;

    /**
     * 发货经度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
     */
    private BigDecimal sendLng;

    /**
     * 发货纬度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
     */
    private BigDecimal sendLat;

    /**
     * 收货地简称
     */
    private String receiveSimpleName;

    /**
     * 收货省
     */
    private String receiveProvince;

    /**
     * 收货市
     */
    private String receiveCity;

    /**
     * 收货区
     */
    private String receiveArea;

    /**
     * 收货地址（不填的话优先取收货区，然后取收货市）
     */
    private String receiveAddress;

    /**
     * 收货经度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
     */
    private BigDecimal receiveLng;

    /**
     * 收货纬度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
     */
    private BigDecimal receiveLat;
}




