package com.teyuntong.outer.export.service.client.esign.api.sign;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.constant.OuterExportConstant;
import com.teyuntong.outer.export.service.client.esign.vo.sign.*;
import com.teyuntong.outer.export.service.client.fallback.CommonExceptionFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 易签宝-电子签名
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 *
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
@Primary
@FeignClient(name = OuterExportConstant.SERVICE_PROVIDER + "/esignSign",
        fallbackFactory = CommonExceptionFallbackFactory.class)
public interface EsignSignClient {

    /**
     * 本地PDF模板文件流填充
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/sed7zt">官方文档链接</a>
     */
    @PostMapping("/pdf/create")
    WebResult<CreatePdfVO> createPdf(@RequestBody @Validated CreatePdfReq req);

    /**
     * 创建企业签署账户
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/nogqr7">官方文档链接</a>
     */
    @PostMapping("/account/add")
    WebResult<AddAccountVO> addAccount(@RequestBody @Validated AddAccountReq req);

    /**
     * 创建个人签署账户
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/ndxlx8">官方文档链接</a>
     */
    @PostMapping("/addPersonAccount")
    WebResult<AddAccountVO> addPersonAccount(@RequestBody @Validated AddPersonAccountReq req);

    /**
     * 创建企业模板印章
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/qg2s0z">官方文档链接</a>
     */
    @PostMapping("/template/seal/add")
    WebResult<TemplateSealVO> addTemplateSeal(@RequestBody @Validated TemplateSealReq req);

    /**
     * 创建个人模板印章
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/meryvg">官方文档链接</a>
     */
    @PostMapping("/addPersonSeal")
    WebResult<TemplateSealVO> addPersonSeal(@RequestBody @Validated PersonTemplateSealReq req);

    /**
     * 指定手机发送签署短信验证码
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/uv05t5">官方文档链接</a>
     */
    @PostMapping("/mobileCode/send")
    WebResult<?> sendSignMobileCode3rd(@RequestBody @Validated SendSignMobileCode3rdReq req);

    /**
     * 平台用户PDF文件流签署（指定手机号短信验证）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/gg9yea">官方文档链接</a>
     */
    @PostMapping("/localPdf/saveSign3rd")
    WebResult<LocalSafeSignPDF3rdVO> localSafeSignPDF3rd(@RequestBody @Validated LocalSafeSignPDF3rdReq req);

    /**
     * 平台用户PDF文件多位置文件流签署（指定手机短信验证）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/defebg">官方文档链接</a>
     */
    @PostMapping("/localPdf/multiPositionSign3rd")
    WebResult<LocalSafeSignPDF3rdVO> multiPositionSign3rd(@RequestBody @Validated MultiPositionSignPDF3rdReq req);

    /**
     * 平台自身PDF文件流签署（印章标识）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/zk13ay">官方文档链接</a>
     */
    @PostMapping("/localPdf/sealIdSign")
    WebResult<LocalSignPdfVO> sealIdSign(@RequestBody @Validated LocalSignPdfReq req);

    /**
     * PDF文档验签（文件流）
     *
     * <a href="https://open.esign.cn/doc/opendoc/paas_sdk/ruk0br">官方文档链接</a>
     */
    @PostMapping("/localPdf/verify")
    WebResult<LocalVerifyPdfVO> localSignPdf(@RequestBody @Validated LocalVerifyPdfReq req);

    /**
     * 平台用户PDF文件签署(无意愿)
     *
     * <a href="https://qianxiaoxia.yuque.com/opendoc/pv66r3/wq86pt">官方文档链接</a>
     */
    @PostMapping("/silentUserSealSign")
    WebResult<LocalSafeSignPDF3rdVO> silentUserSealSign(@RequestBody @Validated SilentUserSealSignReq req);

}
