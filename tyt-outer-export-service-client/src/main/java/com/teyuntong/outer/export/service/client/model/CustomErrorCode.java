package com.teyuntong.outer.export.service.client.model;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;

/**
 * 自定义灵活的错误码
 *
 * <AUTHOR>
 * @date 2024/3/29 13:26
 */
public interface CustomErrorCode extends ErrorCodeBase {

    String getLogLevel();

    default String logLevel() {
        if(this.getLogLevel() != null){
            return this.getLogLevel();
        }
        return "warn";
    }

    default ResponseCode info(String msg){

        ResponseCode esignErrorCode = new ResponseCode();

        esignErrorCode.setCode(this.getCode());
        esignErrorCode.setMsg(msg);
        esignErrorCode.setLogLevel(this.getLogLevel());
        esignErrorCode.setSuccess(this.isSuccess());

        return esignErrorCode;
    }

    /**
     * 判断是否是某个code
     * @param reqCode reqCode
     * @return boolean
     */
    default boolean equalsCode(String reqCode){
        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

    static ResponseCode easyInfo(ErrorCodeBase codeBase){

        ResponseCode esignErrorCode = new ResponseCode();

        esignErrorCode.setCode(codeBase.getCode());
        esignErrorCode.setMsg(codeBase.getMsg());
        esignErrorCode.setLogLevel(codeBase.logLevel());
        esignErrorCode.setSuccess(codeBase.isSuccess());

        return esignErrorCode;
    }

    static ResponseCode easyInfo(ErrorCodeBase codeBase, String msg){

        ResponseCode esignErrorCode = new ResponseCode();

        esignErrorCode.setCode(codeBase.getCode());
        esignErrorCode.setMsg(msg);
        esignErrorCode.setLogLevel(codeBase.logLevel());
        esignErrorCode.setSuccess(codeBase.isSuccess());

        return esignErrorCode;
    }

    static ResponseCode easyInfo(String code, String msg){

        ResponseCode esignErrorCode = new ResponseCode();

        esignErrorCode.setCode(code);
        esignErrorCode.setMsg(msg);
        esignErrorCode.setLogLevel("warn");
        esignErrorCode.setSuccess(false);

        return esignErrorCode;
    }


}
