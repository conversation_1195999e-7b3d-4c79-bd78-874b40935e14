package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ValuationType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 编辑运单(三方)请求参数实体类
 *
 * <AUTHOR>
 * @since 2024/7/16 13:23
 */
@Data
public class EditWaybillThreeRequest {

    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 运单信息
     */
    private WaybillDto waybillDto;

    /**
     * 运力信息
     */
    private CarDriverDto carDriverDto;

    /**
     * 路线信息
     */
    private RouteInfoDto routeInfoDto;

    /**
     * 货物信息
     */
    private GoodsInfoDto goodsInfoDto;

    /**
     * 结算信息
     */
    private UpWaybillSettlementDto upWaybillSettlementDto;

    /**
     * 运单装载信息
     */
    /*private WaybillLoadDto waybillLoadDto;*/


    private String userCode;


}