package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/28 14:35
 */
@NoArgsConstructor
@Data
public class LegalRepSignApiReq {
    /**
     * 当前实名认证办理人姓名
     */
    @NotNull
    private String agentName;
    /**
     * 当前实名认证办理人证件号（仅支持大陆二代身份证）
     */
    @NotNull
    private String agentIdNo;
    /**
     * 法定代表人手机号，用于签署电子授权书
     */
    @NotNull
    private String mobileNo;
    /**
     * 法定代表人身份证号,如果信息比对api中已传入，可为空；否则需传入
     * <p>
     * （企业3要素认证时该字段必传）
     */
    private String legalRepIdNo;
    /**
     * 法定代表人签署完成后，重定向跳转地址
     */
    private String redirectUrl;
}
