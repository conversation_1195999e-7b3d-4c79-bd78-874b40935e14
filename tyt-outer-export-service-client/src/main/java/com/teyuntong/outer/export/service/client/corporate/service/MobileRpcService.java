package com.teyuntong.outer.export.service.client.corporate.service;

import com.teyuntong.outer.export.service.client.corporate.vo.PhoneLocale;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 手机号归属地查询
 *
 * <AUTHOR>
 * @since 2024/12/05 21:32
 */
public interface MobileRpcService {

    /**
     * 手机号归属地查询
     *
     * @param mobile
     * @return
     * @throws Exception
     */
    @GetMapping(produces = "application/json;charset=utf-8", path = "/mobile")
    PhoneLocale getMobile(@RequestParam(value = "mobile") String mobile) throws Exception;
}
