package com.teyuntong.outer.export.service.client.megvii.service;

import com.teyuntong.outer.export.service.client.megvii.vo.v3.GetBizTokenV3Req;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.MegviiBizTokenV3Resp;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.VerifyV3Req;
import com.teyuntong.outer.export.service.client.megvii.vo.v3.VerifyV3Resp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;

/**
 * 旷世人脸识别-v3版本
 *
 * <AUTHOR>
 * @since 2024/12/10 10:37
 */
public interface MegaiiFaceIdV3RpcService {

    /**
     * 获取业务token
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping("/megaii/face_id/v3/get_biz_token")
    MegviiBizTokenV3Resp getBizToken(@RequestBody @Validated GetBizTokenV3Req req);

    /**
     * 比对认证
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping("/megaii/face_id/v3/verify")
    VerifyV3Resp verify(@RequestBody @Validated VerifyV3Req req);
}
