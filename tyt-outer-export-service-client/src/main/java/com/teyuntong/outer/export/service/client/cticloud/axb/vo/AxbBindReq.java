package com.teyuntong.outer.export.service.client.cticloud.axb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/14 上午10:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AxbBindReq implements Serializable {

    /**
     * 要绑定的A电话
     */
    @NotNull(message = "telA不能为空")
    private String telA;

    /**
     * 要绑定的B电话
     */
    @NotNull(message = "telB不能为空")
    private String telB;

    /**
     * 业务类型
     */
    @NotNull(message = "bizType不能为空")
    private Integer bizType;

    /**
     * 业务id
     */
    @NotNull(message = "bizId不能为空")
    private Long bizId;

    /**
     * 有效期，单位为秒，不传默认为0：永久有效
     */
    private Integer expiration;

    /**
     * 自定义字段
     */
    private String extraField;

    /**
     * 特运通业务字段，传给天润用于在天润后台管理中进行字段的筛选查询，编码后的json字符串
     */
    private String userField;

    /**
     * 使用哪个天润账号 1:用账号1；2:用账号2
     */
    private Integer tianrunAccount;

    public AxbBindReq(String telA, String telB, Integer bizType, Long bizId, Integer expiration, String extraField, String userField) {
        this.telA = telA;
        this.telB = telB;
        this.bizType = bizType;
        this.bizId = bizId;
        this.expiration = expiration;
        this.extraField = extraField;
        this.userField = userField;
    }

}
