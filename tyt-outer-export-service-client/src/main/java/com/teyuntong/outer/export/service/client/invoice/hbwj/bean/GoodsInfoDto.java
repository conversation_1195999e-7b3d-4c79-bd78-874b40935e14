package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.GoodsUnitEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public  class GoodsInfoDto {
    /**
     * 货物品类 1钢材类 2快消类 3煤炭类
     */
    private String goodsCategory;

    /**
     * 货物计量单位
     * TON 吨
     * CUBIC 方
     * PIECE 件
     */
    private GoodsUnitEnum goodsUnit;

    /**
     * 货物重量
     */
    private BigDecimal goodsWeight;

    /**
     * 运输单价
     */
    private BigDecimal unitAmount;

    /**
     * 运输金额
     */
    private BigDecimal amount;

    /**
     * 货物品名
     */
    private String goodsName;
}

