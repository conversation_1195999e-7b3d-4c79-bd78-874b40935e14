package com.teyuntong.outer.export.service.client.manbang.api.enterprise;

import com.teyuntong.outer.export.service.client.constant.OuterExportConstant;
import com.teyuntong.outer.export.service.client.fallback.CommonExceptionFallbackFactory;
import com.teyuntong.outer.export.service.client.manbang.api.enterprise.bean.CompanyRealNameReq;
import com.teyuntong.outer.export.service.client.manbang.vo.EnterpriseRealNameStatusVo;
import com.teyuntong.outer.export.service.client.model.FileDataInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 企业实名相关Controller
 *
 * <AUTHOR>
 * @since 2024/4/2 19:22
 */
@Primary
@FeignClient(name = OuterExportConstant.SERVICE_PROVIDER + "/out/enterpriseReal",
        fallbackFactory = CommonExceptionFallbackFactory.class)
public interface OutEnterpriseRealClient {

    /**
     * 上传文件
     */
    @PostMapping("/uploadFile")
    String uploadFile(@RequestBody FileDataInfo fileDataInfo);

    /**
     * 提交企业实名认证
     */
    @PostMapping("/companyRealName")
    String companyRealName(@RequestBody CompanyRealNameReq realNameReq);

    /**
     * 提交企业实名认证
     */
    @PostMapping("/getRealNameStatus")
    EnterpriseRealNameStatusVo getRealNameStatus(@RequestParam("identityNo") String identityNo);

}
