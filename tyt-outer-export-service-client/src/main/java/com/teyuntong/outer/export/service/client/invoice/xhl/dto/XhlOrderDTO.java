package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 翔和翎 订单实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlOrderDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 三方订单号
     */
    @NotBlank(message = "三方订单号不能为空")
    private String tpOrderNo;

    /**
     * 发货人
     */
    @NotBlank(message = "发货人不能为空")
    private String shipperName;

    /**
     * 发货电话
     */
    @NotBlank(message = "发货电话不能为空")
    private String shipperPhone;

    /**
     * 发货地址
     */
    @NotBlank(message = "发货地址不能为空")
    private String shippingAddress;

    /**
     * 发货经度
     */
    @NotBlank(message = "发货经度不能为空")
    private String startLng;

    /**
     * 发货纬度
     */
    @NotBlank(message = "发货纬度不能为空")
    private String startLat;

    /**
     * 发货地城市编码
     */
    @NotBlank(message = "发货地城市编码不能为空")
    private String startArea;

    /**
     * 收货单位
     */
    @NotBlank(message = "收货单位不能为空")
    private String consigneeBranch;

    /**
     * 收货人
     */
    @NotBlank(message = "收货人不能为空")
    private String consigneeName;

    /**
     * 收货电话
     */
    @NotBlank(message = "收货电话不能为空")
    private String consigneePhone;

    /**
     * 收货地址
     */
    @NotBlank(message = "收货地址不能为空")
    private String consigneeAddress;

    /**
     * 收货经度
     */
    @NotBlank(message = "收货经度不能为空")
    private String endLng;

    /**
     * 收货纬度
     */
    @NotBlank(message = "收货纬度不能为空")
    private String endLat;

    /**
     * 收货地城市编码
     */
    @NotBlank(message = "收货地城市编码不能为空")
    private String endArea;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    private String goodsName;

    /**
     * 货物类型
     */
    @NotBlank(message = "货物类型不能为空")
    private String goodsType;

    /**
     * 重量（吨）
     */
    @NotBlank(message = "重量不能为空")
    private String weight;

    /**
     * 单价
     */
    @NotBlank(message = "单价不能为空")
    private String unitPrice;

    /**
     * 体积（可空,若空，传0）
     */
    @NotBlank(message = "体积不能为空")
    private String goodsVolume;

    /**
     * 数量（可空,若空，传0）
     */
    @NotBlank(message = "数量不能为空")
    private String goodsNumber;

    /**
     * 估值方式
     */
    @NotBlank(message = "估值方式不能为空")
    private String valuationWay;

    /**
     * 托运费,可以传0
     */
    @NotBlank(message = "托运费不能为空")
    private String shippingCharge;

    /**
     * 承运费,可以传0
     */
    @NotBlank(message = "承运费不能为空")
    private String freight;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "创建时间不能为空")
    private String createTime;

    /**
     * 备注，非必填
     */
    private String remark;


}
