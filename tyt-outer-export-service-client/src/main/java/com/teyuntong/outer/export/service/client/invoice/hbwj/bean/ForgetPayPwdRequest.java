package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 重置支付密码
 * <AUTHOR>
 * @since 2025/06/16 13:39
 */
@Data
public class ForgetPayPwdRequest implements Serializable {

    /**
     * 验证码（测试：2025-01-22：验证码：
     * 221052
     * ）日期倒排后 6 位。生产以实际短信为
     * 准
     */
    private String checkCode;

    /**
     * 新支付密码
     */
    private String newPayPwd;

    /**
     * 确认支付密码
     */
    private String confirmPayPwd;
}
