package com.teyuntong.outer.export.service.client.ocr.vo.baidu;

import lombok.Data;

import java.util.Date;

/**
 * 身份证反面照信息
 *
 * <AUTHOR>
 * @date 2023/4/26 16:10
 */
@Data
public class OcrIdCardBackVo extends OcrDataVo {

    private String imageStatus;

    //失效日期
    private String expireDateText;
    private Date expireDate;

    //签发机关
    private String signGov;

    //签发日期
    private String signDateText;
    private Date signDate;

}
