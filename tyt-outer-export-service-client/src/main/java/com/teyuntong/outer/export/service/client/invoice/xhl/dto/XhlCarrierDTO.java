package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 托运人实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlCarrierDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {XhlCarrierDTO.AddInfo.class, XhlCarrierDTO.UpdateInfo.class})
    private String companyName;

    /**
     * 法人姓名
     */
    @NotBlank(message = "法人姓名不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String person;

    /**
     * 法人电话
     */
    @NotBlank(message = "法人电话不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String personPhone;

    /**
     * 法人身份证号
     */
    @NotBlank(message = "法人身份证号不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String idCardNo;

    /**
     * 统一社会代码
     */
    @NotBlank(message = "统一社会代码不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String unifiedSocialCreditCode;

    /**
     * 公司地址
     */
    @NotBlank(message = "公司地址不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String address;

    /**
     * 公司地址城市编码
     */
    @NotBlank(message = "公司地址城市编码不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String areaCode;

    /**
     * 营业执照照片
     */
    @NotBlank(message = "营业执照照片不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String businessCertificatePhotoUrl;

    /**
     * 法人身份证正面照片地址
     */
    @NotBlank(message = "法人身份证正面照片地址不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String idCardNoFrontUrl;

    /**
     * 法人身份证反面照片地址
     */
    @NotBlank(message = "法人身份证反面照片地址不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String idCardNoBackUrl;

    /**
     * 开户许可证照片地址
     */
    @NotBlank(message = "开户许可证照片地址不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String permitPhotoUrl;

    /**
     * 客户点数，传百分比，例如5%，传入0.05即可
     */
    @NotBlank(message = "客户点数不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String rate;

    /**
     * 1:不含税（硬点），2:含税（软点）
     */
    @NotBlank(message = "客户点数类型不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String rateType;

    /**
     * 营业执照有效期开始，yyyy-MM-dd格式
     */
    @NotBlank(message = "营业执照有效期开始不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String effectiveDate;

    /**
     * 营业执照有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    @NotBlank(message = "营业执照有效期结束不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String expiryDate;

    /**
     * 开票信息电话
     */
    private String kpCustomerPhone;

    /**
     * 开票信息地址
     */
    private String kpCustomerAddress;

    /**
     * 开票信息开户行
     */
    private String kpBankName;

    /**
     * 开票信息银行账号
     */
    private String kpBankAccountNo;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "创建时间不能为空", groups = {XhlCarrierDTO.AddInfo.class})
    private String createTime;


    /**
     * 虚户会员id
     */
    private String njUserId;

    /**
     * 虚户账号
     */
    private String njAcctNo;

    /**
     * 余额，单位元
     */
    private String balance;

    /**
     * 冻结金额，单位元
     */
    private String ctrlBalance;

    /**
     * 卡号
     */
    private String bankNo;

    /**
     * 银行名称
     */
    private String bankTypeName;

    /**
     * 开户行
     */
    private String bankBranchName;
}
