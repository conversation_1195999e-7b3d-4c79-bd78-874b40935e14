package com.teyuntong.outer.export.service.client.cticloud.axb.service;

import com.teyuntong.outer.export.service.client.cticloud.axb.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * axb绑定相关
 *
 * <AUTHOR>
 * @since 2024/07/17 13:13
 */
public interface AxbRpcService {

    /**
     * axb绑定
     *
     * @param req
     * @return
     */
    @PostMapping("/axb/bind")
    AxbBindVO axbBind(@RequestBody @Validated AxbBindReq req);

    /**
     * axb绑定-GET
     *
     * @param telA
     * @param telB
     * @param bizType
     * @param bizId
     * @param expiration
     * @param extraField
     * @return
     */
    @GetMapping("/axb/bindGet")
    AxbBindVO bindGet(@RequestParam("telA") String telA, @RequestParam("telB") String telB, @RequestParam("bizType") Integer bizType
            , @RequestParam("bizId") Long bizId, @RequestParam("expiration") Integer expiration, @RequestParam("extraField") String extraField);

    /**
     * axb修改
     *
     * @param req
     * @return
     */
    @PostMapping("/axb/update")
    void axbUpdate(@RequestBody @Validated AxbUpdateReq req);

    /**
     * axb查询
     *
     * @param bizType
     * @param bizId
     * @param telA
     * @param telB
     * @return
     */
    @GetMapping("/axb/info")
    List<AxbInfoVO> getAxbInfo(
            @RequestParam("bizType") Integer bizType,
            @RequestParam("bizId") Long bizId,
            @RequestParam(value = "telA", required = false) String telA,
            @RequestParam(value = "telB", required = false) String telB,
            @RequestParam(value = "extraField", required = false) String extraField);

    /**
     * axb删除
     *
     * @param req
     * @return
     */
    @PostMapping("/axb/delete")
    void axbDelete(@RequestBody @Validated AxbDeleteReq req);

    /**
     * 获取通话记录
     *
     * @param req
     * @return
     */
    @PostMapping("/axb/getCDR")
    CDRsResp getCDR(@RequestBody @Validated CDRReq req);

}
