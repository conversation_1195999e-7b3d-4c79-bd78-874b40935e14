package com.teyuntong.outer.export.service.client.invoice.xhl.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 托运人绑卡实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlCarrierBankVO {

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 卡号
     */
    private String bankNo;

    /**
     * 银行名称
     */
    private String bankTypeName;

    /**
     * 开户行
     */
    private String bankBranchName;

    /**
     * 行号
     */
    private String branchNo;

    /**
     * 虚户会员id
     */
    private String njUserId;

    /**
     * 虚户账号
     */
    private String njAcctNo;

    /**
     * 余额，单位元
     */
    private String balance;

    /**
     * 冻结金额，单位元
     */
    private String ctrlBalance;


}
