//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.teyuntong.outer.export.service.client.esign.enums.sign;

public enum LegalAreaTypeEnum {
    MAINLAND(19),
    HONGKONG(17),
    MACAO(17),
    TAIWAN(18),
    FOREIGN(23),
    PASSPORT(13),
    SOLDIER_IDNO(14),
    SOCIAL_SECURITY_CARD(15),
    ARMED_POLICE_ID(16),
    RESIDENCE_BOOKLET(20),
    TEMPORARY_IDNO(21),
    FOREIGNER_PERMANENT_RESIDENCE_PERMIT(22),
    OTHER(23);

    private Integer area;

    private LegalAreaTypeEnum(Integer area) {
        this.area = area;
    }

    public Integer type() {
        return this.area;
    }

    public static LegalAreaTypeEnum of(Integer type) {
        LegalAreaTypeEnum[] var1;
        int var2 = (var1 = values()).length;

        for(int var3 = 0; var3 < var2; ++var3) {
            LegalAreaTypeEnum area;
            if ((area = var1[var3]).area.equals(type)) {
                return area;
            }
        }

        return null;
    }
}
