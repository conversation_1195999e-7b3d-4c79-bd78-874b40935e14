package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/28 14:25
 */
@Data
public class EnterpriseFourFactorsApiReq {

    /**
     * 组织机构证件（如营业执照）上的组织机构名称
     */
    @NotNull
    private String name;
    /**
     * 组织机构证件号，支持统一社会信用代码号和工商注册号（部分个体工商户）
     */
    @NotNull
    private String orgCode;
    /**
     * 法人证件类型
     * <p>
     * INDIVIDUAL_CH_IDCARD - 中国大陆身份证（默认值）INDIVIDUAL_CH_TWCARD - 台湾来往大陆通行证（台胞证）
     * <p>
     * INDIVIDUAL_CH_HONGKONG_MACAO - 港澳来往大陆通行证（回乡证）
     * <p>
     * INDIVIDUAL_PASSPORT - 护照
     */
    private String legalRepCertType;
    /**
     * 法定代表人名称
     */
    @NotNull
    private String legalRepName;
    /**
     * 法定代表人证件号
     */
    @NotNull
    private String legalRepIdNo;
    /**
     * 官方文档请求示例有，参数列表没有，不知道干什么用的
     */
    private Boolean repetition;
    /**
     * 自定义业务标识，将在异步通知及跳转时携带返回对接方
     */
    private String contextId;
    /**
     * 认证结束后异步通知地址,具体见 异步通知 章节说明
     */
    private String notifyUrl;
    /**
     * 经办人个人认证流程Id
     * <p>
     * （1）当企业认证方式想要使用法定代表人本人认证方式时使用该字段进行判断
     * <p>
     * （2）e签宝会判断经办人流程里的个人信息和当前四要素比对的法定代表人名称，证件号，证件类型是否全部一致，一致方可直接认证通过（返回flowId），无需后续步骤。
     * <p>
     * （3）如果经办人不是法定代表人本人，接口会直接报错。
     */
    private String agentFlowId;
}
