package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 司机实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlDriverDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    public interface FaceAuth extends Default {

    }

    public interface FaceAuthQuery extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 驾驶员手机号
     */
    @NotBlank(message = "驾驶员手机号不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String driverPhone;

    /**
     * 驾驶员姓名
     */
    @NotBlank(message = "驾驶员姓名不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String driverName;

    /**
     * 驾驶员身份证号
     */
    @NotBlank(message = "驾驶员身份证号不能为空", groups = {XhlDriverDTO.AddInfo.class, XhlDriverDTO.UpdateInfo.class, XhlDriverDTO.FaceAuth.class, XhlDriverDTO.FaceAuthQuery.class})
    private String idCardNo;

    /**
     * 驾驶员身份证上地址
     */
    @NotBlank(message = "驾驶员身份证上地址不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String address;

    /**
     * 准驾车型
     */
    @NotBlank(message = "准驾车型不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String driverType;

    /**
     * 身份证有效期开始，yyyy-MM-dd格式
     */
    @NotBlank(message = "身份证有效期开始不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String cardStartUseDate;

    /**
     * 身份证有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    @NotBlank(message = "身份证有效期结束不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String cardEndUseDate;

    /**
     * 驾驶证有效期开始，yyyy-MM-dd格式
     */
    @NotBlank(message = "驾驶证有效期开始不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String startUseDate;

    /**
     * 驾驶证有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    @NotBlank(message = "驾驶证有效期结束不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String endUseDate;

    /**
     * 驾驶员身份证正面照片地址
     */
    @NotBlank(message = "驾驶员身份证正面照片地址不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String idCardNoFrontUrl;

    /**
     * 驾驶员身份证反面照片地址
     */
    @NotBlank(message = "驾驶员身份证反面照片地址不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String idCardNoBackUrl;

    /**
     * 驾驶员驾驶证照片地址
     */
    @NotBlank(message = "驾驶员驾驶证照片地址不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String drivingLicenseUrl;

    /**
     * 驾驶员驾驶证副页照片地址
     */
    private String drivingLicenseBackUrl;

    /**
     * 从业资格证照片
     */
    @NotBlank(message = "从业资格证照片不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String qualiCertNoUrl;

    /**
     * 从业资格证号
     */
    @NotBlank(message = "从业资格证号不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String qualificationCertNo;

    /**
     * 从业资格证有效期结束，yyyy-MM-dd格式
     */
    @NotBlank(message = "从业资格证有效期结束不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String qualificationCertExpireTime;

    /**
     * 驾驶证发证机关
     */
    @NotBlank(message = "驾驶证发证机关不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String issuingOrganizations;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "创建时间不能为空", groups = {XhlDriverDTO.AddInfo.class})
    private String createTime;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空", groups = {XhlDriverDTO.FaceAuth.class, XhlDriverDTO.FaceAuthQuery.class})
    private String realName;

    /**
     * 人脸识别唯一标识
     */
    @NotBlank(message = "人脸识别唯一标识不能为空", groups = {XhlDriverDTO.FaceAuthQuery.class})
    private String faceAuthOrderId;
}
