package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 新增开票信息
 *
 * <AUTHOR>
 * @since 2024/08/06 13:59
 */
@Data
public class InvoiceRequest implements Serializable {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNumber;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerTelephone;

    /**
     * 银行账号
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 邮寄地址 和 电子邮箱 二选一 邮寄地址填写了 收件人和收件人电话必填
     */
    private String sendAddress;

    /**
     * 收件人
     */
    private String receiveName;

    /**
     * 收件人电话
     */
    private String receivePhone;

    /**
     * 电子邮箱 和 邮寄地址 二选一
     */
    private String email;


}
