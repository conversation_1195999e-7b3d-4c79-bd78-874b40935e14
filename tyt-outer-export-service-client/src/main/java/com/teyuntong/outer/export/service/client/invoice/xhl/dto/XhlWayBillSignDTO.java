package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 翔和翎 运单签收实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlWayBillSignDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 三方运单号
     */
    @NotBlank(message = "三方运单号不能为空")
    private String tpWaybillNo;

    /**
     * 签收时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "签收时间不能为空")
    private String signTime;

    /**
     * 签收重量，单位吨
     */
    @NotBlank(message = "签收重量不能为空")
    private String signWeight;

    /**
     * 结算重量，单位吨
     */
    @NotBlank(message = "结算重量不能为空")
    private String settleWeight;

    /**
     * 签收体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String signVolume;

    /**
     * 结算体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String settleVolume;

    /**
     * 回单地址，多张逗号隔开
     */
    private String receiptPhoto;

}
