package com.teyuntong.outer.export.service.client.esign.enums.sign;

import lombok.Getter;

/**
 * 签章类型
 */
public enum SignSealTypeEnum {
    SINGLE(1, "Single"),
    MULTI(2, "Multi"),
    EDGES(3, "Edges"),
    KEY(4, "Key"),
    ;

    @Getter
    private final int val;

    @Getter
    private final String code;

    SignSealTypeEnum(int val, String code) {
        this.val = val;
        this.code = code;
    }
}
