package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发票创建
 *
 * <AUTHOR>
 * @since 2024/08/06 17:32
 */
@Data
public class InvoiceCreateRequest implements Serializable {

    /**
     * 发票信息id 此id是新增发票信息返回的id 如果此id不为空，则以下购买方相关参数就会忽略
     */
    private Long invoiceMaintenanceId;

    /**
     * 结算主体编码 从税源地接口获取
     */
    private String settlementNo;

    /**
     * 1：运费发票 2：增值服务发票
     */
    private String invoiceKind;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 运单号
     */
    private List<String> waybillCodeList;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 购买方名称 发票信息id不为空，此参数会被忽略
     */
    private String buyerName;

    /**
     * 购买方纳税人识别号 发票信息id不为空，此参数会被忽略.
     */
    private String buyerTaxpayerIdentificationNumber;

    /**
     * 购买方地址 发票信息id不为空，此参数会被忽略
     */
    private String buyerAddress;

    /**
     * 购买方电话 发票信息id不为空，此参数会被忽略
     */
    private String buyerTelephone;

    /**
     * 购买方银行 发票信息id不为空，此参数会被忽略
     */
    private String buyerBankName;

    /**
     * 购买方银行账号 发票信息id不为空，此参数会被忽略
     */
    private String buyerBankAccount;

    /**
     * 收件地址 发票信息id不为空，此参数会被忽略
     */
    private String mailingAddress;

    /**
     * 收件邮箱 发票信息id不为空，此参数会被忽略
     */
    private String mailingEmail;

    /**
     * 收件人电话 发票信息id不为空，此参数会被忽略
     */
    private String mailingPhone;

    /**
     * 收件人姓名 发票信息id不为空，此参数会被忽略
     */
    private String mailingName;

}
