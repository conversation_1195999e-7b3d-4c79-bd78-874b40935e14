package com.teyuntong.outer.export.service.client.esign.vo.sign;

import com.teyuntong.outer.export.service.client.esign.enums.sign.OrganRegTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class AddAccountReq {

    /**
     * 邮箱地址，可空
     */
    private String email;
    /**
     * 用于接收签署验证码的手机号码,可空
     */
    private String mobile;
    /**
     * 机构名称，不可空
     */
    @NotNull
    private String name;
    /**
     * 单位类型，0-普通企业，1-社会团体，2-事业单位，3-民办非企业单位，4-党政及国家机构
     */
    private int organType;
    /**
     * 注册类型,1-代理人注册,2-法人注册,0-缺省注册无需法人或代理人信息
     */
    @NotNull
    private Integer userType;
    /**
     * 组织机构代码号、社会信用代码号或工商注册号
     */
    @NotNull
    private String organCode;
    /**
     * 法定代表姓名，当注册类型为2时必填
     */
    private String legalName;
    /**
     * 法定代表身份证号/护照号，当注册类型为2时必填
     */
    private String legalIdNo;
    /**
     * 法定代表人归属地，0-大陆，1-香港，2-澳门，3-台湾，4-外籍，默认0
     */
    private Integer legalArea = 0;
    /**
     * 代理人姓名，当注册类型为1时必填
     */
    private String agentName;
    /**
     * 代理人身份证号，当注册类型为1时必填
     */
    private String agentIdNo;
    /**
     * 代理人 area
     */
    private Integer agentArea;
    /**
     * 公司地址,可空
     */
    private String address;
    /**
     * 经营范围,可空
     */
    private String scope;

    /**
     * 企业注册类型，NORMAL:组织机构代码号，MERGE：多证合一，传递社会信用代码号,REGCODE:企业工商注册码,默认NORMA
     */
    private OrganRegTypeEnum regType = OrganRegTypeEnum.MERGE;

}
