package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建运单(三方)请求实体类
 *
 * <AUTHOR>
 * @since 2024/7/16 13:10
 */
@Data
public class CreateWaybillThreeRequest {

    /**
     * 用户会员代码
     */
    private String userCode;

    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 运单信息
     */
    private WaybillDto waybillDto;

    /**
     * 运力信息
     */
    private CarDriverDto carDriverDto;

    /**
     * 路线信息
     */
    private RouteInfoDto routeInfoDto;

    /**
     * 货物信息
     */
    private GoodsInfoDto goodsInfoDto;

    /**
     * 项目信息
     */
    private ProjectStoreDto projectStoreDto;

    /**
     * 结算信息
     */
    private UpWaybillSettlementDto upWaybillSettlementDto;

    /**
     * 三方配置
     */
    @Data
    public static class ThreeConfig {
        /**
         * 建单后运单状态：1待接单 2待起运(默认)
         */
        private Integer createWaybillStatus;

        /**
         * 是否发送运输合同 true(默认) false
         */
        private Boolean isSendWaybillContract;
    }

    /**
     * 运单信息
     */
    @Data
    public static class WaybillDto {
        /**
         * 业务来源 ORDER订单 PLAN计划单 WAYBILL直接派单（默认）
         */
        private String businessSource;

        /**
         * 业务单号(暂未开放)
         */
        private String businessCode;

        /**
         * 业务来源 1直接建单（默认） 2承运方帮货主建单 3货主帮承运方建单
         */
        private Integer waybillType;

        /**
         * 必传条件：businessSource = WAYBILL && waybillType = 2 货主id(开户成功后返回的)
         */
        private String ownerId;

        /**
         * 必传条件：businessSource = WAYBILL && waybillType = 3 承运方或经纪人id(开户成功后返回的)
         */
        private String carrierId;

        /**
         * 预计装货时间 yyyy-MM-dd HH:mm:ss
         */
        private String predictLoadTime;

        /**
         * 预计卸货时间 yyyy-MM-dd HH:mm:ss
         */
        private String predictUnloadTime;

        /**
         * 三方单号
         */
        private String upstreamCode;

        /**
         * 三方创建时间 yyyy-MM-dd HH:mm:ss
         */
        private String threeCreateTime;

        /**
         * 运输方式(暂不支持：铁运，汽运+船运) CAR 汽运 SHIP 船运 RAIL 铁运 MULTIMODAL 汽运+船运
         */
        private String transportType;

        /**
         * 回单押金
         */
        private BigDecimal returnDepositAmount;

        /**
         * 订船费
         */
        private BigDecimal bookingShipAmount;

        /**
         * 出航费
         */
        private BigDecimal outSailAmount;

        /**
         * 预付款金额
         */
        private BigDecimal advancePayProportion;

        /**
         * 亏涨吨id(新增亏涨吨之后，可以用这个id与运单关联)
         */
        private Long tonLossGrowthId;

        /**
         * 计价方式 QUANTITY 按量 CAR 单车/船
         */
        private String valuationType;
    }

    /**
     * 运力信息
     */
    @Data
    public static class CarDriverDto {
        /**
         * 运力Id（如果填了运力id，则引用运力）
         */
        private String vehicleId;

        /**
         * 驾驶人id（如果填了驾驶人id和车船号id，则引用驾驶人和车船号）
         */
        private String driverId;

        /**
         * 车船号id（如果填了驾驶人id和车船号id，则引用驾驶人和车船号）
         */
        private String travelId;

        /**
         * 驾驶人姓名（如果不引用运力或驾驶人车船号，驾驶人姓名必填）
         */
        private String driverName;

        /**
         * 驾驶人手机号（如果不引用运力或驾驶人车船号，驾驶人手机号必填）
         */
        private String driverPhone;

        /**
         * 车船号（如果不引用运力或驾驶人车船号，车船号必填）
         */
        private String travelNum;

        /**
         * 约定收款人id（如果填了约定收款人id，则引用约定收款人）
         */
        private String payeeId;

        /**
         * 约定收款人账户名称（如果不引用约定收款人，约定收款人账户名称必填）
         */
        private String bankAccountName;

        /**
         * 约定收款人银行预留手机号（如果不引用约定收款人，约定收款人银行预留手机号必填）
         */
        private String bankPhone;

        /**
         * 约定收款人身份证号（如果不引用约定收款人，约定收款人身份证号必填）
         */
        private String idCardNo;

        /**
         * 约定收款人银行卡号（如果不引用约定收款人，约定收款人银行卡号必填）
         */
        private String bankCardNo;

        /**
         * 约定收款人开户行名称（如果不引用约定收款人，约定收款人开户行名称必填）
         */
        private String bankName;
    }

    /**
     * 路线信息
     */
    @Data
    public static class RouteInfoDto {
        /**
         * 发货地简称
         */
        private String sendSimpleName;

        /**
         * 发货省
         */
        private String sendProvince;

        /**
         * 发货市
         */
        private String sendCity;

        /**
         * 发货区
         */
        private String sendArea;

        /**
         * 发货地址（不填的话优先取发货区，然后取发货市）
         */
        private String sendAddress;

        /**
         * 发货经度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
         */
        private BigDecimal sendLng;

        /**
         * 发货纬度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
         */
        private BigDecimal sendLat;

        /**
         * 收货地简称
         */
        private String receiveSimpleName;

        /**
         * 收货省
         */
        private String receiveProvince;

        /**
         * 收货市
         */
        private String receiveCity;

        /**
         * 收货区
         */
        private String receiveArea;

        /**
         * 收货地址（不填的话优先取收货区，然后取收货市）
         */
        private String receiveAddress;

        /**
         * 收货经度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
         */
        private BigDecimal receiveLng;

        /**
         * 收货纬度（建议传入地址经纬度，这样电子围栏的判断更精准；如果不填经纬度我们也会尝试获取这个地址的经纬度）
         */
        private BigDecimal receiveLat;
    }

    /**
     * 货物信息
     */
    @Data
    public static class GoodsInfoDto {
        /**
         * 货物品类 1钢材类 2快消类 3煤炭类
         */
        private String goodsCategory;

        /**
         * 货物计量单位 TON 吨 CUBIC 方 PIECE 件
         */
        private String goodsUnit;

        /**
         * 货物重量
         */
        private BigDecimal goodsWeight;

        /**
         * 运输单价（计价方式 = 按量时必填）
         */
        private BigDecimal unitAmount;

        /**
         * 运输金额
         */
        private BigDecimal amount;

        /**
         * 货物品名
         */
        private String goodsName;
    }

    /**
     * 项目信息
     */
    @Data
    public static class ProjectStoreDto {
        /**
         * 是否新增项目（如果新增项目:则结算主体,建单费支付人,运费支付人,凭证要求,货源类型必填;如果不是新增项目:那就直接传项目名称查询即可）
         */
        private Boolean isAddProject;

        /**
         * 项目名称
         */
        private String projectName;

        /**
         * 结算主体名称（新增项目时：必填）
         */
        private String settlementName;

        /**
         * 建单费支付人 SHIPPER 货主支付 CARRIER_BROKER 承运商/经纪人支付
         */
        private String createAmountUser;

        /**
         * 司机运费支付人 SHIPPER 货主支付 CARRIER_BROKER 承运商/经纪人支付
         */
        private String driverFreightUser;

        /**
         * 凭证要求 1无要求 2需要纸质凭证 3需要电子凭证 4纸质与电子凭证均可
         */
        private Integer credentialRequest;

        /**
         * 货源类型 1平台货源 2三方货源
         */
        private Integer goodsSourceType;
    }

    /**
     * 结算信息
     */
    @Data
    public static class UpWaybillSettlementDto {
        /**
         * 必传条件：businessSource = WAYBILL && (waybillType = 2 || 3)
         * 应收计价方式 1按量 2单车/船
         * (See: 计价方式：
         * QUANTITY 按量
         * CAR 单车/船)
         */
        private String valuationType;

        /**
         * 必传条件：businessSource = WAYBILL && (waybillType = 2 || 3) && valuationTypeUp = QUANTITY
         * 应收单价
         */
        private BigDecimal receivableUnitAmount;

        /**
         * 必传条件：businessSource = WAYBILL && (waybillType = 2 || 3) && valuationTypeUp = CAR
         * 应收总额
         */
        private BigDecimal receivableAmount;
    }

}
