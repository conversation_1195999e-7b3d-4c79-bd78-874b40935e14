package com.teyuntong.outer.export.service.client.cticloud.axb.service;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;

/**
 * 天润 VNC新平台相关
 *
 * <AUTHOR>
 * @since 2024/07/17 13:13
 */
public interface VNCRpcService {

    /**
     * 获取任务列表接口
     *
     * @param recordName 必填	通话名
     * @return result 通话录音URL
     */
    @GetMapping("/vnc/getRecordUrl")
    String getRecordUrl(@RequestParam("recordName") String recordName, @RequestParam("subId") String subId) throws IOException;

    /**
     * 创建音转文任务接口
     *
     * @param callId 必填	通话唯一ID
     * @return result
     */
    @GetMapping("/vnc/asrCreate")
    void asrCreate(@RequestParam("callId") String callId, @RequestParam(value = "subId", required = false) String subId) throws IOException;

    /**
     * 获取音转文结果接口
     *
     * @param callId 必填	通话唯一ID
     * @return result
     */
    @GetMapping("/vnc/asrQuery")
    String asrQuery(@RequestParam("callId") String callId) throws IOException;

}
