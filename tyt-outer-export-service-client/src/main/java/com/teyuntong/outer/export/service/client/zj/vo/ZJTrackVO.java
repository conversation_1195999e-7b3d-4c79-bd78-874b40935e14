package com.teyuntong.outer.export.service.client.zj.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/05 19:20
 */
@Data
public class ZJTrackVO {

    /**
     * 正北方向夹角 正北，大于 0 且小于 90：东北，等于 90：正东，大于 90且小于 180：东南，等于 180：
     * 正南，大于 180 且小于 270：西南，等于 270：正西，大于 270 且小于等于 359：西北，其他：未知
     */
    private String agl;

    /**
     * GPS 时间  20210809/104650
     */
    private String gtm;

    /**
     * 海拔 米
     */
    private String hgt;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 上报里程 单位：千米
     */
    private String mlg;

    /**
     * GPS 速度 千米/小时
     */
    private String spd;

    private String formattedGtm;

    private String originalLon;

    private String originalLat;

}
