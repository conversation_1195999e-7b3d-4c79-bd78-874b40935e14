package com.teyuntong.outer.export.service.client.invoice.xhl.vo;

import lombok.Data;

/**
 * 司机实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlDriverVO {

    /**
     * appId
     */
    private String appId;

    /**
     * 驾驶员手机号
     */
    private String driverPhone;

    /**
     * 驾驶员姓名
     */
    private String driverName;

    /**
     * 驾驶员身份证号
     */
    private String idCardNo;

    /**
     * 驾驶员身份证上地址
     */
    private String address;

    /**
     * 准驾车型
     */
    private String driverType;

    /**
     * 身份证有效期开始，yyyy-MM-dd格式
     */
    private String cardStartUseDate;

    /**
     * 身份证有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    private String cardEndUseDate;

    /**
     * 驾驶证有效期开始，yyyy-MM-dd格式
     */
    private String startUseDate;

    /**
     * 驾驶证有效期结束，yyyy-MM-dd格式，若是长期，则传长期
     */
    private String endUseDate;

    /**
     * 驾驶员身份证正面照片地址
     */
    private String idCardNoFrontUrl;

    /**
     * 驾驶员身份证反面照片地址
     */
    private String idCardNoBackUrl;

    /**
     * 驾驶员驾驶证照片地址
     */
    private String drivingLicenseUrl;

    /**
     * 驾驶员驾驶证副页照片地址
     */
    private String drivingLicenseBackUrl;

    /**
     * 从业资格证照片
     */
    private String qualiCertNoUrl;

    /**
     * 从业资格证号
     */
    private String qualificationCertNo;

    /**
     * 从业资格证有效期结束，yyyy-MM-dd格式
     */
    private String qualificationCertExpireTime;

    /**
     * 驾驶证发证机关
     */
    private String issuingOrganizations;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String createTime;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 审核状态
     */
    private String checkFlag;

    /**
     * 审核原因
     */
    private String checkLog;

    /**
     *  empowerStatus 司机授权委托代征，签署状态， 1 未签订 2 已签订
     */
    private String empowerStatus;

    /**
     *  empowerStatus 司机授权委托代征，签署时间，时间戳格式
     */
    private String empowerTime;

    /**
     *  empowerStatus 司机授权委托代征，url文件地址
     */
    private String empowerUrl;

    /**
     *  每次认证的唯一标识
     */
    private String orderId;

    /**
     *  人脸认证地址
     */
    private String faceUrl;
}
