package com.teyuntong.outer.export.service.client.esign.enums.sign;

import lombok.Getter;

public enum EsignPosTypeEnum {
    /**
     * 0-坐标定位，1-关键字定位
     */
    POSITION(0, "坐标定位"),
    KEYWORD(1, "关键字定位"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    EsignPosTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

}