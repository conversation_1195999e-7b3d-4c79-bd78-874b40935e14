package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/02/28 15:03
 */
@NoArgsConstructor
@Data
public class LegalRepSignResultApiResp {

    /**
     * 签署状态
     * <p>
     * NOTSTART - 授权书签署任务已提交
     * <p>
     * SIGNING - 授权书签署通知已发送法定代表人，但未签署
     * <p>
     * FINISHED - 法定代表人完成授权书签署
     * <p>
     * SIGNFAILED - 授权书签署超过签署截止时间或法定代表人拒绝签署等导致的签署失败
     */
    private String status;
    /**
     * 签署状态描述
     */
    private String message;
}
