package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**
* 取消订单(三方)请求参数实体类
* <AUTHOR>
* @since 2024/11/29 13:55
*/
@Data
public class CancelOrderThreeRequest {


    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 用户会员代码
     */
    private String userCode;

    /**
     * 运单No
     */
    private String waybillCode;

    /**
     * 取消原因
     */
    private String cancelReason;



}