package com.teyuntong.outer.export.service.client.corporate.service;

import com.teyuntong.outer.export.service.client.corporate.vo.CorporateBaseInfoBean;
import com.teyuntong.outer.export.service.client.corporate.vo.IcBasicInfoNormalResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;

/**
 * 企业工商基本信息查询
 *
 * <AUTHOR>
 * @since 2024/12/05 21:26
 */
public interface CorporateBaseInfoRpcService {

    /**
     * 企业基本信息查询：搜索关键字（公司名称、公司id、注册号或社会统一信用代码
     *
     * @param keyword
     * @return
     */
    @GetMapping(produces = "application/json;charset=utf-8", path = "/corporate")
    CorporateBaseInfoBean getCorporate(@RequestParam(value = "keyword") String keyword);

    /**
     * 企业基本信息
     * <p>
     * <a href="https://open.tianyancha.com/open/1116">接口文档地址</a>
     * <p>
     * curl "localhost:8094/tian_yan_cha/ic/baseinfo/normal?keyword=%E4%B8%AD%E8%88%AA%E9%87%8D%E6%9C%BA%E8%82%A1%E4
     * %BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8"
     *
     * @param keyword 关键词
     * @return 用户信息
     */
    @GetMapping("/tian_yan_cha/ic/baseinfo/normal")
    IcBasicInfoNormalResp getIcBasicInfoNormal(@RequestParam(value = "keyword") String keyword) throws IOException;
}
