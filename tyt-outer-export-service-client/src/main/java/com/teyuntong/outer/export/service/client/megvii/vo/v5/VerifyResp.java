package com.teyuntong.outer.export.service.client.megvii.vo.v5;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/11 14:08
 */
@NoArgsConstructor
@Data
public class VerifyResp {

    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("biz_no")
    private String bizNo;
    @JsonProperty("time_used")
    private Integer timeUsed;
    @JsonProperty("user_faced_time")
    private String userFacedTime;
    @JsonProperty("result_code")
    private Integer resultCode;
    @JsonProperty("result_message")
    private String resultMessage;
    @JsonProperty("verification")
    private VerificationDTO verification;
    @JsonProperty("attack_result")
    private AttackResultDTO attackResult;
    @JsonProperty("device_risk_info")
    private DeviceRiskInfoDTO deviceRiskInfo;
    @JsonProperty("images")
    private ImagesDTO images;
    @JsonProperty("video_key")
    private String videoKey;
    @JsonProperty("face")
    private FaceDTO face;
    /**
     * 当请求失败时才会返回此字符串，具体返回内容见后续错误信息章节，否则此字段不存在
     */
    private String error;

    @NoArgsConstructor
    @Data
    public static class VerificationDTO {
        @JsonProperty("idcard")
        private IdcardDTO idcard;

        @NoArgsConstructor
        @Data
        public static class IdcardDTO {
            @JsonProperty("confidence")
            private Double confidence;
            @JsonProperty("thresholds")
            private ThresholdsDTO thresholds;

            @NoArgsConstructor
            @Data
            public static class ThresholdsDTO {
                @JsonProperty("1e-3")
                private Double $1e3;
                @JsonProperty("1e-5")
                private Double $1e5;
                @JsonProperty("1e-4")
                private Double $1e4;
                @JsonProperty("1e-6")
                private Double $1e6;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class AttackResultDTO {
        @JsonProperty("score")
        private Double score;
        @JsonProperty("threshold")
        private Double threshold;
        @JsonProperty("result")
        private Boolean result;
    }

    @NoArgsConstructor
    @Data
    public static class DeviceRiskInfoDTO {
        @JsonProperty("device_info_level")
        private String deviceInfoLevel;
        @JsonProperty("device_info_tags")
        private DeviceInfoTagsDTO deviceInfoTags;

        @NoArgsConstructor
        @Data
        public static class DeviceInfoTagsDTO {
            @JsonProperty("is_root")
            private Integer isRoot;
            @JsonProperty("is_hook")
            private Integer isHook;
            @JsonProperty("is_injection")
            private Integer isInjection;
            @JsonProperty("is_virtual_environment")
            private Integer isVirtualEnvironment;
            @JsonProperty("is_other_risks")
            private Integer isOtherRisks;
        }
    }

    @NoArgsConstructor
    @Data
    public static class ImagesDTO {
        @JsonProperty("image_best")
        private String imageBest;
    }

    @NoArgsConstructor
    @Data
    public static class FaceDTO {
        @JsonProperty("quality")
        private Double quality;
        @JsonProperty("quality_threshold")
        private Double qualityThreshold;
        @JsonProperty("rect")
        private RectDTO rect;
        @JsonProperty("orientation")
        private Integer orientation;

        @NoArgsConstructor
        @Data
        public static class RectDTO {
            @JsonProperty("left")
            private Double left;
            @JsonProperty("top")
            private Double top;
            @JsonProperty("width")
            private Double width;
            @JsonProperty("height")
            private Double height;
        }
    }
}
