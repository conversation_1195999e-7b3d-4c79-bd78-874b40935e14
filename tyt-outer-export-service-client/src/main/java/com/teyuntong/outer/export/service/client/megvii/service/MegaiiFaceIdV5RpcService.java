package com.teyuntong.outer.export.service.client.megvii.service;

import com.teyuntong.outer.export.service.client.megvii.vo.v5.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;

/**
 * 旷世人脸识别-v5版本
 *
 * <AUTHOR>
 * @since 2024/12/10 10:37
 */
public interface MegaiiFaceIdV5RpcService {

    /**
     * 获取业务token
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping("/megaii/face_id/get_biz_token")
    BizTokenV5Resp getBizToken(@RequestBody @Validated GetBizTokenV5Req req) throws IOException;

    /**
     * 活体验证（仅活体不比对）
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping("/megaii/face_id/liveness")
    LivenessResp liveness(@RequestBody @Validated WebLivenessReq req) throws IOException;

    /**
     * 比对认证
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping("/megaii/face_id/verify")
    VerifyResp verify(@RequestBody @Validated WebVerifyReq req) throws IOException;
}
