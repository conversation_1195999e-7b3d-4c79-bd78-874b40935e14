package com.teyuntong.outer.export.service.client.person.VO;

import com.wlqq.wallet.gateway.client.enums.AccountTypeKind;
import com.wlqq.wallet.gateway.client.enums.IdentityType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QueryMemberAccountReqVO extends CommonVO {
    @NotNull(message = "会员标识号不能为空")
    private String identityNo;

    @NotNull(message = "标识类型不能为空")
    private IdentityType identityType;

    /**
     * 会员账户
     */
    private AccountTypeKind accountType;
}
