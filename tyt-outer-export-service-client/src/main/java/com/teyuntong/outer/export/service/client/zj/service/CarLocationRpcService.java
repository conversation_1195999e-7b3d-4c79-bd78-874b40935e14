package com.teyuntong.outer.export.service.client.zj.service;

import com.teyuntong.outer.export.service.client.zj.vo.ZJHistoryTrackResultVO;
import com.teyuntong.outer.export.service.client.zj.vo.ZJVehicleLocationResultVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/11/06 11:08
 */
public interface CarLocationRpcService {

    @GetMapping(path = "/rpc/zj/location/getCarRealTimeLocation")
    ZJVehicleLocationResultVO getCarRealTimeLocation(@RequestParam(value = "carHeadNo") String carHeadNo, @RequestParam(value = "color",required = false) String color);


    @GetMapping(path = "/rpc/zj/location/getCarLocus")
    ZJHistoryTrackResultVO getCarLocus(@RequestParam(value = "carHeadNo") String carHeadNo, @RequestParam(value = "color",required = false) String color, @RequestParam(value = "beginTime") String beginTime, @RequestParam(value = "endTime") String endTime);
}
