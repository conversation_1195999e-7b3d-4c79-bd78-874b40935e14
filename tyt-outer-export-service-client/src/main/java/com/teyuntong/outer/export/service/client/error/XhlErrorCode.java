package com.teyuntong.outer.export.service.client.error;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum XhlErrorCode implements ErrorCodeBase {

    XHL_SUCCESS("1000", "请求成功", "warn", false),
    XHL_SIGN_INVALID_ERROR("401", "访问校验失败：无效的签名！", "warn", false),
    XHL_ACCOUNT_LACK_ERROR("1010", "托运人账号缺失", "warn", false),
    XHL_SYSTEM_ERROR("99", "系统处理出错了，请联系客服！", "warn", false),
    XHL_UNREGISTERED_VIRTUAL_ACCOUNT_ERROR("1091", "未注册虚户", "warn", false),
    XHL_DRIVER_ID_CARD_NO_ERROR("1040", "驾驶员身份证错误", "warn", false),
    XHL_PARAM_LACK_ERROR("1020", "参数缺失", "warn", false),
    XHL_IMAGE_READ_ERROR("1050", "读取图片失败", "warn", false),
    XHL_COMPANY_AUDIT_ERROR("1090", "托运人审核未通过", "warn", false),
    XHL_SEND_ORDER_ERROR("1070", "派单异常", "warn", false),
    XHL_NOT_FOUND_ERROR("1091", "未查到数据", "warn", false),

    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;

    public static ErrorCodeBase buildErrorCode(String code, String msg) {
        return new ErrorCodeBase() {
            @Override
            public String getCode() {
                return code;
            }
            @Override
            public String getMsg() {
                return msg;
            }
            @Override
            public boolean isSuccess() {
                return false;
            }
        };
    }
}
