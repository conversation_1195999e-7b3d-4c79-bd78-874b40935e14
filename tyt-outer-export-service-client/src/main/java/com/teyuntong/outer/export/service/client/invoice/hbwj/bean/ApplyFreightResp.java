package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 运费申请响应
 * <AUTHOR>
 * @since 2024/07/17 16:15
 */
@Data
public class ApplyFreightResp {

    /**
     *运单编号
     */
    private String waybillCode;

    /**
     * 费用类别 1：预付款 2：运费 3：订船费 4：出航费 5：油卡金额 6：建单费
     * (See: 费用类别
     * PREPAYMENT 预付款
     * FREIGHT 运费
     * BOOKING_FEE 订船费
     * SAILING_FEE 出航费
     * FUELCARDAMOUNT 油卡金额
     * CREATEWAYBILL 建单费
     * COMMISSION 佣金提现
     * POSITION 定位费
     * CUSTOMER 保险费)
     */
    private String feeType;

    /**
     *费用金额
     */
    private BigDecimal feeAmount;

    /**
     * 申请金额
     */
    private BigDecimal appliedAmount;


    /**
     * 收款方手机号
     */
    private String payeePhone;

    /**
     * 收款方类型(1-本人,2-一般代收人,3-车老板)
     * (See: 收款方类型
     * SELF 本人
     * GENERAL_COLLECTOR 一般代收人
     * CAR_BOSS 车老板
     * CARRIER_BROKER 承运商
     * ECONOMIC_MAN 经济人
     * PLATFORM 平台代开银行)
     */
    private String payeeType;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 账户名称
     */
    private String bankAccountName;

    /**
     * 收款人身份证号
     */
    private String idCardNo;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 支付单号（申请成功才会返回）
     */
    private String paymentNo;

    /**
     * 申请结果
     */
    private Long applyResult;

    /**
     * 申请错误描述BigDecimal
     */
    private String applyMsg;

    /**
     * 总支付金额
      */
    private BigDecimal paymentAmount;

    /**
     * 运费差价
     */
    private BigDecimal rateAmount;

}
