package com.teyuntong.outer.export.service.client.esign.vo.sign;

import com.teyuntong.outer.export.service.client.esign.enums.sign.SignSealTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class MultiPositionSignPDF3rdReq {
    /**
     * 签署者账号标识，以此获取账户的证书进行签署
     */
    @NotNull
    private String accountId;
    /**
     * 签章类型
     * <p>
     * （1）Single，单页签章
     * <p>
     * （2）Multi，多页签章
     * <p>
     * （3）Edges，签骑缝章
     * <p>
     * （4）Key，关键字签章
     */
    @NotNull
    private SignSealTypeEnum signType;
    /**
     * 接收短信验证码的手机
     */
    @NotNull
    private String mobile;
    /**
     * 短信验证码，必须通过e签宝接口发送
     */
    @NotNull
    private String code;
    /**
     * 印章图片Base64
     * <p>
     * 注意：印章数据要和账户信息对应，否则会出现签署的印章和背后的数字证书不一致的情况，从而造成签署纠纷
     */
    private String sealData;
    /**
     * 签署PDF文档信息
     */
    private LocalSafeSignPDF3rdPdfReq pdfInfo = new LocalSafeSignPDF3rdPdfReq();
    /**
     * 签章位置信息
     */
    private List<LocalSafeSignPDF3rdPosReq> posInfoList;

    @Data
    public static class LocalSafeSignPDF3rdPdfReq {
        /**
         * pdf文件 base64 编码后的数据
         */
        @NotNull
        private String fileBase64;
        /**
         * 文档名称，e签宝签署日志对应的文档名，若为空则取文档路径中的名称
         */
        private String fileName;
        /**
         * 文档编辑密码，当目标PDF设置权限保护时必填
         */
        private String ownerPassword;
        /**
         * 文件标识
         */
        private String markBit;
    }

    @Data
    public static class LocalSafeSignPDF3rdPosReq {
        /**
         * 定位类型，0-坐标定位，1-关键字定位，默认0，SignType为关键字签署的时候，为1，否则为0。用户可以不作处理。此处只是为了兼容旧版本而保留
         */
        private int posType;
        /**
         * 签署页码，若为多页签章，支持页码格式“1-3,5,8“，若为坐标定位时，不可空
         */
        private String posPage;
        /**
         * 签署位置X坐标，若为关键字定位，相对于关键字的X坐标偏移量，默认0
         */
        private float posX;
        /**
         * 签署位置Y坐标，若为关键字定位，相对于关键字的Y坐标偏移量，默认0
         */
        private float posY;
        /**
         * 关键字，仅限关键字签章时有效，若为关键字定位时，不可空；关键字建议不要设置特殊字符，因Adobe无法识别部分符号，某些特殊字符会因解析失败从而导致搜索不到
         */
        private String key;
        /**
         * 印章展现宽度，将以此宽度对印章图片做同比缩放。
         * <p>
         * 1)为空：
         * <p>
         * 若图片宽度超过159，印章宽度等比缩小至159；
         * <p>
         * 若图片宽度小于159，印章尺寸默认以图片大小为准；
         * <p>
         * 2) 不为空
         * <p>
         * 印章宽度以传入参数为准。
         */
        private float width;
        /**
         * 是否是 二维码签署，默认为false。二维码签署不支持骑缝签和多页签
         */
        private boolean qrCodeSign;
        /**
         * 是否是作废签签署，默认为false
         */
        private boolean cancellingSign;
        /**
         * 是否显示本地签署时间，需要width设置92以上才可以看到时间
         */
        private boolean addSignTime;
    }
}
