package com.teyuntong.outer.export.service.client.location.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/17 13:47
 */
@Data
public class VehicleLocationRpcVO {

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 车牌颜色
     */
    private String vehicleColor;
    private String gridCode;
    private double lonWgs;
    private double latWgs;

    /**
     * 火星坐标系经度
     */
    private double lonGcj;
    /**
     * 火星坐标系纬度
     */
    private double latGcj;
    private int speed;
    private Integer mileage;
    private int altitude;
    private int direction;
    /**
     * 定位时间，毫秒时间戳
     */
    private long positionTime;

    /**
     * 定位来源，1-北斗兴路 4-北斗信捷 8-车载盒子
     */
    private int dataSource;

    /**
     * 详细地址
     */
    private String formattedAddress;

    private String town;
    private String street;
    private String streetNumber;

    /**
     * 定位省份ID
     */
    private int provinceId;

    /**
     * 定位城市ID
     */
    private int cityId;

    /**
     * 定位区县ID
     */
    private int districtId;

    private String provinceName;
    private String cityName;
    private String districtName;
}
