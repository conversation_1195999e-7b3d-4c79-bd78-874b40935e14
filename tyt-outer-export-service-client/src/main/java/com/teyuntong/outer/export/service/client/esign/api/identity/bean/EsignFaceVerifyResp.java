package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:37
 */
@Data
public class EsignFaceVerifyResp {

    /**
     * 否
     * 实名流程Id
     */
    private String flowId;

    /**
     * 否
     * e签宝短链接地址
     */
    private String authUrl;

    /**
     * 否
     * 刷脸原始地址
     */
    private String originalUrl;

    /**
     * 否
     * 链接失效时间,毫秒值
     */
    private Long expire;

}
