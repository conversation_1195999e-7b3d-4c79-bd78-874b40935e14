package com.teyuntong.outer.export.service.client.sensitivewords;

import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface SensitiveWordsRpcService {

    /**
     *  集团判断是否命中敏感词接口
     *
     * @param userId userId
     * @param taskContent taskContent
     * @param machineRemark machineRemark
     * @return SensitiveWordsVO
     * <AUTHOR>
     */
    @GetMapping(path = "/sensitiveCheck/cargoSensitiveCheck")
    SensitiveWordsVO newCargoSensitiveCheck(@RequestParam(value = "userId", required = true) Long userId
            , @RequestParam(value = "taskContent", required = false) String taskContent
            , @RequestParam(value = "machineRemark", required = false) String machineRemark);

}
