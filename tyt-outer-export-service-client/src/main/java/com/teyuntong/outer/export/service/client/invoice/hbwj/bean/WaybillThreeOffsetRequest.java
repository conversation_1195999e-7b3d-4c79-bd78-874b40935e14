package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
* 运单回调详情(三方)请求参数实体类
* <AUTHOR>
* @since 2024/7/18 14:59
*/
@Data
public class WaybillThreeOffsetRequest {

    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 用户会员代码
     */
    private String userCode;

    /**
     * 运单No
     */
    private String waybillCode;

}