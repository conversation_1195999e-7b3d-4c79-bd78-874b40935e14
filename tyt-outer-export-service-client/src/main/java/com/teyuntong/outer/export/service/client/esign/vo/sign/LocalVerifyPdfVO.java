package com.teyuntong.outer.export.service.client.esign.vo.sign;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/02/29 17:26
 */
@NoArgsConstructor
@Data
public class LocalVerifyPdfVO {
    /**
     * PDF文件中所有签名信息
     */
    private List<SignaturesDTO> signatures;

    @NoArgsConstructor
    @Data
    public static class SignaturesDTO {
        /**
         * 签名使用的证书数据
         */
        private CertDTO cert;
        /**
         * 签名使用的印章图片数据,BASE64格式
         */
        private String sealData;
        /**
         * 签名使用的印章名称
         */
        private String sealName;
        /**
         * 签名数据
         */
        private SignatureDTO signature;

        @NoArgsConstructor
        @Data
        public static class CertDTO {
            /**
             * 证书所有者名称
             */
            private String cn;
            /**
             * 证书有效期结束时间，yyyy-MM-dd格式
             */
            private String endDate;
            /**
             * 证书有效期开始时间，yyyy-MM-dd格式
             */
            private String issuerCN;
            /**
             * 证书序列号
             */
            private String sn;
            /**
             * 证书有效期开始时间，yyyy-MM-dd格式
             */
            private String startDate;
        }

        @NoArgsConstructor
        @Data
        public static class SignatureDTO {
            /**
             * 文档签署时间，满足yyyy-MM-dd HH:mm:ss格式的日期时间字符串
             */
            private String signDate;
            /**
             * 签名在PDF中的名称
             */
            private String signatureName;
            /**
             * 签名数据来源，tsa-时间戳服务器，local-本地计算机
             */
            private String timeFrom;
            /**
             * 该签名的验证结果:true成功,false失败
             */
            private Boolean validate;
        }
    }
}
