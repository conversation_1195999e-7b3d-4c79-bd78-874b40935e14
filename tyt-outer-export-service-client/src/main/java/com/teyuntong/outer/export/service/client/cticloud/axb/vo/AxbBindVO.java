package com.teyuntong.outer.export.service.client.cticloud.axb.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/14 上午10:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AxbBindVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 成功绑定的A电话
     */
    private String telA;

    /**
     * 成功绑定的B电话
     */
    private String telB;

    /**
     * 成功绑定的X电话
     */
    private String telX;

    /**
     * 有效期，0为永久有效
     */
    private Date expirationDate;

    /**
     * 是否永久有效
     */
    private Boolean isPermanent;

    /**
     * 自定义字段
     */
    private String extraField;
}
