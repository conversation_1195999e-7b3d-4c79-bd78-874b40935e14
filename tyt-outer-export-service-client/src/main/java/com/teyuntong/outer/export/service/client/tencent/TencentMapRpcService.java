package com.teyuntong.outer.export.service.client.tencent;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 腾讯地图
 *
 * <AUTHOR>
 * @since 2025/07/25 09:42
 */
public interface TencentMapRpcService {

    /**
     * 腾讯货车导航
     */
    @PostMapping("/rpc/tencent/navigation/truck")
    String navigationTruck(@RequestBody @Validated DistanceRpcDTO dto);
}
