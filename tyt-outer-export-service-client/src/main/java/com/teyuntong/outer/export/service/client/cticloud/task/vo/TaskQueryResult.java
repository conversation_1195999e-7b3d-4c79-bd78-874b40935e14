package com.teyuntong.outer.export.service.client.cticloud.task.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18 10:15
 */
@NoArgsConstructor
@Data
public class TaskQueryResult implements Serializable {

    /**
     * 任务配置信息
     */
    private List<TaskProperty> taskProperties;

    @Data
    public static class TaskProperty implements Serializable {

        /**
         * 预测式外呼编号
         */
        @JsonProperty("id")
        private String id;

        /**
         * 企业编号
         */
        @JsonProperty("enterpriseId")
        private String enterpriseId;

        /**
         * 任务名称
         */
        @JsonProperty("name")
        private String name;
        /**
         * 任务描述
         */
        @JsonProperty("description")
        private String description;
        /**
         * 任务状态，0初始 1运行中 2暂停 3结束
         */
        @JsonProperty("status")
        private String status;
        /**
         * 任务类型，1.转呼叫组 2.转IVR
         */
        @JsonProperty("type")
        private String type;
        /**
         * 任务开始时间
         */
        @JsonProperty("startTime")
        private String startTime;
        /**
         * 任务结束时间
         */
        @JsonProperty("overTime")
        private String overTime;
        /**
         * 客户超时时间
         */
        @JsonProperty("customerTimeout")
        private String customerTimeout;
        /**
         * 座席超时时间
         */
        @JsonProperty("agentTimeout")
        private String agentTimeout;
        /**
         * 座席工号列表
         */
        @JsonProperty("cnos")
        private String cnos;
        /**
         * 语音导航id
         */
        @JsonProperty("ivrId")
        private Object ivrId;
        /**
         * 是否自动开始 1自动开始，0不自动开始
         */
        @JsonProperty("autoStart")
        private String autoStart;
        /**
         * 自动开始日期，如 2017-02-11
         */
        @JsonProperty("autoStartDay")
        private String autoStartDay;
        /**
         * 自动开始时间，如 08:00:00
         */
        @JsonProperty("autoStartTime")
        private String autoStartTime;
        /**
         * 是否自动结束 1自动结束，0不自动结束
         */
        @JsonProperty("autoStop")
        private String autoStop;
        /**
         * 自动结束日期，如 2017-02-11
         */
        @JsonProperty("autoStopDay")
        private String autoStopDay;
        /**
         * 自动结束时间，如 21:00:00
         */
        @JsonProperty("autoStopTime")
        private String autoStopTime;
        /**
         * 并发通道数，type=2时使用
         */
        @JsonProperty("concurrency")
        private String concurrency;
        /**
         * 系数(骚扰率)，整数，type=1时使用，取值在3~20，默认10
         */
        @JsonProperty("quotiety")
        private String quotiety;
        /**
         * 座席整理时间，默认30秒
         */
        @JsonProperty("wrapup")
        private String wrapup;
        /**
         * 是否随机呼叫，0不随机 1随机
         */
        @JsonProperty("isRandom")
        private String isRandom;
        /**
         * 客户侧外显号码列表
         */
        @JsonProperty("customerClids")
        private String customerClids;

        /**
         * 热线号码组
         */
        @JsonProperty("clidGroup")
        private String clidGroup;
        /**
         * 座席最大等待时间，取值在10~600，默认40
         */
        @JsonProperty("maxWaitTime")
        private String maxWaitTime;
        /**
         * 初始化预计客户接通率
         */
        @JsonProperty("answerRate")
        private String answerRate;
        /**
         * 默认超呼率100 最大200，最小50
         */
        @JsonProperty("predictAdjust")
        private String predictAdjust;
        /**
         * 号码拨打完后任务是否自动完成，0.不自动完成（任务自动暂停） 1.自动完成
         */
        @JsonProperty("autoComplete")
        private Integer autoComplete;
        /**
         * 客户侧等待音,转呼叫组(type=1)生效,如60000011535357097940
         */
        @JsonProperty("customerMoh")
        private String customerMoh;
        /**
         * 任务创建时间 ，格式为： yyyy-MM-dd HH:mm:ss
         */
        @JsonProperty("createTime")
        private String createTime;
        /**
         * 自定义字段
         */
        @JsonProperty("userFields")
        private String userFields;
    }
}
