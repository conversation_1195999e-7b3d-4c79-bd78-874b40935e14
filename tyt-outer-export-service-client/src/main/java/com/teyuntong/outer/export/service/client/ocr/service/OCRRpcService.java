package com.teyuntong.outer.export.service.client.ocr.service;

import com.teyuntong.outer.export.service.client.ocr.vo.BusinessLicenseOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportBackOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportQuaCertOcrRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.VehicleLicenseDeputyPageBackOcrRpcVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 集团OCR RPC接口类
 *
 * <AUTHOR>
 * @since 2024-4-2 15:04:08
 */
public interface OCRRpcService {

    /**
     * 道运证OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    @GetMapping(path = "/ocr/roadTransportBackOcr")
    RoadTransportBackOcrRpcVO roadTransportBackOcr(@RequestParam("url")String url);

    /**
     * 行驶证副页背面OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    @GetMapping(path = "/ocr/vehicleLicenseDeputyPageBackOcr")
    VehicleLicenseDeputyPageBackOcrRpcVO vehicleLicenseDeputyPageBackOcr(@RequestParam("url")String url);


    /**
     * 道路运输从业资格证主页OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    @GetMapping(path = "/ocr/roadTransportQuaCertOcr")
    RoadTransportQuaCertOcrRpcVO roadTransportQualificationCertificateOcr(@RequestParam("url")String url);

    /**
     * 道路运输从业资格证副页OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    @GetMapping(path = "/ocr/roadTransportQualificationCertificateBackOcr")
    RoadTransportQuaCertOcrRpcVO roadTransportQualificationCertificateBackOcr(@RequestParam("url")String url);

    /**
     * 营业执照OCR
     *
     * @param url ocr图片链接
     * @return OCR结果
     */
    @GetMapping(path = "/ocr/businessLicenseOcr")
    BusinessLicenseOcrRpcVO businessLicenseOcr(@RequestParam("url") String url);
}
