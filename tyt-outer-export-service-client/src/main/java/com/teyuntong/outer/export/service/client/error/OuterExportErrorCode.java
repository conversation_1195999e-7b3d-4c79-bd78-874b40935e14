package com.teyuntong.outer.export.service.client.error;

import com.teyuntong.outer.export.service.client.model.CustomErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特运通错误码ErrorCode
 * <p>
 * （1）总共8位，1 2 位保留
 * （2）3 4 位代表具体的子模块
 * （3）5 6 7 8 位表示具体的业务
 * <p>
 */
@AllArgsConstructor
@Getter
public enum OuterExportErrorCode implements CustomErrorCode {
    /**
     * outer错误码 00112000 开始
     */
    BUSINESS_ERROR("00990000", "内部错误", "warn", false),
    PARAM_LACK_ERROR("00112001", "缺少参数", "warn", false),



    /**
     * 外部系统错误码
     */
    ESIGN_ERROR("901001", "e签宝api错误", "warn", false),
    MANBANG_ERROR("901002", "接口请求错误", "warn", false),

    WJ_ERROR("901002", "接口请求错误", "warn", false),

    /**
     * common api 旧编码 interface
     */
    COMMON_API_INTERFACE_ERROR("500", "三方接口请求失败", "warn", false),
    COMMON_TASK_FAIL_ERROR("10001", "创建音转文任务失败", "warn", false),
    COMMON_AXB_BIND_FAIL_ERROR("10000", "axb绑定未成功", "warn", false),
    COMMON_AXB_UPDATE_FAIL_ERROR("10000", "axb修改未成功", "warn", false),
    COMMON_AXB_DELETE_FAIL_ERROR("10000", "axb删除未成功", "warn", false),
    COMMON_AXB_GET_FAIL_ERROR("10000", "获取话单请求失败", "warn", false),
    COMMON_LACK_ERROR("100001", "缺少参数", "warn", false),
    COMMON_AUTO_CALL_TIME_ERROR("100002", "未到工作开始时间，不可自动外呼", "warn", false),
    COMMON_AUTO_CALL_CREATE_ERROR("99999", "自动外呼任务创建失败", "warn", false),
    COMMON_AUTO_CALL_STATUS_ERROR("500", "获取已结束的自动外呼任务失败", "warn", false),
    COMMON_AXB_BIND_NOT_EXIST_ERROR("500", "绑定信息不存在", "warn", false),
    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;
}
