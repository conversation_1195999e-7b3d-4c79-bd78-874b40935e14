package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import com.teyuntong.outer.export.service.client.invoice.hbwj.enums.ValuationType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public  class UpWaybillSettlementDto {
    /**
     * 应收计价方式 1按量 2单车/船
     * QUANTITY 按量
     * CAR 单车/船
     */
    private ValuationType valuationType;

    /**
     * 应收单价
     */
    private BigDecimal receivableUnitAmount;

    /**
     * 应收总额
     */
    private BigDecimal receivableAmount;
}