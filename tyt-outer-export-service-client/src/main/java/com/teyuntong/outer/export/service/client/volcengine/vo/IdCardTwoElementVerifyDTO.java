package com.teyuntong.outer.export.service.client.volcengine.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/01/10 17:02
 */
@Data
public class IdCardTwoElementVerifyDTO {

    /**
     * 待查询身份证姓名，目前只支持明文，如果姓名中带"·"的，建议使用英文半角点
     */
    @NotBlank(message = "idCardName 不能为空")
    private String idCardName;

    /**
     * 待查询身份证号，目前只支持明文
     */
    @NotBlank(message = "idCardNo 不能为空")
    private String idCardNo;

}