package com.teyuntong.outer.export.service.client.ocr.vo;

import lombok.Data;

/**
 * 道路运输证验真信息结果类
 *
 * <AUTHOR>
 * @since 2024/04/26 14:22
 */
@Data
public class RoadTransportVerifyInfoRpcVO {

    /**
     * 车牌号码
     */
    private String vehicleNo;
    /**
     * 道路运输证号
     */
    private String transCertificateCode;
    /**
     * 经营业户名称
     */
    private String ownerName;
    /**
     * 所属业户经营许可证号
     */
    private String licenseCode;
    /**
     * 企业经营状态
     */
    private String operatingStatus;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 车辆车长
     */
    private String vehicleLength;
    /**
     * 车辆车宽
     */
    private String vehicleWidth;
    /**
     * 车辆车高
     */
    private String vehicleHigh;
    /**
     * 有效期起
     */
    private String certificateBeginDate;
    /**
     * 有效期止
     */
    private String certificateExpireDate;
    /**
     * 车辆经营范围代码
     */
    private String businessScopeCode;
}
