package com.teyuntong.outer.export.service.client.esign.api.identity.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:37
 */
@Data
public class EsignFaceVerifyReq {

    /**
     * 姓名
     */
    @NotNull
    private String name;

    /**
     * 身份证号
     */
    @NotNull
    private String idNo;

    /**
     * 是
     * 人脸认证方式
     * TENCENT  腾讯微众银行认证
     * ZHIMACREDIT  支付宝芝麻信用认证
     * ESIGN  e签宝刷脸
     */
    @NotNull
    private String faceauthMode;

    /**
     * 是
     * 认证结束后页面跳转地址
     */
    @NotNull
    private String callbackUrl;

    /**
     * 否
     * 对接方业务上下文id，将在异步通知及跳转时携带返回对接方，最大支持500个字符
     */
    private String contextId;

    /**
     * 否
     * 认证结束后异步通知地址
     */
    private String notifyUrl;

}
