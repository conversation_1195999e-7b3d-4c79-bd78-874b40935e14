package com.teyuntong.outer.export.service.client.invoice.hbwj.enums;


/**
* 运单状态：WAIT_TAKE_ORDER待接单 WAIT_DEPARTURE待起运 WAIT_ARRIVE运输中 HAS_FINISH已送达 HAS_CANCEL
* <AUTHOR>
* @since 2024/12/13 13:18
*/
public enum WayBillStatusEnum {

    WAIT_TAKE_ORDER("WAIT_TAKE_ORDER", "待接单"),
    WAIT_DEPARTURE("WAIT_DEPARTURE", "待起运"),
    WAIT_ARRIVE("WAIT_ARRIVE", "运输中"),
    HAS_FINISH("HAS_FINISH", "已送达"),
    HAS_CANCEL("HAS_CANCEL", "已取消");

    private String code;
    private String desc;

    WayBillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
