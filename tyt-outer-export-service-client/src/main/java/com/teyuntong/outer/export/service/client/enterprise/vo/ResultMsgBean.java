package com.teyuntong.outer.export.service.client.enterprise.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.Date;

/**
 * User: Administrator
 * Date: 19-12-24
 * Time: 下午5:57
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultMsgBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**** 正确结果 **/
    public static final int OK = 200;
    public static final String OK_MSG = "操作成功";


    /**** 错误结果 **/
    public static final int ERROR = 500;

    private int code = OK;
    private String msg;
    private Object data;
    private Date timestamp;
    //为了扩展兼容以前的接口，额外增加的字段
    private Object extraData;

    public ResultMsgBean() {
    }

    public ResultMsgBean(int code, String msg) {
        super();
        this.code = code;
        this.msg = msg;
    }

    public ResultMsgBean(int code, String msg, Object data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    /**
     * @return the data
     */
    public Object getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(Object data) {
        this.data = data;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
