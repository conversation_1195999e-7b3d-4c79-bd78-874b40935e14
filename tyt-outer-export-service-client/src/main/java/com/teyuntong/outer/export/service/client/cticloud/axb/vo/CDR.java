package com.teyuntong.outer.export.service.client.cticloud.axb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CDR {

    private String billDuration;

    private String billDurationMin;

    private String callId;

    private String callRecording;

    private String callTime;

    private String callType;

    private String createTime;

    private String pushType;

    private String recordMode;

    private String recordName;

    private String recordUrl;

    private String releaseCause;

    private String releaseDir;

    private String releaseTime;

    private String requestId;

    private String result;

    private String ringingTime;

    private String serviceType;

    private String startTime;

    private String subId;

    private String telA;

    private String telB;

    private String telX;

    private String totalDuration;

    private String userField;

    private Long srcMsgId;

    private Long carUserId;

}