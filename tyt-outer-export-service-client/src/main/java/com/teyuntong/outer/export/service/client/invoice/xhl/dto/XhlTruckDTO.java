package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 翔和翎 车辆实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlTruckDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空", groups = {XhlTruckDTO.AddInfo.class, XhlTruckDTO.UpdateInfo.class})
    private String plateNumber;

    /**
     * 车辆类型，行驶证上的车辆类型
     */
    @NotBlank(message = "车辆类型不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String truckType;

    /**
     * 车长，单位mm
     */
    @NotBlank(message = "车长不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String length;

    /**
     * 车宽，单位mm
     */
    @NotBlank(message = "车宽不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String width;

    /**
     * 车高，单位mm
     */
    @NotBlank(message = "车高不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String height;

    /**
     * 行驶证照片地址
     */
    @NotBlank(message = "行驶证照片地址不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String vehicleLicenseUrl;

    /**
     * 行驶证副页
     */
    @NotBlank(message = "行驶证副页不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String vehicleLicenseBackUrl;

    /**
     * 行驶证年审页
     */
    @NotBlank(message = "行驶证年审页不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String vehicleLicenseCheckUrl;

    /**
     * 行驶证注册日期，格式yyyy-MM-dd
     */
    @NotBlank(message = "行驶证注册日期不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String regDate;

    /**
     * 强制报废日期，格式yyyy-MM-dd
     */
    @NotBlank(message = "强制报废日期不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String scrapDate;

    /**
     * 行驶证发证日期，格式yyyy-MM-dd
     */
    @NotBlank(message = "行驶证发证日期不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String issueDate;

    /**
     * 道路运输证号
     */
    @NotBlank(message = "道路运输证号不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String optCertNo;

    /**
     * 道路运输照片地址
     */
    @NotBlank(message = "道路运输照片地址不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String optCertNoUrl;

    /**
     * 道路运输证失效日期，格式yyyy-MM-dd
     */
    @NotBlank(message = "道路运输证失效日期不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String optCertExpireTime;

    /**
     * 车牌颜色（1蓝色，2黄色，3黑色，4白色，5绿色，9其他，91农黄色，92农绿色，93黄绿色，94渐变绿）
     */
    @NotBlank(message = "车牌颜色不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String plateColor;

    /**
     * 车辆所有人，车辆行驶证上的所有人
     */
    @NotBlank(message = "车辆所有人不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String ownerName;

    /**
     * 是否挂靠（1挂靠、2自有）
     */
    @NotBlank(message = "是否挂靠不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String isPersonal;

    /**
     * 能源类型，A汽油，B柴油，C电，D混石油，E天然气，F液化石油气，L甲醇，M乙醇，N太阳能
     */
    @NotBlank(message = "能源类型不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String energyType;

    /**
     * 总质量，单位kg，若没有传0
     */
    @NotBlank(message = "总质量不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String totalWeight;

    /**
     * 整备质量，单位kg，若没有传0
     */
    @NotBlank(message = "整备质量不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String curbWeight;

    /**
     * 核定载质量，单位kg，若没有传0
     */
    @NotBlank(message = "核定载质量不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String approveWeight;

    /**
     * 牵引总质量，单位kg，若没有传0
     */
    @NotBlank(message = "牵引总质量不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String tractionWeight;

    /**
     * 使用性质
     */
    @NotBlank(message = "使用性质不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String useCharacter;

    /**
     * 车辆识别代号
     */
    @NotBlank(message = "车辆识别代号不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String vin;

    /**
     * 行驶证发证机关
     */
    @NotBlank(message = "行驶证发证机关不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String issuingOrganizations;

    /**
     * 发动机号
     */
    @NotBlank(message = "发动机号不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String engineNo;

    /**
     * 经营许可证号
     */
    @NotBlank(message = "经营许可证号不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String businessCertificate;

    /**
     * 许可证截止时间，格式yyyy-MM-dd
     */
    @NotBlank(message = "许可证截止时间不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String businessCertificateExpireTime;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "创建时间不能为空", groups = {XhlTruckDTO.AddInfo.class})
    private String createTime;

}
