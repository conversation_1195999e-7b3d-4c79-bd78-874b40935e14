package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**
 * 新增项目请求类
 *
 * <AUTHOR>
 * @since 2024-7-19 15:45:48
 */
@Data
public class CreateProjectRequest {

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 建单费支付人
     */
    private String createAmountUser = "SHIPPER";

    /**
     * 司机运费支付人
     */
    private String driverCarriageUser = "SHIPPER";

    /**
     * 凭证要求
     */
    private String certificateType = "RECEIPT";


    /**
     * 货源类型
     */
    private String goodsSource = "ORDINARY";


    /**
     * 项目简称
     */
    private String projectSimpleName;

    private String userCode;
}