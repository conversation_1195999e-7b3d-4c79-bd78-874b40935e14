package com.teyuntong.outer.export.service.client.common.old.enums;

import com.teyuntong.outer.export.service.client.common.old.bean.ResponseCode;
import lombok.Getter;

/**
 * 后期可考虑code 是否可以改为字符串
 */
public enum ResponseEnum {
    /** 成功 **/

    success(200, "请求成功"),
    sys_error(500, "系统繁忙，请稍后重试"),
    not_found(404, "没有找到资源"),
    sign_err_1002(1002, "无效签名。"),
    err_1004(1004, "其他客户端登录"),

    request_error(403, "参数错误"),

    timeout(601, "请求超时"),
    api_error(6001, "api接口错误"),

    illegal_req(9001, "非法请求"),

    sys_busy(5002, "系统繁忙"),
    fallback(5009, "fallback"),
    service_error(5010, "服务异常"),

    redis_timeout(5011, "redis超时"),

    err_5502(5502, "上传格式不持支"),

    ;

    @Getter
    private Integer code;
    @Getter
    private String msg;

    ResponseEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public ResponseCode info(){
        ResponseCode respCode = new ResponseCode(this.code, this.msg);
        return respCode;
    }

    public ResponseCode info(String reqMsg){

        ResponseCode respCode = new ResponseCode(this.code, reqMsg);

        return respCode;
    }

    public static void main(String[] args) {

        String respCode = "no_found1";

        ResponseEnum codeEnum = ResponseEnum.success;

        try {
            codeEnum = ResponseEnum.valueOf(respCode);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }

        System.out.println(codeEnum);

    }

}
