package com.teyuntong.outer.export.service.client.cticloud.task.service;

import com.teyuntong.outer.export.service.client.cticloud.task.vo.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;

/**
 * cticloud task相关
 *
 * <AUTHOR>
 * @since 2024/07/17 13:13
 */
public interface TaskRpcService {

    /**
     * 获取任务列表接口
     *
     * @param name      可选	任务名称	需进行UTF-8格式的URLEncode编码
     * @param type      可选	任务类型	1.转呼叫组 2.转IVR
     * @param status    可选	任务状态	0初始 1运行中 2暂停 3结束
     * @param autoStart 可选	是否自动开始	0.不自动 1.自动
     * @param autoStop  可选	是否自动结束	0.不自动 1.自动
     * @param timeType  可选	时间过滤条件	1.任务启动时间 2.任务结束时间 3.任务创建时间
     * @param startTime 可选	起始时间点	取值说明："2019-10-11 00:00:00"
     * @param endTime   可选	终止时间点	取值说明："2019-10-11 23:59:59"
     * @return result
     */
    @GetMapping("/cticloud/task/query")
    CticloudResp<TaskQueryResult> queryTask(@RequestParam(value = "name", required = false) String name,
                                                           @RequestParam(value = "type", required = false) Integer type,
                                                           @RequestParam(value = "status", required = false) Integer status,
                                                           @RequestParam(value = "autoStart", required = false) Integer autoStart,
                                                           @RequestParam(value = "autoStop", required = false) Integer autoStop,
                                                           @RequestParam(value = "timeType", required = false) Integer timeType,
                                                           @RequestParam(value = "startTime", required = false) String startTime,
                                                           @RequestParam(value = "endTime", required = false) String endTime) throws IOException;


    /**
     * 任务号码导入接口
     *
     * @return result
     */
    @PostMapping("/cticloud/task/importTaskTel")
    CticloudResp<ImportTaskTelResponse> importTaskTel(@RequestBody ImportTaskTelRequest importTaskTelRequest) throws IOException;

    /**
     * 自动外呼
     *
     * @param autoCallTaskRequest
     * @return
     * @throws IOException
     * @throws InterruptedException
     */
    @PostMapping("/cticloud/task/autoCallTask")
    void autoCallTask(@RequestBody AutoCallTaskRequest autoCallTaskRequest) throws IOException, InterruptedException;

    /**
     * 清除已结束的专车派单自动外呼任务
     *
     * @return
     * @throws IOException
     */
    @PostMapping("/cticloud/task/autoDeleteAutoCallTask")
    void autoDelAutoCallTask() throws IOException;
}
