package com.teyuntong.outer.export.service.client.corporate.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/01/11 15:33
 */
@NoArgsConstructor
@Data
public class IcBasicInfoNormalResp {

    @JsonProperty("result")
    private ResultDTO result;
    @JsonProperty("reason")
    private String reason;
    @JsonProperty("error_code")
    private Long errorCode;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JsonProperty("historyNames")
        private String historyNames;
        @JsonProperty("cancelDate")
        private Object cancelDate;
        @JsonProperty("regStatus")
        private String regStatus;
        @JsonProperty("regCapital")
        private String regCapital;
        @JsonProperty("city")
        private String city;
        @JsonProperty("staffNumRange")
        private String staffNumRange;
        @JsonProperty("bondNum")
        private String bondNum;
        @JsonProperty("historyNameList")
        private List<String> historyNameList;
        @JsonProperty("industry")
        private String industry;
        @JsonProperty("bondName")
        private String bondName;
        @JsonProperty("revokeDate")
        private Object revokeDate;
        @JsonProperty("type")
        private Long type;
        @JsonProperty("updateTimes")
        private Long updateTimes;
        @JsonProperty("legalPersonName")
        private String legalPersonName;
        @JsonProperty("revokeReason")
        private String revokeReason;
        @JsonProperty("compForm")
        private Object compForm;
        @JsonProperty("regNumber")
        private String regNumber;
        @JsonProperty("creditCode")
        private String creditCode;
        @JsonProperty("property3")
        private String property3;
        @JsonProperty("usedBondName")
        private String usedBondName;
        @JsonProperty("approvedTime")
        private Long approvedTime;
        @JsonProperty("fromTime")
        private Long fromTime;
        @JsonProperty("socialStaffNum")
        private Long socialStaffNum;
        @JsonProperty("actualCapitalCurrency")
        private String actualCapitalCurrency;
        @JsonProperty("alias")
        private String alias;
        @JsonProperty("companyOrgType")
        private String companyOrgType;
        @JsonProperty("id")
        private Long id;
        @JsonProperty("cancelReason")
        private String cancelReason;
        @JsonProperty("orgNumber")
        private String orgNumber;
        @JsonProperty("toTime")
        private Object toTime;
        @JsonProperty("actualCapital")
        private String actualCapital;
        @JsonProperty("estiblishTime")
        private Long estiblishTime;
        @JsonProperty("regInstitute")
        private String regInstitute;
        @JsonProperty("businessScope")
        private String businessScope;
        @JsonProperty("taxNumber")
        private String taxNumber;
        @JsonProperty("regLocation")
        private String regLocation;
        @JsonProperty("regCapitalCurrency")
        private String regCapitalCurrency;
        @JsonProperty("tags")
        private String tags;
        @JsonProperty("district")
        private String district;
        @JsonProperty("bondType")
        private String bondType;
        @JsonProperty("name")
        private String name;
        @JsonProperty("percentileScore")
        private Long percentileScore;
        @JsonProperty("industryAll")
        private IndustryAllDTO industryAll;
        @JsonProperty("isMicroEnt")
        private Long isMicroEnt;
        @JsonProperty("base")
        private String base;

        @NoArgsConstructor
        @Data
        public static class IndustryAllDTO {
            @JsonProperty("categoryMiddle")
            private String categoryMiddle;
            @JsonProperty("categoryBig")
            private String categoryBig;
            @JsonProperty("category")
            private String category;
            @JsonProperty("categorySmall")
            private String categorySmall;
        }
    }
}
