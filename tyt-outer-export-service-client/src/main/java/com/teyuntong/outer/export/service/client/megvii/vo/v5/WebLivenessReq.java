package com.teyuntong.outer.export.service.client.megvii.vo.v5;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/01/11 14:08
 */
@Data
public class WebLivenessReq {

    /**
     * “默认为空”。客户业务流水号，建议设置为您的业务相关的流水串号并且唯一，
     * 会在return时原封不动的返回给您的服务器，以帮助您确认对应业务的归属。此字段不超过128字节
     */
    private String bizNo;

    /**
     * 验证数据来源
     * 0：通过SDK 5.0.0以上进行活体验证
     * 1：自传照片进行活体验证
     */
    @NotBlank(message = "dataType 不能为空")
    private String dataType;

    /**
     * get_biz_token接口获取的biz_token；用以标识本次核验数据对象
     */
    @NotBlank(message = "bizToken 不能为空")
    private String bizToken;

    /**
     * 是否开启传输数据加密，详细说明见：加密说明
     * 0：不开启
     * 1：SM2
     * 2：RSA
     */
    private String encryptionType;

}
