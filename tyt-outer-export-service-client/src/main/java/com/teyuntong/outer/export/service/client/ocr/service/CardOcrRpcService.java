package com.teyuntong.outer.export.service.client.ocr.service;

import com.teyuntong.outer.export.service.client.ocr.dto.OcrRpcDTO;
import com.teyuntong.outer.export.service.client.ocr.vo.baidu.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 百度OCR RPC接口类
 *
 * <AUTHOR>
 * @since 2024-4-2 15:04:08
 */
public interface CardOcrRpcService {

    /**
     * 身份证-头像面
     */
    @PostMapping("/cardOcr/idCardFrontOcr")
    OcrIdCardFrontVo idCardFrontOcr(@RequestBody OcrRpcDTO ocrRpcDTO);

    /**
     * 身份证-国徽面
     */
    @PostMapping("/cardOcr/idCardBackOcr")
    OcrIdCardBackVo idCardBackOcr(@RequestBody OcrRpcDTO ocrRpcDTO);

    /**
     * 驾驶证-正页
     */
    @GetMapping("/cardOcr/driverLicenseFrontOcr")
    DriverLicenseFrontVo driverLicenseFrontOcr(@RequestParam("url") String url);

    /**
     * 驾驶证-副页
     */
    @GetMapping("/cardOcr/driverLicenseBackOcr")
    DriverLicenseBackVo driverLicenseBackOcr(@RequestParam("url") String url);

    /**
     * 行驶证-主页
     */
    @GetMapping("/cardOcr/vehicleLicenseMainOcr")
    VehicleLicenseFrontVo vehicleLicenseMainOcr(@RequestParam("url") String url);

    /**
     * 行驶证-副页
     */
    @GetMapping("/cardOcr/vehicleLicenseBackOcr")
    VehicleLicenseBackVo vehicleLicenseBackOcr(@RequestParam("url") String url);

    /**
     * 营业执照
     */
    @GetMapping("/cardOcr/businessLicenseOcr")
    OcrBusinessLicenseVo businessLicenseOcr(@RequestParam("url") String url);

    /**
     * 道路运输证
     */
    @GetMapping("/cardOcr/roadTransportOcr")
    RoadTransportVo roadTransportOcr(@RequestParam("url") String url);

}
