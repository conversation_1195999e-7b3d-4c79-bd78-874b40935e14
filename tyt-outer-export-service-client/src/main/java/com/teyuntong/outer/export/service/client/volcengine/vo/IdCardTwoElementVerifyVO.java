package com.teyuntong.outer.export.service.client.volcengine.vo;

import com.volcengine.model.response.ElementVerifyResponseV2;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/01/10 17:02
 */
@Data
public class IdCardTwoElementVerifyVO {

    /**
     * 认证是否成功
     */
    private boolean success = false;

    /**
     * 原始请求返回信息
     * <p>
     * code	对应的message	    含义描述
     * 0	success	             请求正常。
     * 1001	invalid parameters 参数不合法。
     * 1002	"auth failed"	认证失败。
     * 1003	"no permission"	权限不足，服务未开通或已到期。
     * 1004	qps limit	QPS超过已购规格，限流。
     * 1005	insufficient balance	余额/余量不足
     * 1006	timeout	处理超时（建议出现该code值时重试）
     * 1007	invalid element	认证要素格式不合法，如：手机号码不存在
     * 1013	invalid encrypted data	加密数据错误或者数据加密秘钥错误
     * 1022	unsupported mobile number segment 	不支持核验的手机号段，如虚拟号、广电号段(只出现在要素存在手机号的情况下)
     * 2000	"internal error"	内部服务器错误。
     * <p>
     * Status	说明
     * 10000	查询失败
     * 10001	通过
     * 10002	不通过
     */
    private ElementVerifyResponseV2.ElementVerifyResultV2 originResponse;

}