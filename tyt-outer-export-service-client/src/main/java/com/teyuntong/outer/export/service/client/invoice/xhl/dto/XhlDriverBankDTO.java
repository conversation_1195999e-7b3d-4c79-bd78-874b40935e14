package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 司机绑卡实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlDriverBankDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * 驾驶员身份证号
     */
    @NotBlank(message = "驾驶员身份证号不能为空")
    private String idCardNo;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankNo;

}
