package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 运费申请
 *
 * <AUTHOR>
 * @since 2024/07/17 15:48
 */
@Data
public class ApplyFreightRequest  {


    /**
     * 批量申请运费信息集合
     */
    private List<ApplyFreightInfo> applyFreightInfoList;

    /**
     * 业务类型
     * (See: 运单业务类型枚举类
     * ADVANCE_TRANSPORT 1预付款运输
     * UPSTREAM_ADVANCE_MONEY 垫资运输)
     */
    private String businessType;
}
