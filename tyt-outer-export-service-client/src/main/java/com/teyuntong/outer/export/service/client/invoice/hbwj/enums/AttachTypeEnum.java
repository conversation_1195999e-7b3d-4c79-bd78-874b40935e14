package com.teyuntong.outer.export.service.client.invoice.hbwj.enums;

/**
* 运单凭证类型
 *
* <AUTHOR>
* @since 2025-4-27 13:26:37
*/
public enum AttachTypeEnum {
    DELIVERY_RECEIPT("DELIVERY_RECEIPT", "回单"),
    LOADING_WEIGHT_RECEIPT("LOADING_WEIGHT_RECEIPT", "装货磅单"),
    UNLOADING_WEIGHT_RECEIPT("UNLOADING_WEIGHT_RECEIPT", "卸货磅单"),
    LOADING_WEIGHT_IMG("LOADING_WEIGHT_IMG", "装货照片"),
    UNLOADING_WEIGHT_IMG("UNLOADING_WEIGHT_IMG", "卸货照片"),
    EXCEPTION_PHOTO("EXCEPTION_PHOTO", "异常图片");

    private String code;
    private String name;

    AttachTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
