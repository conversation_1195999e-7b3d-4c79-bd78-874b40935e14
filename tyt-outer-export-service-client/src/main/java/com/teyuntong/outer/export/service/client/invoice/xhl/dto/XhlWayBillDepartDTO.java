package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 翔和翎 运单发车实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlWayBillDepartDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 三方运单号
     */
    @NotBlank(message = "三方运单号不能为空")
    private String tpWaybillNo;

    /**
     * 发车时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "发车时间不能为空")
    private String departTime;

    /**
     * 发车重量，单位吨
     */
    @NotBlank(message = "发车重量不能为空")
    private String departWeight;

    /**
     * 发车体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String departVolume;

    /**
     * 发货磅单，多张逗号隔开
     */
    @NotBlank(message = "发货磅单不能为空")
    private String departPhoto;

    @NotBlank(message = "运距不能为空")
    private String distance;


}
