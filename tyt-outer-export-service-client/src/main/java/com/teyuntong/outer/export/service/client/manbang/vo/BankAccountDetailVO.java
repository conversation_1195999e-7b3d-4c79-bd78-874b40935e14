package com.teyuntong.outer.export.service.client.manbang.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 集团绑卡信息VO
 *
 * <AUTHOR>
 * @since 2024-7-31 14:19:14
 */
@Data
public class BankAccountDetailVO implements Serializable {
    private String bankcardId;
    private String memberId;
    private String bankCode;
    private String bankName;
    private String bankBranch;
    private String bankAccountNumMask;
    private String realName;
    private String province;
    private String city;
    private Integer cardType;
    private Integer cardAttribute;
    private Integer isVerified;
    private String alias;
    private String cardSkin;
    private String isSigning;
    private Integer status;
    private String extention;
    private String payAttribute;
    private String certType;
    private String certNum;
    private String mobileNum;
    private Date activateDate;
    private String channelCode;
    private String branchNo;
    private Integer accountRoleType;
    private String rawBankCardNo;
    private String rawMobileNum;
    private String rawCertNum;
    private boolean dragonCard;
    private Integer dragonCardStatus;
}
