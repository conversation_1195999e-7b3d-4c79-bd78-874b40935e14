package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 司机人车合照实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlDriverTruckDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * 司机身份证号
     */
    @NotBlank(message = "驾驶员身份证号不能为空")
    private String idCardNo;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String plateNumber;

    /**
     * 人车站照片地址，司机站在车头前面拍照，要求照片司机、车辆以及车牌号清晰显示
     */
    @NotBlank(message = "人车站照片地址不能为空")
    private String driverTruckUrl;

}
