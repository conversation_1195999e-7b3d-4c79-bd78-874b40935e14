package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
* 装货完成(三方)请求参数实体类
* <AUTHOR>
* @since 2024/7/18 14:59
*/
@Data
public class ArriveGoodsThreeRequest {


    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 用户会员代码
     */
    private String userCode;

    /**
     * 运单No
     */
    private String waybillCode;

    /**
     * 实际装货量
     */
    private BigDecimal realityLoadWeight;

    /**
     * 实际卸货量
     */
    private BigDecimal realityUnloadWeight;

    /**
     * 装货照片
     */
    private List<String> realityLoadImg;

    /**
     * 卸货照片
     */
    private List<String> unRealityLoadImg;


    /**
     * 装货磅单
     */
    private List<Integer> realityLoadReceipt;


    /**
     * 卸货磅单
     */
    private List<Integer> unRealityUnloadReceipt;

    /**
     * 实际卸货时间/运抵时间/定位结束时间
     */
    private String realityUnloadTime;

    /**
     * 回单照片
     */
    private List<String> returnDepositImg;


}