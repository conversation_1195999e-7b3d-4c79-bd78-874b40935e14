package com.teyuntong.outer.export.service.client.common.old.bean;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.teyuntong.outer.export.service.client.common.old.enums.ResponseEnum;
import com.teyuntong.outer.export.service.client.common.old.bean.ResponseCode;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020/05/19
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultMsgBean<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**** 正确结果 **/
    public static final int OK = 200;
    public static final String OK_MSG = "操作成功";


    /**** 错误结果 **/
    public static final int ERROR = 500;

    private int code = OK;
    private String msg;
    private T data;
    private Date timestamp = new Date();
    /** 为了扩展兼容以前的接口，额外增加的字段 **/
    private Object extraData;

    public ResultMsgBean() {
    }

    public ResultMsgBean(int code, String msg) {
        super();
        this.code = code;
        this.msg = msg;
    }

    public ResultMsgBean(int code, String msg, T data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    /**
     * @return the data
     */
    public T getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(T data) {
        this.data = data;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 返回成功
     * @param obj
     */
    public static<T> ResultMsgBean<T> successResponse(T obj){
        ResultMsgBean<T> resp = new ResultMsgBean<>();
        resp.setCode(ResponseEnum.success.getCode());
        resp.setMsg(ResponseEnum.success.getMsg());

        resp.setData(obj);
        return resp;
    }

    /**
     * 返回成功
     * @return
     */
    public static ResultMsgBean successResponse(){
        ResultMsgBean resp = new ResultMsgBean();

        resp.setCode(ResponseEnum.success.getCode());
        resp.setMsg(ResponseEnum.success.getMsg());

        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(int errorCode, String errorMsg) {
        ResultMsgBean resp = new ResultMsgBean(errorCode,errorMsg);;
        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(Throwable t) {

        TytException customExc = TytException.createException(t);
        Integer errorCode = customExc.getErrorCode();
        String errorMsg = customExc.getErrorMsg();

        ResultMsgBean resp = new ResultMsgBean(errorCode, errorMsg);;
        return resp;
    }

    /**
     * 返回失败
     */
    public static ResultMsgBean failResponse(ResponseCode responseCode) {
        TytException exce = TytException.createException(responseCode);
        return failResponse(exce);
    }

}
