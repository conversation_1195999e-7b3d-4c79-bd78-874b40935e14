package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 托运人实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlCarrierBankDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {XhlCarrierBankDTO.AddInfo.class, XhlCarrierBankDTO.UpdateInfo.class})
    private String companyName;

    /**
     * 卡号
     */
    @NotBlank(message = "卡号不能为空", groups = {XhlCarrierBankDTO.AddInfo.class, XhlCarrierBankDTO.UpdateInfo.class})
    private String bankNo;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空", groups = {XhlCarrierBankDTO.AddInfo.class})
    private String bankTypeName;

    /**
     * 开户行
     */
    @NotBlank(message = "开户行不能为空", groups = {XhlCarrierBankDTO.AddInfo.class})
    private String bankBranchName;

    /**
     * 行号
     */
    @NotBlank(message = "行号不能为空", groups = {XhlCarrierBankDTO.AddInfo.class})
    private String branchNo;

    /**
     * 虚户会员id
     */
    private String njUserId;

    /**
     * 虚户账号
     */
    private String njAcctNo;

    /**
     * 余额，单位元
     */
    private String balance;

    /**
     * 冻结金额，单位元
     */
    private String ctrlBalance;


}
