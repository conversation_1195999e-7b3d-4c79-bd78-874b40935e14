package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 新增合同请求类
 *
 * <AUTHOR>
 * @since 2024-7-19 15:45:48
 */
@Data
public class CreateContractRequest {

    /**
     * 合同类型
     */
    private String contractType = "ENTRUST_CONTRACT";

    /**
     * 合同甲方id
     */
    private Long contractFirstPartyId;

    /**
     * 合同甲方名称
     */
    private String contractFirstPartyName;

    /**
     * 合同乙方id
     */
    private Long contractSecondPartyId;

    /**
     * 合同乙方名称
     */
    private String contractSecondPartyName;


    /**
     * 合同费率
     */
    private BigDecimal contractRate;


    /**
     * 合同开始时间
     */
    private String contractStartDate;

    /**
     * 合同截止时间
     */
    private String contractEndDate;

    /**
     * 运输方式
     */
    private String transportType = "CAR";

    /**
     * 是否线上签署
     */
    private String isOnlineSign = "YES";

    private String userCode;
}