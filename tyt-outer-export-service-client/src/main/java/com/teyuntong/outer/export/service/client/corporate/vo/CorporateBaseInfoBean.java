package com.teyuntong.outer.export.service.client.corporate.vo;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/12/05 21:01
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorporateBaseInfoBean implements Serializable {
    private Long id;
    private String historyNames;
    private String regStatus;
    private Date cancelDate;
    private String regCapital;
    private String staffNumRange;
    private String industry;
    private String bondNum;
    private Integer type;
    private String bondName;
    private Date revokeDate;
    private String legalPersonName;
    private String revokeReason;
    private String regNumber;
    private String property3;
    private String creditCode;
    private String usedBondName;
    private Date fromTime;
    private Date approvedTime;
    private Integer socialStaffNum;
    private String alias;
    private String companyOrgType;
    private String actualCapitalCurrency;
    private String orgNumber;
    private String cancelReason;
    private Date toTime;
    private String email;
    private String actualCapital;
    private Date estiblishTime;
    private String regInstitute;
    private String taxNumber;
    private String businessScope;
    private String regLocation;
    private String regCapitalCurrency;
    private String tags;
    private String websiteList;
    private String phoneNumber;
    private String name;
    private String bondType;
    private Integer percentileScore;
    private String categoryMiddle;
    private String categoryBig;
    private String category;
    private String categorySmall;
    private Integer isMicroEnt;
    private String base;
    private String city;
    private String district;
    private static final long serialVersionUID = 1L;

    public CorporateBaseInfoBean() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHistoryNames() {
        return this.historyNames;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    public String getRegStatus() {
        return this.regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public Date getCancelDate() {
        return this.cancelDate;
    }

    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getRegCapital() {
        return this.regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public String getStaffNumRange() {
        return this.staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public String getIndustry() {
        return this.industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getBondNum() {
        return this.bondNum;
    }

    public void setBondNum(String bondNum) {
        this.bondNum = bondNum;
    }

    public Integer getType() {
        return this.type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getBondName() {
        return this.bondName;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    public Date getRevokeDate() {
        return this.revokeDate;
    }

    public void setRevokeDate(Date revokeDate) {
        this.revokeDate = revokeDate;
    }

    public String getLegalPersonName() {
        return this.legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getRevokeReason() {
        return this.revokeReason;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
    }

    public String getRegNumber() {
        return this.regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public String getProperty3() {
        return this.property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getCreditCode() {
        return this.creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getUsedBondName() {
        return this.usedBondName;
    }

    public void setUsedBondName(String usedBondName) {
        this.usedBondName = usedBondName;
    }

    public Date getFromTime() {
        return this.fromTime;
    }

    public void setFromTime(Date fromTime) {
        this.fromTime = fromTime;
    }

    public Date getApprovedTime() {
        return this.approvedTime;
    }

    public void setApprovedTime(Date approvedTime) {
        this.approvedTime = approvedTime;
    }

    public Integer getSocialStaffNum() {
        return this.socialStaffNum;
    }

    public void setSocialStaffNum(Integer socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public String getAlias() {
        return this.alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getCompanyOrgType() {
        return this.companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public String getActualCapitalCurrency() {
        return this.actualCapitalCurrency;
    }

    public void setActualCapitalCurrency(String actualCapitalCurrency) {
        this.actualCapitalCurrency = actualCapitalCurrency;
    }

    public String getOrgNumber() {
        return this.orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public String getCancelReason() {
        return this.cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Date getToTime() {
        return this.toTime;
    }

    public void setToTime(Date toTime) {
        this.toTime = toTime;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getActualCapital() {
        return this.actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public Date getEstiblishTime() {
        return this.estiblishTime;
    }

    public void setEstiblishTime(Date estiblishTime) {
        this.estiblishTime = estiblishTime;
    }

    public String getRegInstitute() {
        return this.regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public String getTaxNumber() {
        return this.taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getBusinessScope() {
        return this.businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getRegLocation() {
        return this.regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getRegCapitalCurrency() {
        return this.regCapitalCurrency;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public String getTags() {
        return this.tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getWebsiteList() {
        return this.websiteList;
    }

    public void setWebsiteList(String websiteList) {
        this.websiteList = websiteList;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBondType() {
        return this.bondType;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public Integer getPercentileScore() {
        return this.percentileScore;
    }

    public void setPercentileScore(Integer percentileScore) {
        this.percentileScore = percentileScore;
    }

    public String getCategoryMiddle() {
        return this.categoryMiddle;
    }

    public void setCategoryMiddle(String categoryMiddle) {
        this.categoryMiddle = categoryMiddle;
    }

    public String getCategoryBig() {
        return this.categoryBig;
    }

    public void setCategoryBig(String categoryBig) {
        this.categoryBig = categoryBig;
    }

    public String getCategory() {
        return this.category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategorySmall() {
        return this.categorySmall;
    }

    public void setCategorySmall(String categorySmall) {
        this.categorySmall = categorySmall;
    }

    public Integer getIsMicroEnt() {
        return this.isMicroEnt;
    }

    public void setIsMicroEnt(Integer isMicroEnt) {
        this.isMicroEnt = isMicroEnt;
    }

    public String getBase() {
        return this.base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return this.district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }
}