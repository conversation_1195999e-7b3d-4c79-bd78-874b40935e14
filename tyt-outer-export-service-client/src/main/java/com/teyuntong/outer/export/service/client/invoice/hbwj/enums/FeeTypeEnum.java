package com.teyuntong.outer.export.service.client.invoice.hbwj.enums;

/**
* description 费用类别
* <AUTHOR>
* @since 2024/7/17 15:54
*/
public enum FeeTypeEnum {
    PREPAYMENT("PREPAYMENT", "预付款"),
    FREIGHT("FREIGHT", "运费"),
    BOOKING_FEE("BOOKING_FEE", "订船费"),
    SAILING_FEE("SAILING_FEE", "出航费"),
    FUELCARDAMOUNT("FUELCARDAMOUNT", "油卡金额"),
    CREATEWAYBILL("CREATEWAYBILL", "建单费"),
    COMMISSION("COMMISSION", "佣金提现"),
    POSITION("POSITION", "定位费"),
    CUSTOMER("CUSTOMER", "保险费");

    private String code;
    private String desc;

    FeeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
