package com.teyuntong.outer.export.service.client.ocr.vo.baidu;

import lombok.Data;

import java.util.Date;

/**
 * 身份证正面照信息
 *
 * <AUTHOR>
 * @date 2023/4/26 16:10
 */
@Data
public class OcrIdCardFrontVo extends OcrDataVo {

    /**
     * 身份证识别状态
     * normal-识别正常
     * reversed_side-身份证正反面颠倒
     * non_idcard-上传的图片中不包含身份证
     * blurred-身份证模糊
     * other_type_card-其他类型证照
     * over_exposure-身份证关键字段反光或过曝
     * over_dark-身份证欠曝（亮度过低）
     * unknown-未知状态
     */
    private String imageStatus;

    //名称
    private String name;

    //性别
    private String gender;

    //民族
    private String nation;

    //生日
    private String birthdayText;
    private Date birthday;

    //住址
    private String liveAddress;

    //身份证号
    private String idNumber;

}
