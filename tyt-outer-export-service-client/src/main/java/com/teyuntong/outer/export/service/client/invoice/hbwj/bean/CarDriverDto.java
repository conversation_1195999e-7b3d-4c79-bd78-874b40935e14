package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public  class CarDriverDto {
    /**
     * 运力Id（如果填了运力id，则引用运力）
     */
    private Long vehicleId;

    /**
     * 驾驶人id（如果填了驾驶人id和车船号id，则引用驾驶人和车船号）
     */
    private Long driverId;

    /**
     * 车船号id（如果填了驾驶人id和车船号id，则引用驾驶人和车船号）
     */
    private Long travelId;

    /**
     * 驾驶人姓名（如果不引用运力或驾驶人车船号，驾驶人姓名必填）
     */
    private String driverName;

    /**
     * 驾驶人手机号（如果不引用运力或驾驶人车船号，驾驶人手机号必填）
     */
    private String driverPhone;

    /**
     * 车船号（如果不引用运力或驾驶人车船号，车船号必填）
     */
    private String travelNum;

    /**
     * 约定收款人id（如果填了约定收款人id，则引用约定收款人）
     */
    private Long payeeId;

    /**
     * 约定收款人账户名称（如果不引用约定收款人，约定收款人账户名称必填）
     */
    private String bankAccountName;

    /**
     * 约定收款人银行预留手机号（如果不引用约定收款人，约定收款人银行预留手机号必填）
     */
    private String bankPhone;

    /**
     * 约定收款人身份证号（如果不引用约定收款人，约定收款人身份证号必填）
     */
    private String idCardNo;

    /**
     * 约定收款人银行卡号（如果不引用约定收款人，约定收款人银行卡号必填）
     */
    private String bankCardNo;

    /**
     * 约定收款人开户行名称（如果不引用约定收款人，约定收款人开户行名称必填）
     */
    private String bankName;
}
