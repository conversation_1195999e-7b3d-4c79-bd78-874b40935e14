package com.teyuntong.outer.export.service.client.distance.service;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 距离服务
 *
 * <AUTHOR>
 * @since 2025-02-06 14:34
 */
public interface DistanceRpcService {

    /**
     * 计算两个坐标点之间的导航距离
     * <a href="https://lbs.qq.com/service/webService/webServiceGuide/route/directionTrucking#10">官方文档</a>
     *
     * @param dto
     * @return
     */
    @PostMapping("/distance/rpc/calculateDistance")
    DistanceRpcVO calculateDistance(@RequestBody @Validated DistanceRpcDTO dto);

    /**
     * 货车导航，返回三方导航JSON格式数据
     */
    @PostMapping("/rpc/navigation/truck")
    String navigationTruck(@RequestBody @Validated DistanceRpcDTO dto);
}
