package com.teyuntong.outer.export.service.client.distance.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;


/**
 * 距离计算参数
 *
 * <AUTHOR>
 * @since 2025-02-06 14:36
 */
@Data
public class DistanceRpcDTO {
    private Long srcMsgId;

    /**
     * 货车导航选用类型：1-高德 2-腾讯
     */
    private Integer truckNavigationType;
    /**
     * 是否只返回距离
     */
    private boolean onlyDistance = false;
    /**
     * 出发地纬度
     */
    @NotBlank(message = "出发地纬度不能为空")
    private String fromLatitude;
    /**
     * 出发地经度
     */
    @NotBlank(message = "出发地经度不能为空")
    private String fromLongitude;
    /**
     * 目的地纬度
     */
    @NotBlank(message = "目的地纬度不能为空")
    private String toLatitude;
    /**
     * 目的地经度
     */
    @NotBlank(message = "目的地经度不能为空")
    private String toLongitude;

    /**
     * 车辆长度，单位：米
     */
    private BigDecimal length;

    /**
     * 车辆高度，单位：米
     */
    private BigDecimal height;

    /**
     * 车辆宽度，单位：米
     */
    private BigDecimal width;

    /**
     * 车辆总重（核定载重+车辆自重），单位：吨
     */
    private BigDecimal weight;

    /**
     * 核定载重，单位：吨
     */
    private BigDecimal load;

    /**
     * 轴数，默认 2
     */
    private Integer axleCount;

    /**
     * 车牌号
     */
    private String plateNumber;
}
