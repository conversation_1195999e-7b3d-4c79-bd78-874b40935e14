package com.teyuntong.outer.export.service.client.zj.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/05 14:55
 */
@Data
public class ZJVehicleLocationVO {

    // 车牌号
    private String vno;

    // 状态码，业务查询状态：详见附录 A
    private int code;

    // 车辆最后定位经度，格式：1/600000.0（WGS84 坐标系）
    private int lon;

    // 车辆最后定位纬度，格式：1/600000.0（WGS84 坐标系）
    private int lat;

    // 车辆地理位置名称
    private String adr;

    // 车辆定位时间，时间戳
    private long utc;

    // 速度，单位 km/h
    private double spd;

    // 方向，正北，大于 0 且小于 90：东北，等于 90：正东，大于 90 且小于 180：东南，等于 180：正南，大于 180 且小于 270：西南，等于 270：正西，大于 270 且小于等于 359：西北，其他：未知
    private int drc;

    // 省
    private String province;

    // 市
    private String city;

    // 县
    private String country;

    // 里程，单位：km（车机上报点自带里程）
    private double mil;

    // 离线状态，true:离线，false:在线
    private Integer offlineState;

    // 离线时长，单位：分钟
    private int offlineTime;

    // 车辆已行驶距离，单位：km
    private double runDistance;

    // 剩余运距，单位：km
    private double remainDistance;

    // 预计到达时间，时间戳
    private long estimateArriveTime;


    private String originalLon;

    private String originalLat;

    private String formattedUtc;

}
