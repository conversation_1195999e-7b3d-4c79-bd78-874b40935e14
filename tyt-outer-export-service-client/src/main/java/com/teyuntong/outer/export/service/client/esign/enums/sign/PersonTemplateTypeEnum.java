//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.teyuntong.outer.export.service.client.esign.enums.sign;

import lombok.Getter;

public enum PersonTemplateTypeEnum {
    SQUARE("PersonalSealTemplate.SQUARE", "simsun"),
    RECTANGLE("PersonalSealTemplate.RECTANGLE", "simsun"),
    FZKC("PersonalSealTemplate.FZKC", "fzkc"),
    YYGXSF("PersonalSealTemplate.YYGXSF", "yygxsf"),
    HYLSF("PersonalSealTemplate.HYLSF", "hylsf"),
    BORDERLESS("PersonalSealTemplate.BORDERLESS", "simfang"),
    HWLS("PersonalSealTemplate.HWLS", "hwls"),
    YGYJFCS("PersonalSealTemplate.YGYJFCS", "ygyjfcs"),
    YGYMBXS("PersonalSealTemplate.YGYMBXS", "ygymbxs"),
    HWXKBORDER("PersonalSealTemplate.HWXKBORDER", "hwxk"),
    HWXK("PersonalSealTemplate.HWXK", "hwxk"),
    STRIP("PersonalSealTemplate.STRIP", "strip");

    @Getter
    private String template;

    @Getter
    private String fontName;

    private PersonTemplateTypeEnum(String template, String fontName) {
        this.template = template;
        this.fontName = fontName;
    }

}
