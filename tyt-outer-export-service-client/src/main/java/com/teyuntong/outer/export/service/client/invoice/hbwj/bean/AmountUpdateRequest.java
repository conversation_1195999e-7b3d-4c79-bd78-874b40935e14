package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AmountUpdateRequest implements Serializable {

    private Integer clientCode;

    /**
     * 三方 运单编号
     */
    private String  wayBillCode;

    /**
     * 调整后的运费
     */
    private Integer amount;

    /**
     * 增补扣减集
     */
    private List<UpdateAmountDto> amountUpdateList = new ArrayList<>();
    /**
     * 删除的id集
     */
    private List<String> delWaybillSettlementModifyIdList = new ArrayList<>();

    /**
     * 货主帮承运方调整后的运费
     */
    private Integer amountDown;

    /**
     *  货主帮删除id的集合
     */
    private List<String> delWaybillSettlementModifyIdListDown = new ArrayList<>();

    /**
     * 货主帮承运方增补扣减集
     */
    private List<AmountUpdateListDownDto> amountUpdateListDown;

    private String userCode;
}


