package com.teyuntong.outer.export.service.client.model;

import lombok.Data;
import org.apache.commons.codec.binary.Base64;

/**
 * 内容为base64格式的文件
 */
@Data
public class FileDataInfo {
    /**
     * base64内容
     */
    private String base64;

    /**
     * 文件名称
     */
    private String fileName;

    public FileDataInfo() {
    }
    public FileDataInfo(String base64, String fileName) {
        this.base64 = base64;
        this.fileName = fileName;
    }

    public FileDataInfo(byte[] dataBytes, String fileName) {
        String base64 = Base64.encodeBase64String(dataBytes);
        this.base64 = base64;
        this.fileName = fileName;
    }

}
