package com.teyuntong.outer.export.service.client.ocr.service;

import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportQCVerifyRpcVO;
import com.teyuntong.outer.export.service.client.ocr.vo.RoadTransportVerifyRpcVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 集团验真RPC接口类
 *
 * <AUTHOR>
 * @since 2024-5-31 16:27:36
 */
public interface VerifyRpcService {

    /**
     * 道路运输证验真
     *
     * @param vehicleNo 车牌号码
     * @param vehicleVIN 车辆识别代号
     * @return 验真结果
     */
    @GetMapping(path = "/verify/roadTransportVerify")
    RoadTransportVerifyRpcVO roadTransportVerify(@RequestParam("vehicleNo")String vehicleNo, @RequestParam("vehicleVIN")String vehicleVIN);

    /**
     * 道路运输道路运输从业资格证验真
     *
     * @param idCard 身份证号
     * @return 验真结果
     */
    @GetMapping(path = "/verify/roadTransportQCVerify")
    RoadTransportQCVerifyRpcVO roadTransportQCVerify(@RequestParam("idCard")String idCard);
}
