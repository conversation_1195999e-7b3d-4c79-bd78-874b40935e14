package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 运单凭证上传请求参数类
 *
 * <AUTHOR>
 * @since 2025-4-25 18:26:11
 */
@Data
public class ReceiptsUploadRequest implements Serializable {

    /**
     * 支付单号集合
     */
    private List<ReceiptsUploadFileInfoDto> uploadFileInfoDtoList;

    /**
     * 运单 code
     */
    private String waybillCode;
}
