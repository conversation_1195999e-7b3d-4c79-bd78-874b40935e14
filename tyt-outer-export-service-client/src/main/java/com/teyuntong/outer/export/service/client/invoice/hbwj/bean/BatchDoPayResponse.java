package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 批量支付响应类
 *
 * <AUTHOR>
 * @since 2024/07/23 19:57
 */
@Data
public class BatchDoPayResponse implements Serializable {

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 当前支付结果 1 成功 2 失败
     */
    private Integer payResult;

    /**
     * 支付单状态
     */
    private String paymentStatus;

    /**
     * 支付描述
     */
    private String payDesc;


}
