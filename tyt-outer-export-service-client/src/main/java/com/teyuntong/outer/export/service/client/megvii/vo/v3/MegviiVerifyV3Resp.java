package com.teyuntong.outer.export.service.client.megvii.vo.v3;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/01/11 14:08
 */
@NoArgsConstructor
@Data
public class MegviiVerifyV3Resp {

    @JsonProperty("time_used")
    private Integer timeUsed;
    @JsonProperty("verification")
    private VerificationDTO verification;
    @JsonProperty("attack_result")
    private AttackResultDTO attackResult;
    @JsonProperty("risk_info")
    private RiskInfoDTO riskInfo;
    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("images")
    private ImagesDTO images;
    @JsonProperty("biz_no")
    private String bizNo;
    @JsonProperty("result_message")
    private String resultMessage;
    @JsonProperty("result_code")
    private Integer resultCode;
    @JsonProperty("error")
    private String error;

    @NoArgsConstructor
    @Data
    public static class VerificationDTO {
        @JsonProperty("idcard")
        private IdcardDTO idcard;

        @NoArgsConstructor
        @Data
        public static class IdcardDTO {
            @JsonProperty("confidence")
            private Double confidence;
            @JsonProperty("thresholds")
            private ThresholdsDTO thresholds;

            @NoArgsConstructor
            @Data
            public static class ThresholdsDTO {
                @JsonProperty("1e-3")
                private Double $1e3;
                @JsonProperty("1e-5")
                private Double $1e5;
                @JsonProperty("1e-4")
                private Double $1e4;
                @JsonProperty("1e-6")
                private Double $1e6;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class AttackResultDTO {
        @JsonProperty("score")
        private Double score;
        @JsonProperty("threshold")
        private Double threshold;
        @JsonProperty("result")
        private Boolean result;
    }

    @NoArgsConstructor
    @Data
    public static class RiskInfoDTO {
        @JsonProperty("device_info_level")
        private String deviceInfoLevel;
        @JsonProperty("device_info_tags")
        private DeviceInfoTagsDTO deviceInfoTags;

        @NoArgsConstructor
        @Data
        public static class DeviceInfoTagsDTO {
            @JsonProperty("is_root")
            private Integer isRoot;
            @JsonProperty("is_hook")
            private Integer isHook;
            @JsonProperty("is_injection")
            private Integer isInjection;
            @JsonProperty("is_virtual_environment")
            private Integer isVirtualEnvironment;
        }
    }

    @NoArgsConstructor
    @Data
    public static class ImagesDTO {
        @JsonProperty("image_best")
        private String imageBest;
    }
}
