package com.teyuntong.outer.export.service.client.invoice.hbwj.service;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.ApplyFreightRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.ApplyFreightResp;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.AmountUpdateRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeResponse;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 开票-湖北我家开放接口服务
 *
 * <AUTHOR>
 * @since 2024/07/17 13:13
 */
public interface InvoiceWjOpenApiService {

    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return CreateWaybillThreeResponse 创建运单返回对象
     */
    @PostMapping(path = "/invoice/wj/addWaybillThree")
    WebResult<CreateWaybillThreeResponse> addWaybillThree(@RequestBody @Validated CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception;


    /**
     * 运费申请
     *
     * <AUTHOR>
     * @param applyFreightRequest 运费申请
     * @return ApplyFreightResp 运费申请响应
     */
    @PostMapping(path = "/invoice/wj/applyFreight")
    WebResult<List<ApplyFreightResp>> applyFreight(@RequestBody @Validated ApplyFreightRequest applyFreightRequest , @RequestParam("userCode") String userCode) throws Exception;
    /**
     * 修改运费
     *
     * <AUTHOR>
     * @param amountUpdateRequest 修改运费请求对象
     * @return boolean 创建运单返回对象
     */
    @PostMapping(path = "/invoice/wj/amountUpdate")
    boolean amountUpdate(@RequestBody @Validated AmountUpdateRequest amountUpdateRequest) throws Exception;

    /**
     * 修改运单
     *
     * <AUTHOR>
     * @param editWaybillThreeRequest 修改运费请求对象
     * @return boolean 创建运单返回对象
     */
    @PostMapping(path = "/invoice/wj/editWaybillThree")
    WebResult<EditWaybillThreeResponse> editWaybillThree(@RequestBody @Validated EditWaybillThreeRequest editWaybillThreeRequest) throws Exception;


    /**
     * 新增司机
     *
     * <AUTHOR>
     * @param report 新增司机对象
     * @return driverDetailResponse 返回司机信息
     */
    @PostMapping(path = "/invoice/wj/driver/create")
    DriverDetailResponse createDriver(@RequestBody @Validated HbwjDriverReport report) throws Exception;


    /**
     * 起运
     * <AUTHOR>
     * @param pickGoodsThreeRequest 起运请求对象
     */
    @PostMapping(path = "/invoice/wj/pickGoods")
    WebResult<Object> pickGoods(@RequestBody @Validated PickGoodsThreeRequest pickGoodsThreeRequest) throws Exception;



    /**
     * 起运
     * <AUTHOR>
     * @param pickGoodsThreeV3Request 起运请求对象
     */
    @PostMapping(path = "/invoice/wj/pickGoodsV3")
    WebResult<Object> pickGoodsV3(@RequestBody @Validated PickGoodsThreeV3Request pickGoodsThreeV3Request) throws Exception;

    /**
     * 抵运
     * <AUTHOR>
     * @param arriveGoodsThreeRequest 抵运请求对象
     */
    @PostMapping(path = "/invoice/wj/arriveGoods")
    WebResult<Object> arriveGoods(@RequestBody @Validated ArriveGoodsThreeRequest arriveGoodsThreeRequest) throws Exception;

    /**
     * 运单回调详情
     * <AUTHOR>
     * @param waybillThreeOffsetRequest 运单回调详情请求对象
     */
    @PostMapping(path = "/invoice/wj/getWaybillThreeOffset")
    WebResult<WaybillThreeOffsetResp> getWaybillThreeOffset(@RequestBody @Validated WaybillThreeOffsetRequest waybillThreeOffsetRequest) throws Exception;

    /** 取消订单
     * <AUTHOR>
     * @param cancelOrderThreeRequest 取消订单请求对象
     */
    @PostMapping(path = "/invoice/wj/cancelWaybill")
    WebResult<Object> cancelWaybill(@RequestBody @Validated CancelOrderThreeRequest cancelOrderThreeRequest) throws Exception;

    /**
     * 图片上传
     * <AUTHOR>
     * @param uploadFileUrl 图片路径url
     */
    @PostMapping(path = "/invoice/wj/uploadFile")
    FileUpLoadResp uploadFile(@RequestParam("uploadFileUrl")  String  uploadFileUrl) throws Exception;

    /**
     * 上传回单
     * @param uploadWeightReceiptsReqDto
     * @return
     * @throws Exception
     */
    @Deprecated
    @PostMapping(path = "/invoice/uploadWeightReceipts")
    String uploadWeightReceipts(@RequestBody UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 获取验支付证码
     * @param mobile 手机号
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/invoice/fund/getVerificationCode")
    String getFundVerificationCode(@RequestParam("mobile") String mobile, @RequestParam("userCode") String userCode) throws Exception;


    /**
     * 新增车辆
     *
     * <AUTHOR>
     * @param report 新增司机对象
     * @return driverDetailResponse 返回司机信息
     */
    @PostMapping(path = "/invoice/wj/driver/car")
    DriverDetailResponse createCar(@RequestBody @Validated HbwjCarReport report) throws Exception;

    /**
     *  生成合同
     *
     * <AUTHOR>
     * @param createProtocolRequest 请求对象
     * @return driverDetailResponse 返回司机信息
     */
    @PostMapping(path = "/invoice/wj/createProtocol")
    WebResult<CreateProtocolResponse> createProtocol(@RequestBody @Validated CreateProtocolRequest createProtocolRequest) throws Exception;

    /**
     * 创建企业认证信息
     */
    @PostMapping(path = "/company/create")
    CreateCompanyResponse createCompany(@RequestBody CreateCompanyRequest createCompanyRequest) throws Exception;

    /**
     * 新增合同
     */
    @PostMapping(path = "/contract/create")
    CreateContractResponse createContract(@RequestBody CreateContractRequest request) throws Exception;

    /**
     * 新增项目
     */
    @PostMapping(path = "/project/create")
    CreateProjectResponse createProject(@RequestBody CreateProjectRequest request) throws Exception;

    /**
     * 查询钱包信息
     */
    @PostMapping(path = "/fund/selectCustomerRechargeWalletList")
    QueryWalletResponse selectCustomerRechargeWalletList(@RequestBody QueryWalletRequest request) throws Exception;

    /**
     * 设置支付密码
     */
    @PostMapping(path = "/fund/setPayPwd")
    WebResult<Object> setPayPwd(@RequestBody SavePayPwdRequest request) throws Exception;

    /**
     * 修改支付密码
     */
    @PostMapping(path = "/fund/changePayPwd")
    WebResult<Object> changePayPwd(@RequestBody ChangePayPwdRequest request) throws Exception;

    /**
     * 批量支付
     */
    @PostMapping(path = "/payment/batchDoPay")
    WebResult<List<BatchDoPayResponse>> batchDoPay(@RequestBody BatchDoPayRequest batchDoPayRequest , @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 发送更换手机验证码
     */
    @PostMapping(path = "/invoice/account/changePhoneCaptchaSend")
    WebResult<Object> changePhoneCaptchaSendForAccount(@RequestBody ChangePhoneCaptchaSendRequest changePhoneCaptchaSendRequest) throws Exception;

    /**
     * 修改手机号
     */
    @PostMapping(path = "/invoice/account/changePhone")
    WebResult<Object> changePhone(@RequestBody ChangePhoneRequest changePhoneRequest) throws Exception;

    /**
     * 检查更换手机号验证码
     */
    @PostMapping(path = "/invoice/account/changePhoneCaptchaCheck")
    WebResult<ChangePhoneCaptchaCheckResponse> changePhoneCaptchaCheck(@RequestBody ChangePhoneCaptchaCheckRequest changePhoneCaptchaCheckRequest) throws Exception;


    /**
     * 新增开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return WebResult<List<BatchDoPayResponse>>
     */
    @PostMapping(path = "/invoice/create")
    WebResult<Long> invoiceCreate(@RequestBody InvoiceRequest invoiceRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 修改开票信息
     *
     * @param invoiceRequest
     * @param userCode
     * @return WebResult<List<BatchDoPayResponse>>
     */
    @PostMapping(path = "/invoice/update")
    WebResult<Long> invoiceUpdate(@RequestBody InvoiceRequest invoiceRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 发票创建
     *
     * @param invoiceCreateRequest
     * @param userCode
     */
    @PostMapping(path = "/invoice/invoiceCreate")
    WebResult<Object> doInvoiceCreate(@RequestBody InvoiceCreateRequest invoiceCreateRequest, @RequestParam("userCode") String userCode) throws Exception;


    /**
     * 查询发票开票状态
     * @param invoiceInfoRequest 发票号码
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/invoice/queryInvoiceCallBackVo")
    WebResult<InvoiceResponse> queryInvoiceCallBackVo(@RequestBody InvoiceInfoRequest invoiceInfoRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 上传回单
     * @param uploadWeightReceiptsReqDto
     * @return
     * @throws Exception
     */
    @PostMapping(path = "/invoice/uploadWeightReceiptsNew")
    WebResult<String> uploadWeightReceiptsNew(@RequestBody UploadWeightReceiptsReqDto uploadWeightReceiptsReqDto, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 支付单审核结果查询
     * @param paymentNo
     * @param userCode
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/invoice/getPaymentDetailByNo")
    WebResult<PaymentOrderResp> getPaymentDetailByNo(@RequestParam("paymentNo")  String paymentNo, @RequestParam("userCode") String userCode) throws Exception;


    /**
     * 撤销运单的支付单
     *
     * @param cancelFreightRequest
     * @param userCode
     */
    @PostMapping(path = "/invoice/cancelFreight")
    WebResult<Object> cancelFreight(@RequestBody CancelFreightRequest cancelFreightRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 支付单作废
     * @param cancelFreightRequest
     * @param userCode
     * @return
     * @throws Exception
     */
    @PostMapping(path = "/invoice/paymentCancel")
    WebResult<Object> paymentCancel(@RequestBody PaymentCancelRequest cancelFreightRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 运单凭证上传(V3)
     */
    @PostMapping(path = "/invoice/receiptsUpload")
    WebResult<Object> receiptsUpload(@RequestBody ReceiptsUploadRequest receiptsUploadRequest, @RequestParam("userCode") String userCode) throws Exception;

    /**
     * 运单运抵(V3)
     */
    @PostMapping(path = "/invoice/arriveWaybill")
    WebResult<Object> arriveWaybill(@RequestBody ArriveWaybillRequest arriveWaybillRequest, @RequestParam("userCode") String userCode) throws Exception;



    /**
     * 重置支付密码 - 发送短信验证码 (V3)
     */
    @PostMapping(path = "/invoice/fund/sendmsg")
    WebResult<Object> fundSendMsg(@RequestParam("userCode") String userCode) throws Exception;


    /**
     * 重置支付密码
     * @param forgetPayPwdRequest
     * @param userCode
     * @return
     * @throws Exception
     */
    @PostMapping(path = "/invoice/fund/forgetpaypwd")
    WebResult<Object> forgetpaypwd(@RequestBody ForgetPayPwdRequest forgetPayPwdRequest, @RequestParam("userCode") String userCode) throws Exception;
}
