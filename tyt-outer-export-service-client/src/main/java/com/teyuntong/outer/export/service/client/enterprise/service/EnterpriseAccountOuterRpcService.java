package com.teyuntong.outer.export.service.client.enterprise.service;

import com.teyuntong.outer.export.service.client.enterprise.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 用户认证企业信息
 *
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
public interface EnterpriseAccountOuterRpcService {

    /**
     * 创建企业用户
     *
     * @param req 创建企业用户请求实体
     * @return CreateEnterpriseApiResp
     * <AUTHOR>
     */
    @PostMapping(path = "/enterprise/createEnterprise")
    CreateEnterpriseApiResp createEnterprise(@RequestBody @Validated EnterpriseAccountApiReq req);


    /**
     * 企业用户开专户
     *
     * @param req 企业用户开专户请求实体
     * @return Boolean
     * <AUTHOR>
     */
    @PostMapping(path = "/enterprise/enterpriseOpenAccount")
    Boolean enterpriseOpenAccount(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 企业网商开户
     *
     * @param req 企业网商开户请求实体
     * @return EnterpriseOpenNetAccountApiResp
     * <AUTHOR>
     */
    @PostMapping(path = "/enterprise/enterpriseOpenNetAccount")
    EnterpriseOpenNetAccountApiResp enterpriseOpenNetAccount(@RequestBody @Validated EnterpriseAccountApiReq req);


    /**
     * 查询网商开户信息
     *
     * @param req 查询网商开户信息请求参数
     * @return CreateEnterpriseApiResp
     * <AUTHOR>
     */
    @PostMapping(path = "/enterprise/queryEnterpriseNetAccountInfo")
    EnterpriseOpenNetAccountApiResp queryEnterpriseNetAccountInfo(@RequestBody @Validated EnterpriseAccountApiReq req);

    /**
     * 查看企业用户账户余额
     *
     * @param req 查看企业用户账户余额请求参数
     * @return QueryEnterpriseBalanceApiResp
     * <AUTHOR>
     */
    @GetMapping(path = "/enterprise/queryEnterpriseBalance")
    QueryEnterpriseBalanceApiResp queryEnterpriseBalance(@RequestBody @Validated QueryEnterpriseBalanceApiReq req);

    /**
     * 查看企业用户账单
     *
     * @param req 查看企业用户账单请求参数
     * @return QueryEnterpriseBalanceApiResp
     * <AUTHOR>
     */
    @PostMapping(path = "/enterprise/queryEnterpriseBill")
    ResultMsgBean queryEnterpriseBill(@RequestBody @Validated QueryEnterpriseBillApiReq req);

}
