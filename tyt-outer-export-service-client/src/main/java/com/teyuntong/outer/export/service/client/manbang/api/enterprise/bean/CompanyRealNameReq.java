package com.teyuntong.outer.export.service.client.manbang.api.enterprise.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CompanyRealNameReq {

    @NotNull
    private String identityNo;  //会员标识号

    @NotNull
    private String identityType;  //标识类型

    /**
     * 企业基本信息
     */
    @NotNull
    private String nameUnit;                                   //企业名称
    @NotNull
    private String licenseNo;                                  //营业执照号
    @NotNull
    private String typeExplicense;                             //营业执照有效期类型 0 非永久 / 1 永久 默认非永久
    private String expLicenseStart;                           //营业执照有效期开始
    private String expLicenseEnd;                             //营业执照有效期结束

    @NotNull
    private String name;                                        //法人真实姓名
    @NotNull
    private String idCard;                                      //身份证号码

    @NotNull
    private String typeExpidcard;                              //身份证有效期类型 0 非永久 / 1 永久  默认非永久
    private String expIdcardStart;                            //身份证有效期开始
    private String expIdcardEnd;                              //身份证有效期结束
    @NotNull
    private String mobBind;                                   //绑定手机号

    private String addrArea;                                  //所在地----县级

    private String addrDetail;                                //所在地----详细地址
    private String busiUser;                                  //经营范围

    /**
     * 资质
     */
    @NotNull
    private String licenseImage;                              //营业执照
    @NotNull
    private String legalIdcardFrontImage;                       //法人身份证正面
    @NotNull
    private String legalIdcardBackImage;                        //法人身份证背面
    private String clearingAccountPathImage;                    //开户许可证

    /**
     * 对公信息
     */
    @NotNull
    private String area;  //开户支行地区编码
    @NotNull
    private String bankAccountNum; //银行卡号码
    @NotNull
    private String realName; //户名
    @NotNull
    private String braBankName;  // 开户支行名称
    @NotNull
    private String branchCode; // 联行号
    @NotNull
    private String bankName; //银行名称
    @NotNull
    private String bankCode; //银行编码

    @NotNull
    private Boolean skipWalletAudit; //是否跳过钱包运营审核

}
