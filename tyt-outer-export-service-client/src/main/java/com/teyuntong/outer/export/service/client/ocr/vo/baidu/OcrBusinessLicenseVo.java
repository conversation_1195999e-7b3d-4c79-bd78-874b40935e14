package com.teyuntong.outer.export.service.client.ocr.vo.baidu;

import lombok.Data;

import java.util.Date;

/**
 * 企业营业执照
 *
 * <AUTHOR>
 * @date 2023/4/26 18:30
 */
@Data
public class OcrBusinessLicenseVo extends OcrDataVo {

    //经营范围
    private String businessScope;
    //组成形式
    private String componentForm;
    //法人
    private String legalPersonName;
    //证件编号
    private String licenseNumber;
    //注册资本
    private String registerCapital;
    //单位名称
    private String companyName;
    //有效期
    private String expiration;
    //社会信用代码
    private String creditCode;
    //实收资本
    private String paidCapital;
    //有效期起始日期
    private String validStartDateText;
    private Date validStartDate;

    //核准日期
    private String approvalDateText;
    private Date approvalDate;

    //成立日期
    private String establishDateText;
    private Date establishDate;

    //税务登记号
    private String taxNumber;
    //地址
    private String registerAddress;
    //登记机关
    private String registerGov;
    //类型
    private String companyType;

}
