package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 翔和翎 发票实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlInvoiceDTO {

    public interface AddInfo extends Default {

    }

    public interface DeleteInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {XhlInvoiceDTO.AddInfo.class, XhlInvoiceDTO.DeleteInfo.class})
    private String companyName;

    /**
     * 发票申请单号
     */
    @NotBlank(message = "发票申请单号不能为空", groups = {XhlInvoiceDTO.AddInfo.class, XhlInvoiceDTO.DeleteInfo.class})
    private String invoiceApplyNo;

    /**
     * 三方运单号，多个逗号隔开
     */
    @NotBlank(message = "三方运单号不能为空", groups = {XhlInvoiceDTO.AddInfo.class})
    private String tpWaybillNos;

    /**
     * 开票方公司名称
     */
    @NotBlank(message = "开票方公司名称不能为空", groups = {XhlInvoiceDTO.AddInfo.class})
    private String kpCustomerName;

    /**
     * 开票方统一社会代码
     */
    @NotBlank(message = "开票方统一社会代码不能为空", groups = {XhlInvoiceDTO.AddInfo.class})
    private String kpTaxNo;

    /**
     * 开票方公司地址
     */
    private String kpCustomerAddress;

    /**
     * 开票方电话
     */
    private String kpCustomerPhone;

    /**
     * 开票方开户银行
     */
    private String kpBankName;

    /**
     * 开票方银行账户
     */
    private String kpBankAccountNo;

    /**
     * 发票类型，1:数电票，2:纸质发票
     */
    private String type;

    /**
     * 开票清单，1:需要清单，2:不需要清单
     */
    private String hasList;

    /**
     * 开票单位（吨、方、件等）
     */
    private String unit;

    /**
     * 数量
     */
    private String num;

    /**
     * 规格类型
     */
    private String norms;

    /**
     * 发票清单地址
     */
    private String invoiceListUrl;

    /**
     * 发票备注
     */
    private String remark;


}
