package com.teyuntong.outer.export.service.client.distance.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 距离计算返回参数
 *
 * <AUTHOR>
 * @since 2025-02-06 14:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistanceRpcVO {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 返回信息
     */
    private String message;
    /**
     * 距离，单位米
     */
    private Integer distance;
}
