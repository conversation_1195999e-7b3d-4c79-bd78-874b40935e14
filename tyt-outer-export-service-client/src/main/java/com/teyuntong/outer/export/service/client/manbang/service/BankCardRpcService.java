package com.teyuntong.outer.export.service.client.manbang.service;

import com.teyuntong.outer.export.service.client.manbang.vo.BankAccountDetailVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 满帮银行卡相关 RPC接口类
 *
 * <AUTHOR>
 * @since 2024-7-31 13:10:30
 */
public interface BankCardRpcService {

    /**
     * 查询银行卡列表
     *
     * @param userId 用户ID
     */
    @GetMapping("/rpc/group/bankcard/getList")
    List<BankAccountDetailVO> getGroupBankCardList(@RequestParam("userId") Long userId);
}
