package com.teyuntong.outer.export.service.client.enums;

import lombok.Getter;

/**
 * 证件有效期类型.
 *
 * <AUTHOR>
 * @date 2023-1-28 10:59:20
 */
public enum CardExpireTypeEnum {
    //全局状态枚举
    TIME_RANGE(0, "非永久"),
    FOREVER(1, "永久"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String zhName;

    CardExpireTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

}