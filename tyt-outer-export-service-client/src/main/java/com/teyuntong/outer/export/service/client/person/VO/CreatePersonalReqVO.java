package com.teyuntong.outer.export.service.client.person.VO;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class CreatePersonalReqVO extends CommonVO {

    private String mobile;

    /*
        外部系统用户ID
     */
    private String uid;

    private String real_name;

    private String id_card_no;

    private String member_name;

    private String is_active;

    private String email;

    @NotBlank(message = "登录名不能为空")
    private String loginName;

    /**
     * 会员角色
     */
    private String memebr_role;

}
