package com.teyuntong.outer.export.service.client.invoice.xhl.service;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.invoice.xhl.dto.*;
import com.teyuntong.outer.export.service.client.invoice.xhl.vo.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 开票-翔和翎开放接口对接
 *
 * <AUTHOR>
 * @since 2025/01/13 13:13
 */
public interface InvoiceXhlOpenApiService {


    /**
     * 新增托运人
     *
     * @param xhlCarrierDTO 托运人信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/company/save")
    WebResult<XhlCarrierVO> saveCompany(@Validated(value = XhlCarrierDTO.AddInfo.class) @RequestBody XhlCarrierDTO xhlCarrierDTO) throws Exception;

    /**
     * 修改托运人
     *
     * @param xhlCarrierDTO 托运人信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/company/update")
    WebResult<XhlCarrierVO> updateCompany(@Validated(value = XhlCarrierDTO.UpdateInfo.class) @RequestBody XhlCarrierDTO xhlCarrierDTO) throws Exception;

    /**
     * 查询托运人
     *
     * @param companyName 公司名称
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/company/query")
    WebResult<XhlCarrierVO> queryCompany(@RequestParam("companyName") String companyName) throws Exception;

    /**
     * 托运人绑卡
     *
     * @param xhlCarrierBankDTO 托运人绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/company/bank/save")
    WebResult<XhlCarrierBankVO> saveCompanyBank(@Validated(value = XhlCarrierBankDTO.AddInfo.class) @RequestBody XhlCarrierBankDTO xhlCarrierBankDTO) throws Exception;

    /**
     * 托运人解绑银行卡
     *
     * @param xhlCarrierBankDTO 托运人解绑银行卡信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/company/bank/delete")
    WebResult<XhlCarrierBankVO> deleteCompanyBank(@Validated(value = XhlCarrierBankDTO.UpdateInfo.class) @RequestBody XhlCarrierBankDTO xhlCarrierBankDTO) throws Exception;

    /**
     * 查询托运人余额
     *
     * @param companyName 公司名称
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/company/balance")
    WebResult<XhlCarrierBankVO> queryCompanyBalance(@RequestParam("companyName") String companyName) throws Exception;


    /**
     * 新增驾驶员
     *
     * @param xhlDriverDTO 司机信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/save")
    WebResult<XhlDriverVO> saveDriver(@Validated(value = XhlDriverDTO.AddInfo.class) @RequestBody XhlDriverDTO xhlDriverDTO) throws Exception;


    /**
     * 修改驾驶员
     *
     * @param xhlDriverDTO 司机信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/update")
    WebResult<XhlDriverVO> updateDriver(@Validated(value = XhlDriverDTO.UpdateInfo.class) @RequestBody XhlDriverDTO xhlDriverDTO) throws Exception;

    /**
     * 查询驾驶员信息
     *
     * @param idCardNo 身份证号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/driver/query")
    WebResult<XhlDriverVO> queryDriver(@RequestParam("idCardNo") String idCardNo) throws Exception;


    /**
     * 驾驶员绑卡
     *
     * @param xhlDriverBankDTO 驾驶员绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/bank/save")
    WebResult<XhlDriverBankVO> saveDriverBank(@Validated @RequestBody XhlDriverBankDTO xhlDriverBankDTO) throws Exception;

    /**
     * 驾驶员解绑银行卡
     *
     * @param xhlDriverBankDTO 驾驶员绑卡信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/bank/delete")
    WebResult<XhlDriverBankVO> deleteDriverBank(@Validated @RequestBody XhlDriverBankDTO xhlDriverBankDTO) throws Exception;


    /**
     * 新增车辆
     *
     * @param xhlTruckDTO 新增车辆信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/truck/save")
    WebResult<XhlTruckVO> saveTruck(@Validated(value = XhlTruckDTO.AddInfo.class) @RequestBody XhlTruckDTO xhlTruckDTO) throws Exception;

    /**
     * 修改车辆
     *
     * @param xhlTruckDTO 修改车辆信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/truck/update")
    WebResult<XhlTruckVO> updateTruck(@Validated(value = XhlTruckDTO.UpdateInfo.class) @RequestBody XhlTruckDTO xhlTruckDTO) throws Exception;

    /**
     * 查询车辆信息
     *
     * @param plateNumber 车牌号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/truck/query")
    WebResult<XhlTruckVO> queryTruck(@RequestParam("plateNumber") String plateNumber) throws Exception;


    /**
     * 新增人车合照
     *
     * @param xhlDriverTruckDTO 新增人车合照信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/rel/driver_truck/save")
    WebResult<XhlDriverTruckVO> saveDriverTruck(@Validated @RequestBody XhlDriverTruckDTO xhlDriverTruckDTO) throws Exception;

    /**
     * 修改人车合照
     *
     * @param xhlDriverTruckDTO 修改人车合照信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/rel/driver_truck/update")
    WebResult<XhlDriverTruckVO> updateDriverTruck(@Validated @RequestBody XhlDriverTruckDTO xhlDriverTruckDTO) throws Exception;

    /**
     * 查询人车合照
     *
     * @param idCardNo    身份证号
     * @param plateNumber 车牌号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/rel/driver_truck/query")
    WebResult<XhlDriverTruckVO> queryDriverTruck(@RequestParam("idCardNo") String idCardNo, @RequestParam("plateNumber") String plateNumber) throws Exception;

    /**
     * 保存司机授权（委托代征）
     *
     * @param xhlDriverDTO 驾驶员信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/contract/driver/empower")
    WebResult<XhlDriverContractEmpowerVO> saveEmpowerDriver(@Validated(value = XhlDriverDTO.UpdateInfo.class) @RequestBody XhlDriverDTO xhlDriverDTO) throws Exception;

    /**
     * 查询司机授权（委托代征）
     *
     * @param idCardNo 身份证号
     * @return 返回操作结果的封装对象
     */
    @GetMapping("/invoice/xhl/contract/driver/query")
    WebResult<XhlDriverVO> queryEmpowerDriver(@RequestParam("idCardNo") String idCardNo) throws Exception;


    /**
     * 新增订单
     *
     * @param xhlOrderDTO 订单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/order/save")
    WebResult<XhlOrderVO> saveOrder(@Validated @RequestBody XhlOrderDTO xhlOrderDTO) throws Exception;

    /**
     * 修改订单
     *
     * @param xhlOrderDTO 订单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/order/update")
    WebResult<XhlOrderVO> updateOrder(@Validated @RequestBody XhlOrderDTO xhlOrderDTO) throws Exception;

    /**
     * 查询订单
     *
     * @param companyName 公司名称
     * @param tpOrderNo   第三方订单号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/order/query")
    WebResult<XhlOrderVO> queryOrder(@RequestParam("companyName") String companyName, @RequestParam("tpOrderNo") String tpOrderNo) throws Exception;

    /**
     * 派单
     *
     * @param xhlWayBillDTO 派单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/order/send")
    WebResult<XhlSendVO> sendOrder(@Validated(value = XhlWayBillDTO.AddInfo.class) @RequestBody XhlWayBillDTO xhlWayBillDTO) throws Exception;

    /**
     * 运单发车
     *
     * @param xhlOrderDepartDTO 运单发车信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/depart")
    WebResult<XhlWaybillVO> departWaybill(@Validated @RequestBody XhlWayBillDepartDTO xhlOrderDepartDTO) throws Exception;

    /**
     * 运单签收
     *
     * @param xhlOrderSignDTO 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/sign")
    WebResult<XhlWaybillVO> signWaybill(@Validated @RequestBody XhlWayBillSignDTO xhlOrderSignDTO) throws Exception;

    /**
     * 运单删除
     *
     * @param xhlWayBillDTO 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/delete")
    WebResult<XhlWaybillVO> deleteWaybill(@Validated(value = XhlWayBillDTO.DeleteInfo.class) @RequestBody XhlWayBillDTO xhlWayBillDTO) throws Exception;

    /**
     * 运单修改
     *
     * @param xhlWayBillDTO 运单信息实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/update")
    WebResult<XhlWaybillVO> updateWaybill(@Validated(value = XhlWayBillDTO.UpdateInfo.class) @RequestBody XhlWayBillDTO xhlWayBillDTO) throws Exception;

    /**
     * 运单查询
     *
     * @param companyName 公司名称
     * @param tpWaybillNo 第三方运单号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/waybill/query")
    WebResult<XhlWaybillVO> queryWaybill(@RequestParam("companyName") String companyName, @RequestParam("tpWaybillNo") String tpWaybillNo) throws Exception;

    /**
     * 创建订单和派单二合一接口
     *
     * @param xhlOrderAndWayBillDTO 订单和派单相关参数
     * @return 返回查询结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/save")
    WebResult<XhlWaybillVO> saveWaybill(@RequestBody XhlOrderAndWayBillDTO xhlOrderAndWayBillDTO) throws Exception;

    /**
     * 运费打款
     *
     * @param xhlFreightDTO 运费实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/payMoney")
    WebResult<XhlFreightVO> payMoney(@Validated(value = XhlFreightDTO.AddInfo.class) @RequestBody XhlFreightDTO xhlFreightDTO) throws Exception;


    /**
     * 修改运费
     *
     * @param xhlFreightDTO 运费实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/waybill/updateCharge")
    WebResult<XhlFreightVO> updateCharge(@Validated(value = XhlFreightDTO.UpdateInfo.class) @RequestBody XhlFreightDTO xhlFreightDTO) throws Exception;

    /**
     * 查询运费支付结果
     *
     * @param companyName 公司名称
     * @param tpWaybillNo 第三方运单号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/waybill/queryPay")
    WebResult<XhlFreightVO> queryPay(@RequestParam("companyName") String companyName, @RequestParam("tpWaybillNo") String tpWaybillNo) throws Exception;

    /**
     * 发票申请
     *
     * @param xhlInvoiceDTO 发票实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/invoice/apply")
    WebResult<XhlInvoiceVO> applyInvoice(@Validated(value = XhlInvoiceDTO.AddInfo.class)@RequestBody XhlInvoiceDTO xhlInvoiceDTO) throws Exception;


    /**
     * 发票取消
     *
     * @param xhlInvoiceDTO 发票实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/invoice/delete")
    WebResult<XhlInvoiceVO> deleteInvoice(@Validated(value = XhlInvoiceDTO.DeleteInfo.class)@RequestBody XhlInvoiceDTO xhlInvoiceDTO) throws Exception;

    /**
     * 查询发票申请
     *
     * @param invoiceApplyNo 发票申请单号
     * @return 返回查询结果的封装对象
     */
    @GetMapping("/invoice/xhl/invoice/query")
    WebResult<XhlInvoiceVO> queryInvoice(@RequestParam("invoiceApplyNo") String invoiceApplyNo) throws Exception;

    /**
     * 司机人脸认证
     *
     * @param xhlDriverDTO 司机实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/saveAppFaceAuth")
    WebResult<XhlDriverVO> saveAppFaceAuth(@Validated(value = XhlDriverDTO.FaceAuth.class)@RequestBody XhlDriverDTO xhlDriverDTO);


    /**
     * 司机人脸结果查询
     *
     * @param xhlDriverDTO 司机实体
     * @return 返回操作结果的封装对象
     */
    @PostMapping("/invoice/xhl/driver/queryAppFaceResult")
    WebResult<Boolean> queryAppFaceResult(@Validated(value = XhlDriverDTO.FaceAuth.class)@RequestBody XhlDriverDTO xhlDriverDTO);
}
