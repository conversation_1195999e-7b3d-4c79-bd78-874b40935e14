package com.teyuntong.outer.export.service.client.esign.vo.sign;

import com.teyuntong.outer.export.service.client.esign.enums.sign.OrganizeTemplateTypeEnum;
import com.teyuntong.outer.export.service.client.esign.enums.sign.SealColorEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class TemplateSealReq {

    /**
     * 签署账号id，通过创建企业签署账号接口获取
     */
    @NotNull
    private String accountId;
    /**
     * 模板类型，详见对象解释
     */
    @NotNull
    private OrganizeTemplateTypeEnum templateType;
    /**
     * 生成印章的颜色，详见对象解释
     * <p>
     * RED，红色
     * BLUE，蓝色
     * BLACK，黑色
     */
    @NotNull
    private SealColorEnum color;
    /**
     * 生成印章中的横向文内容；可设置0-8个字，企业名称超出25个字后，不支持设置横向文；一般是指的XX专用章，如合同专用章、财务专用章等
     */
    private String hText;
    /**
     * 生成印章中的下弦文内容；可设置0-20个字，企业企业名称超出25个字后，不支持设置下弦文；下弦文是指的贵司公章底部一串防伪数字，若没有该参数可不填
     */
    private String qText;

}
