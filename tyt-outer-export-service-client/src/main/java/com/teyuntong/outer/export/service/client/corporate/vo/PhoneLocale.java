package com.teyuntong.outer.export.service.client.corporate.vo;

/**
 * <AUTHOR>
 * @since 2024/12/05 21:24
 */
public class PhoneLocale {

    private static final long serialVersionUID = -1L;
    private String province;
    private String city;
    private String operation;
    private Integer status;
    private String version;
    private int code;
    private String message;

    public PhoneLocale() {
    }

    public String getProvince() {
        return this.province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOperation() {
        return this.operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String toString() {
        return "PhoneLocale{province='" + this.province + '\'' + ", city='" + this.city + '\'' + ", operation='" + this.operation + '\'' + ", status=" + this.status + ", version='" + this.version + '\'' + ", code=" + this.code + ", message='" + this.message + '\'' + '}';
    }
}
