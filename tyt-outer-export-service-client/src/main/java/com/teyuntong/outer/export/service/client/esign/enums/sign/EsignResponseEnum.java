package com.teyuntong.outer.export.service.client.esign.enums.sign;

import com.teyuntong.outer.export.service.client.model.ResponseCode;
import lombok.Getter;

/**
 * e签宝错误码.
 */
public enum EsignResponseEnum {

    /**
     * e签宝错误码对应.
     */
    OPT_ERROR(30500000, "操作出错,请联系服务人员处理, ID:XXXX!", "网络异常，请稍后重试！"),

    BALANCE_LOW(30503107, "很抱歉，订单余额不足", "网络异常，请稍后重试！"),

    INFO_NOT_MATCH(30501002, "运营商三要素信息核验失败，请检查信息正确性", "手机号错误或与实名信息不匹配！"),

    ;

    @Getter
    private final Integer code;

    @Getter
    private final String originMsg;

    @Getter
    private final String msg;

    EsignResponseEnum(Integer code, String originMsg, String msg){
        this.code = code;
        this.originMsg = originMsg;
        this.msg = msg;
    }

    public ResponseCode info(){

        ResponseCode responseCode = new ResponseCode();
        responseCode.setCode(this.code + "");
        responseCode.setMsg(this.msg);

        return responseCode;
    }

    public ResponseCode info(String reqMsg){

        ResponseCode responseCode = new ResponseCode();
        responseCode.setCode(this.code + "");
        responseCode.setMsg(reqMsg);

        return responseCode;

    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(Integer reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.getCode().equals(reqCode);
        return result;
    }

    /**
     * 根据code 获取对应的 code
     * @param reqCode reqCode
     * @return EsignResponseEnum
     */
    public static EsignResponseEnum getResponseEnum(Integer reqCode) {
        EsignResponseEnum resultEnum = null;
        for (EsignResponseEnum oneEnum : EsignResponseEnum.values()) {

            if(oneEnum.equalsCode(reqCode)){
                resultEnum = oneEnum;
                break;
            }
        }
        return resultEnum;
    }

}
