package com.teyuntong.outer.export.service.client.location.service;

import com.teyuntong.outer.export.service.client.location.dto.LastLocationRpcDTO;
import com.teyuntong.outer.export.service.client.location.dto.LocationTraceRpcDTO;
import com.teyuntong.outer.export.service.client.location.vo.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17 14:37
 */
public interface LocationRpcService {

    @PostMapping("/mb/rpc/getVehicleLastLocation")
    List<VehicleLocationRpcVO> getVehicleLastLocation(@RequestBody List<LastLocationRpcDTO> lastLocationRpcDTOList);



    @PostMapping("/mb/rpc/getVehicleSinoiovLastLocation")
    List<SinoiovVehicleLastLocationRpcVO> getVehicleSinoiovLastLocation(@RequestBody List<LastLocationRpcDTO> lastLocationDTOList);


    @PostMapping("/mb/rpc/getAppLastPosition")
    List<AppLastPositionRpcVO> getAppLastPosition(@RequestBody List<Long> userIds);


    @PostMapping("/mb/rpc/getLocationTrace")
    LocationTraceRpcVO getLocationTrace(@RequestBody LocationTraceRpcDTO locationTraceRpcDTO);

    @GetMapping("/mb/rpc/getLbsAuth")
    List<LbsAuthRpcVO> getLbsAuth(@RequestParam("vehicleNo") String vehicleNo);
}
