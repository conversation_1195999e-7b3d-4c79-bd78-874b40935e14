package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
* 装货完成(三方)请求参数实体类
* <AUTHOR>
* @since 2024/7/18 14:59
*/
@Data
public class PickGoodsThreeRequest {

    /**
     * 三方配置
     */
    private ThreeConfig threeConfig;

    /**
     * 用户会员代码
     */
    private String userCode;

    /**
     * 运单No
     */
    private String waybillCode;


    /**
     * 实际装货量
     */
    private BigDecimal realityLoadWeight;

    /**
     * 装货照片
     */
    private List<String> realityLoadImg;


    /**
     * 装货磅单
     */
    private List<String> realityLoadReceipt;

    /**
     * 实际起运时间
     */
    private String realityLoadTime;

}