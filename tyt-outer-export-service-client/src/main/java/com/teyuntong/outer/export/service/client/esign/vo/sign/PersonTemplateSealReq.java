package com.teyuntong.outer.export.service.client.esign.vo.sign;

import com.teyuntong.outer.export.service.client.esign.enums.sign.PersonTemplateTypeEnum;
import com.teyuntong.outer.export.service.client.esign.enums.sign.SealColorEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class PersonTemplateSealReq {

    /**
     * 签署账号id，通过创建企业签署账号接口获取
     */
    @NotNull
    private String accountId;

    /**
     * 模板类型，详见对象解释
     */
    @NotNull
    private PersonTemplateTypeEnum templateType;

    /**
     * 生成印章的颜色，详见对象解释
     * SealColorEnum
     * <p>
     * RED，红色
     * BLUE，蓝色
     * BLACK，黑色
     */
    @NotNull
    private SealColorEnum color;

}
