package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**
 * 新增合同响应类
 *
 * <AUTHOR>
 * @since 2024-7-19 15:45:48
 */
@Data
public class QueryWalletResponse {

    /**
     * 开户行名称
     */
    private String openBankName;

    /**
     * 联行号
     */
    private String interBankNo;

    /**
     * 账户类型名称 只有运费钱包
     */
    private String walletTypeName;

    /**
     * 充值渠道
     */
    private String channelName;

    /**
     * 钱包余额
     */
    private String walletBalance;

    /**
     * 专属汇款账号
     */
    private String qfChildAccount;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 钱包卡号
     */
    private String walletCode;

    /**
     * 钱包类型
     */
    private String walletTypeCode;
}