package com.teyuntong.outer.export.service.client.sensitivewords.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 敏感词检测结果VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SensitiveWordsVO {

    /**
     * 货物名称是否命中敏感词
     */
    private Boolean taskContentHitSensitiveWords;

    /**
     * 货名备注是否命中敏感词
     */
    private Boolean machineRemarkHitSensitiveWords;

    /**
     * 所有命中的敏感词集合
     */
    private Set<String> sensitiveWords;

}
