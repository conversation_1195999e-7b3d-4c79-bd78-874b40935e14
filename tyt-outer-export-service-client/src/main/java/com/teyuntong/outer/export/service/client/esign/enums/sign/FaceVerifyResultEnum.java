package com.teyuntong.outer.export.service.client.esign.enums.sign;

import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/3/27 16:19
 */
public enum FaceVerifyResultEnum {

    //刷脸结果状态
    ING("刷脸地址已申请"),
    SCAN("刷脸地址已使用，但尚未接收到刷脸结果"),
    SUCCESS("刷脸认证通过"),
    FAIL("刷脸认证失败"),
    ;

    @Getter
    private final String disc;

    FaceVerifyResultEnum(String disc) {
        this.disc = disc;
    }

    /**
     * 判断是否相等
     * @param reqCode
     * @return
     */
    public boolean equalsCode(String reqCode) {

        if(reqCode == null){
            return false;
        }
        boolean result = this.name().equals(reqCode);
        return result;
    }

}
