package com.teyuntong.outer.export.service.client.invoice.xhl.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * 翔和翎 运单实体类
 *
 * <AUTHOR>
 * @since 2025/01/13 17:56
 */
@Data
public class XhlWayBillDTO {

    public interface AddInfo extends Default {

    }

    public interface UpdateInfo extends Default {

    }

    public interface DeleteInfo extends Default {

    }

    /**
     * appId
     */
    private String appId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {XhlWayBillDTO.AddInfo.class, XhlWayBillDTO.UpdateInfo.class})
    private String companyName;

    /**
     * 三方运单号
     */
    @NotBlank(message = "三方运单号不能为空", groups = {XhlWayBillDTO.AddInfo.class, XhlWayBillDTO.UpdateInfo.class, XhlWayBillDTO.DeleteInfo.class})
    private String tpWaybillNo;

    /**
     * 三方订单号
     */
    @NotBlank(message = "三方订单号不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String tpOrderNo;

    /**
     * 托运费,可以传0
     */
    @NotBlank(message = "托运费不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String shippingCharge;

    /**
     * 承运费,可以传0
     */
    @NotBlank(message = "承运费不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String freight;

    /**
     * 司机身份证号
     */
    @NotBlank(message = "司机身份证号不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String driverIdCardNo;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String plateNumber;

    /**
     * 派单时间，yyyy-MM-dd HH:mm:ss格式
     */
    @NotBlank(message = "派单时间不能为空", groups = {XhlWayBillDTO.AddInfo.class})
    private String sendTime;


    /**
     * 发货人
     */
    private String shipperName;

    /**
     * 发货电话
     */
    private String shipperPhone;

    /**
     * 发货地址
     */
    private String shippingAddress;

    /**
     * 发货经度
     */
    private String startLng;

    /**
     * 发货纬度
     */
    private String startLat;

    /**
     * 发货地城市编码
     */
    private String startArea;

    /**
     * 收货单位
     */
    private String consigneeBranch;

    /**
     * 收货人
     */
    private String consigneeName;

    /**
     * 收货电话
     */
    private String consigneePhone;

    /**
     * 收货地址
     */
    private String consigneeAddress;

    /**
     * 收货经度
     */
    private String endLng;

    /**
     * 收货纬度
     */
    private String endLat;

    /**
     * 收货地城市编码
     */
    private String endArea;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 货物类型
     */
    private String goodsType;

    /**
     * 重量（吨）
     */
    private String weight;

    /**
     * 单价
     */
    private String unitPrice;

    /**
     * 体积（可空,若空，传0）
     */
    private String goodsVolume;

    /**
     * 数量（可空,若空，传0）
     */
    private String goodsNumber;

    /**
     * 估值方式
     */
    private String valuationWay;


    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String createTime;

    /**
     * 备注，非必填
     */
    private String remark;

    /**
     * 审核状态
     */
    private String checkFlag;

    /**
     * 审核原因
     */
    private String checkLog;


    /**
     * 发车时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String departTime;

    /**
     * 发车重量，单位吨
     */
    private String departWeight;

    /**
     * 发车体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String departVolume;

    /**
     * 发货磅单，多张逗号隔开
     */
    private String departPhoto;

    /**
     * 签收时间，yyyy-MM-dd HH:mm:ss格式
     */
    private String signTime;

    /**
     * 签收重量，单位吨
     */
    private String signWeight;

    /**
     * 结算重量，单位吨
     */
    private String settleWeight;

    /**
     * 签收体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String signVolume;

    /**
     * 结算体积，非必填，如果货源订单是按体积，字段必填，单位方
     */
    private String settleVolume;

    /**
     * 回单地址，多张逗号隔开
     */
    private String receiptPhoto;
}
