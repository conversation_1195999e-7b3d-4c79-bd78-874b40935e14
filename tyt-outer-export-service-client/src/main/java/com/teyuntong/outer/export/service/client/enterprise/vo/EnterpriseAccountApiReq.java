package com.teyuntong.outer.export.service.client.enterprise.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 【满帮】企业开户请求实体
 *
 * <AUTHOR>
 * @since 2024/4/2 15:45
 */
@Data
public class EnterpriseAccountApiReq {

    /**
     * 平台企业会员的登录名（字母、数字、@、.、_等）
     * 此处代指企业的统一社会信用码
     */
    @NotEmpty(message = "平台企业会员的登录名不能为空")
    private String loginName;

    /**
     * 平台企业会员的公司id(字母或数字)
     * 此处代指企业的统一社会信用码
     */
    @NotEmpty(message = "平台企业会员的公司id不能为空")
    private String uid;

    @NotEmpty(message = "企业名称不能为空")
    private String enterpriseName;

}
