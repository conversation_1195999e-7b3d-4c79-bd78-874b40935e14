package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

/**车辆信息
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/17 13:39
 */
@Data
public class HbwjCarReport {


   /**
    * 车牌号
    */
   private String plateNo;

    /**
     * 车辆类型，传字典对应表里面的key
     */
    private Integer carTypesOf;

    /**
     * 车型，传字典对应表里面的key
     */
    private Integer carType;

    /**
     * 车牌颜色
     * BLUE：蓝色
     * YELLOW：黄色
     * GRADIENT_GREEN：渐变绿色
     * CHARTREUSE：黄绿色
     */
    private String plateColor;

    /**
     * 车长，传字典对应表里面的key
     */
    private String carLength;

    /**
     * 核定载质量(kg)
     */
    private String carryingWeight;

    /**
     * 行驶证正面url
     */
    private String carDrivingFrontImg;

    /**
     * 行驶证反面url
     */
    private String carDrivingCopyImg;

    /**
     * 行驶证过期补充照片url
     */
    private String carDrivingCopyAddImg;

    /**
     * 行驶证有效期（开始）
     */
    private String carDrivingPeriodStart;

    /**
     * 行驶证有效期（结束）
     */
    private String carDrivingPeriodEnd;

    /**
     * 挂车行驶证主页url 如果车辆类型中含有“挂”字，该字段必填
     */
    private String trailerCarDrivingFrontImg;

    /**
     * 挂车行驶证副页url 如果车辆类型中含有“挂”字，该字段必填
     */
    private String trailerCarDrivingCopyImg;

    /**
     *车头正面照
     */
    private String headstockImg;

    /**
     * 挂车牌照号 如果车辆类型中含有“挂”字，该字段必填
     */
    private String trailerNo;

    /**
     * 道路运输证图片url
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg，该字段必填。
     */
    private String roadTransportPermImg;

    /**
     * 道路运输证有效期（开始）
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg
     * 有效期开始和截止时间至少传一个。
     */
    private String roadTransportPermPeriodStart;

    /**
     * 道路运输证有效期（结束）
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg
     * 有效期开始和截止时间至少传一个。
     */
    private String roadTransportPermPeriodEnd;

    /**
     * 道路运输证号
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg，该字段必填。
     */
    private String roadTransportPermNo;

    /**
     * 车辆所有人
     */
    private String owner;

    /**
     * 统一社会信用代码或证件号码(车辆所有人纳税人识别码)
     */
    private String carOwnerTaxpayerNo;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 挂靠单位
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg，该字段必填。
     */
    private String attachedUnits;

    /**
     * 道路运输证车牌号
     * 车长超过4.2米
     * 或者车辆类型含有“重型”“中型”
     * 或者车辆总质量超过4500kg，该字段必填。
     */
    private String roadTransportPlateNo;

    /**
     * 车辆总质量(kg)
     */
    private String carTotalWeight;

    /**
     * 车辆整备质量(kg)
     */
    private String carNetWeight;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 行驶证发证机关
     */
    private String issuingOrganization;

    /**
     * 行驶证发证日期
     */
    private String issuingDate;

    /**
     * 车辆外廓尺寸
     */
    private String carSize;

    /**
     * 挂车外廓尺寸
     */
    private String trailerSize;

    /**
     * 车辆注册日期
     */
    private String registerDate;

    /**
     * 车辆能源类型
     * NATURAL_GAS：天然气
     * DIESEL_OIL：柴油
     * GASOLINE：汽油
     * ELECTRICITY：电
     * OTHER：其他
     */
    private String carEnergyType;

    /**
     * 车辆识别代号
     */
    private String carVin;

    /**
     * 道路运输经营许可证图片url（车辆所有人为公司）
     */
    private String carOwnerBusiImg;

    /**
     * 道路运输经营许可证号
     */
    private String carOwnerBusiNo;

    /**
     * 车辆所有人有效期（车辆所有人经营许可证有效期）（开始）
     */
    private String carOwnerBusiNoPeriodStart;

    /**
     * 车辆所有人有效期（车辆所有人经营许可证有效期）（结束）
     */
    private String carOwnerBusiNoPeriodEnd;

    private String userCode;
}
