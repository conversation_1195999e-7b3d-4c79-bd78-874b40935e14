package com.teyuntong.outer.export.service.client.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/1/19 13:43
 */
public enum FileTypeEnum {
    /**
     * 文件后缀类型
     */
    BMP(".bmp", "image/*"),
    GIF(".gif", "image/*"),
    JPEG(".jpeg", "image/*"),
    JPG(".jpg", "image/*"),
    PNG(".png", "image/*"),

    XLS(".xls", "application/vnd.ms-excel"),
    XLSX(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),

    DOC(".doc", "application/msword"),
    DOCX(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),

    PPT(".ppt", "application/vnd.ms-powerpoint"),
    PPTX(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"),

    PDF(".pdf", "application/pdf"),
    MDB(".mdb", "application/octet-stream"),

    OTHER("", "application/octet-stream"),

    ;

    @Getter
    private String suffix;

    @Getter
    private String contentType;

    private FileTypeEnum(String suffix, String contentType) {
        this.suffix = suffix;
        this.contentType = contentType;
    }


    public static String getContentType(String fileName){
        FileTypeEnum resultEnum = FileTypeEnum.OTHER;

        int index = fileName.lastIndexOf(".");

        String suffixTmp = null;

        if(index >= 0){
            suffixTmp = fileName.substring(index).toLowerCase();
        }

        if(StringUtils.isNotBlank(suffixTmp)){

            for (FileTypeEnum oneEnum : FileTypeEnum.values()) {
                String oneSuffix = oneEnum.getSuffix();

                if(suffixTmp.equalsIgnoreCase(oneSuffix)){
                    resultEnum = oneEnum;
                    break;
                }
            }

        }

        String contentType = resultEnum.getContentType();
        return contentType;
    }

}
