package com.teyuntong.outer.export.service.client.ocr.vo.baidu;

import lombok.Data;

import java.util.Date;

/**
 * 驾驶证-主页
 *
 * <AUTHOR>
 * @date 2023/4/27 15:30
 */
@Data
public class DriverLicenseFrontVo extends OcrDataVo {

    //姓名
    private String name;

    //出生日期
    private String birthdayText;
    private Date birthday;

    //证号
    private String driverNumber;

    //住址
    private String liveAddress;

    //初次领证日期
    private String firstRegisterDateText;
    private Date firstRegisterDate;

    //国籍
    private String nationality;

    //准驾车型
    private String licenseType;

    //性别
    private String gender;

    //发证单位
    private String issueUnit;

    //有效期限
    private String validStartDateText;
    private Date validStartDate;

    //至
    private String validEndDateText;
    private Date validEndDate;

}
