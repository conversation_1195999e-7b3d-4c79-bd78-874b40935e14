package com.teyuntong.outer.export.service.client.person;

import com.teyuntong.outer.export.service.client.person.VO.*;
import com.wlqq.wallet.gateway.client.response.BaseResponse;
import com.wlqq.wallet.gateway.client.response.mgs.CreatePersonalResponse;
import com.wlqq.wallet.gateway.client.response.mgs.QueryMemberAccountResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

public interface PersonOpenAccountRpcService {

    /**
     *  个人开户
     *
     * @param
     * @return CreateEnterpriseApiResp
     * <AUTHOR>
     */
    @PostMapping(path = "/person/openAcct")
    CreatePersonalResponse applyInactiveAcct(@RequestBody @Validated CreatePersonalReqVO vo);
    /**
     *  注销实名
     *
     * @param
     * @return BaseResponse
     * <AUTHOR>
     */
    @PostMapping(path = "/person/cancel/verify")
    BaseResponse cancelVerify(@RequestBody @Validated CommonVO commonVO);
    /**
     *  满帮个人实名查询
     *
     * @param
     * @return BaseResponse
     * <AUTHOR>
     */
    @PostMapping(path = "/person/getUserRealInfo")
    UserRealResponse getUserRealInfo(@RequestBody @Validated QueryUserVerifyInfoReVo queryUserVerifyInfoReVo);
    /**
     *  获取满帮个人账号信息
     *
     * @param
     * @return QueryMemberAccountResponse
     * <AUTHOR>
     */
    @PostMapping(path = "/person/queryMember/accountInfo")
    QueryMemberAccountResponse queryMemberAccountInfo(@RequestBody @Valid QueryMemberAccountReqVO vo);
    /**
     *  个人保证金开户
     *
     * @param
     * @return BaseResponse
     * <AUTHOR>
     */
    @PostMapping(path = "/person/depositAccount/apply")
    BaseResponse depositAccountApply(@RequestBody @Valid CommonVO vo);
    @PostMapping(path = "/person/getToken")
    String getToken(@RequestBody @Valid MbTokenInfoReqVO mbTokenInfo);
}
