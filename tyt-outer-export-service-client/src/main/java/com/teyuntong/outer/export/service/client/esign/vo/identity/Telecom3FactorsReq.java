package com.teyuntong.outer.export.service.client.esign.vo.identity;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 14:37
 */
@Data
public class Telecom3FactorsReq {

    /**
     * 姓名
     */
    @NotNull
    private String name;
    /**
     * 身份证号
     */
    @NotNull
    private String idNo;
    /**
     * 手机号（仅限中国大陆11位手机号）
     */
    @NotNull
    private String mobileNo;
    /**
     * 自定义业务标识，将在异步通知及跳转时携带返回对接方
     */
    private String contextId;
    /**
     * 实名结束后异步通知地址,具体见 异步通知 章节说明
     */
    private String notifyUrl;
    /**
     * 指定运营商3要素信息比对结果详情
     * <p>
     * ADVANCED - 详情版（核验失败时会返回具体不匹配信息）
     * <p>
     * STANDARD - 普通版（默认值）
     * <p>
     * 【注】详情版：需要单独购买，具体购买方式请咨询e签宝工作人员；
     * <p>
     * 普通版：信息比对核验失败，不会返回具体的不匹配信息。
     */
    private String grade;
}
