package com.teyuntong.outer.export.service.client.gaode.service;

import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.outer.export.service.client.gaode.vo.GaoDeRegeoVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 高德地图
 *
 * <AUTHOR>
 * @since 2024/12/05 19:58
 */
public interface GaoDeMapRpcService {

    @GetMapping("/gaode/regeoAddress")
    GaoDeRegeoVo regeoAddress(@RequestParam("longitude") String longitude, @RequestParam("latitude") String latitude);

    /**
     * 高德货车导航
     */
    @PostMapping("/rpc/gaode/navigation/truck")
    String navigationTruck(@RequestBody @Validated DistanceRpcDTO dto);
}
