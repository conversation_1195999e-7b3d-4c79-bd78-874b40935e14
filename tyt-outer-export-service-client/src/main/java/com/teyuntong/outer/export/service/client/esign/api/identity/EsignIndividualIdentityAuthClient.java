package com.teyuntong.outer.export.service.client.esign.api.identity;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.constant.OuterExportConstant;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.EsignFaceResultResp;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.EsignFaceVerifyReq;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.EsignFaceVerifyResp;
import com.teyuntong.outer.export.service.client.esign.api.identity.bean.Telecom3FactorsApiReq;
import com.teyuntong.outer.export.service.client.esign.vo.identity.Telecom3FactorsVO;
import com.teyuntong.outer.export.service.client.esign.vo.identity.Telecom3FactorsVerifyReq;
import com.teyuntong.outer.export.service.client.fallback.CommonExceptionFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 易签宝-个人身份验证
 * <p>
 * 每个接口的说明都是超链接，点击可以跳转官方文档
 * 使用注解:
 * @EnableFeignClients(basePackages = {
 *         "com.teyuntong.outer.export.service.client.esign.api"
 * })
 * <AUTHOR>
 * @since 2024/01/10 16:45
 */
@Primary
@FeignClient(name = OuterExportConstant.SERVICE_PROVIDER + "/esignIdentityAuthIndividual",
        fallbackFactory = CommonExceptionFallbackFactory.class)
public interface EsignIndividualIdentityAuthClient {

    /**
     * 【手机号认证】运营商3要素核身
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/wry7rc">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors")
    WebResult<Telecom3FactorsVO> telecom3Factors(@RequestBody @Validated Telecom3FactorsApiReq req);

    /**
     * 【手机号认证】短信验证码校验
     * <p>
     * code = 200 就算成功
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/ddklrl">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors/verify")
    WebResult<?> telecom3FactorsVerify(@RequestBody @Validated Telecom3FactorsVerifyReq req);

    /**
     * 【手机号认证】重新发送验证码
     * <p>
     * code = 200 就算成功
     * <p>
     * <a href="https://open.esign.cn/doc/opendoc/identity_service/fxr4yh">官方文档链接</a>
     */
    @PostMapping("/telecom3Factors/authCode")
    WebResult<?> telecom3FactorsAuthCode(@RequestBody @Validated Telecom3FactorsVerifyReq req);

    /**
     * 发起个人刷脸实名认证
     * <p>
     * <a href="https://qianxiaoxia.yuque.com/opendoc/fei7gb/io1yrd">官方文档链接</a>
     */
    @PostMapping("/faceVerifyUrl")
    WebResult<EsignFaceVerifyResp> faceVerifyUrl(@RequestBody @Validated EsignFaceVerifyReq req);

    /**
     * 查询个人刷脸状态
     * <p>
     * <a href="https://qianxiaoxia.yuque.com/opendoc/fei7gb/qeig5n">官方文档链接</a>
     */
    @PostMapping("/getFaceResult")
    WebResult<EsignFaceResultResp> getFaceResult(@RequestParam("flowId") String flowId);

}
