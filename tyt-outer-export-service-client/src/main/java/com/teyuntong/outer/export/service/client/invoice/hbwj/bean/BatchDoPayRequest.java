package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.util.List;

/**
 * 运费申请
 *
 * <AUTHOR>
 * @since 2024/07/23 19:54
 */
@Data
public class BatchDoPayRequest {

    /**
     * 支付密码
     */
    private String password;

    /**
     * 支付单No数组
     */
    private List<String> paymentNos;

    /**
     * 支付手机号(开启手机号验证码后必填)
     */
    private String phone;

    /**
     * 手机号验证码(开启手机号验证码后必填)
     */
    private String vsgCode;
}
