package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CreateProtocolRequest implements Serializable {
    /**
     *  只传4    SINGLE_TRANSPORT_NOT_PAYEE(4, "货物运输合同-未约定收款人"),
     */
    private String yyzContractTypeEnum;

    /**
     * 三方订单号
     */
    private String waybillCode;

    private String userCode;
}
