package com.teyuntong.outer.export.service.client.enterprise.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
* 【满帮】企业账户查看余额请求实体
* <AUTHOR>
* @since 2024/4/2 15:45
*/
@Data
public class QueryEnterpriseBillApiReq {
    /**
     * 平台企业会员的公司id(字母或数字)
     */
    @NotEmpty(message = "平台企业会员的公司id不能为空")
    private String uid;

    /**
     * 开始时间
     */
    private String  startTime;

    /**
     * 结束时间
     */
    private String endTime;

}
