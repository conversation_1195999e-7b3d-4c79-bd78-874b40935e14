package com.teyuntong.outer.export.service.client.invoice.hbwj.bean;

import lombok.Data;

import java.io.Serializable;
@Data
public class CreateProtocolResponse implements Serializable {

    private String yyzContractTypeEnum;
    private String waybillCode;
    private String imageUrl;
    private String contractNo;
    /**
     * 签署地址
     */
    private String signHtmlUrl;
    /**
     * 是否签署
     */
    private Integer isFinishSign;
    private String  info;
}
