package com.teyuntong.outer.export.service.client.person.VO;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName MbTokenInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/2 10:48
 * @Version 1.0
 **/
@Data
public class MbTokenInfoReqVO {


    private Integer clientSign;

    private String clientId;

    private String ticket;

    @NotNull
    private Long userId;

    /**
     * 分配的商户ID
     */
    @NotNull
    private Long merchantId;
    /**
     * 版本号,固定值：10
     */
    @NotNull
    private Integer version;

    /**
     * 时间戳
     */
    @NotNull
    private String timestamp;

    /**
     * 设备ID
     */
    private String deviceId;

    private String mbUserId;


}
