package com.teyuntong.outer.export.service.client.esign.vo.sign;

import com.teyuntong.outer.export.service.client.esign.enums.sign.LegalAreaTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/02/05 11:02
 */
@Data
public class AddPersonAccountReq {
    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 姓名
     */
    @NotNull
    private String name;

    /**
     * 证件号码
     */
    @NotNull
    private String idNo;

    /**
     * 个人身份证件类型，支持类型如下：
     * ● MAINLAND
     * 大陆身份证，15位或者17位+1位校验位
     * ● HONGKONG
     * 香港居民往来内地通行证，字母H或者h开头，后接8位或者10位数字
     * ● MACAO
     * 澳门居民往来内地通行证，字母M或者m开头，后接8位或者10位数字
     * ● TAIWAN
     * 台湾居民来往大陆通行证，8位或者10位数字
     * ● FOREIGN
     * 外籍，不校验
     * ● PASSPORT
     * 护照，GgEePpSsDd字母中任一开头+1位数字或【.】+7位数字，总计9位
     * ● SOLDIER_IDNO
     * 军人身份证，15位或者17位+一位校验位
     * ● SOCIAL_SECURITY_CARD
     * 社会保障卡，15位或者17位+一位校验位
     * ● ARMED_POLICE_ID
     * 武装警察身份证件，不校验
     * ● RESIDENCE_BOOKLET
     * 户口簿，不校验
     * ● TEMPORARY_IDNO
     * 临时居民身份证，15位或者17位+一位校验位
     * ● FOREIGNER_PERMANENT_RESIDENCE_PERMIT
     * 外国人永久居留证，3位字母+12位数字
     * ● OTHER
     * 其他类型，不校验
     */
    @NotNull
    private LegalAreaTypeEnum personArea = LegalAreaTypeEnum.MAINLAND;

    /**
     * 否
     * 所属公司
     */
    private String organ;
    /**
     * 否
     * 职位
     */
    private String title;
    /**
     * 否
     * 常用地址
     */
    private String address;
    /**
     * 否
     * 国家
     */
    private String country;
    /**
     * 否
     * 省份
     */
    private String province;
    /**
     * 否
     * 城市
     */
    private String city;
    /**
     * 否
     * 部门
     */
    private String department;

}
