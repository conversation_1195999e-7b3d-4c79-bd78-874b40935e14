package com.teyuntong.outer.export.service.client.invoice.hbwj.service;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.CreateWaybillThreeResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 开票-湖北我家开放接口服务
 *
 * <AUTHOR>
 * @since 2024/07/17 13:13
 */
public interface ThirdInvoiceService {

    /**
     * 创建运单接口
     *
     * <AUTHOR>
     * @param createWaybillThreeRequest 创建运单请求对象
     * @return CreateWaybillThreeResponse 创建运单返回对象
     */
    @PostMapping(path = "/invoice/addWaybillThree")
    WebResult<CreateWaybillThreeResponse> addWaybillThree(@RequestBody @Validated CreateWaybillThreeRequest createWaybillThreeRequest) throws Exception;
}
